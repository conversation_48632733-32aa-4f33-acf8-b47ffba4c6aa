"use client";

import { useState, useCallback, useEffect, useRef, useMemo } from "react";
import { useToast } from "@/hooks/use-toast";
import { retryApiCall, withRetry } from "@/lib/utils/retry";
import { 
  User, 
  UserFormData, 
  PasswordChangeData, 
  UserListResponse, 
  ApiResponse,
  UserSearchParams 
} from "@/lib/types";

interface UseUsersState {
  users: User[];
  loading: boolean;
  error: string | null;
  totalUsers: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  searchQuery: string;
}

interface UseUsersActions {
  fetchUsers: (showLoading?: boolean) => Promise<void>;
  createUser: (data: UserFormData) => Promise<User>;
  updateUser: (id: number, data: UserFormData) => Promise<User>;
  deleteUser: (id: number) => Promise<void>;
  changePassword: (id: number, data: PasswordChangeData) => Promise<void>;
  setSearchQuery: (query: string) => void;
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
  refresh: () => void;
  clearError: () => void;
}

interface UseUsersReturn extends UseUsersState, UseUsersActions {}

export function useUsers(initialParams?: Partial<UserSearchParams>): UseUsersReturn {
  const { toast } = useToast();
  const abortControllerRef = useRef<AbortController | null>(null);

  // Utility function for consistent toast notifications - memoized to prevent infinite re-renders
  const showToast = useMemo(() => ({
    success: (title: string, description: string) => {
      toast({ title, description });
    },
    error: (title: string, description: string) => {
      toast({ variant: "destructive", title, description });
    },
    info: (title: string, description: string) => {
      toast({ title, description });
    },
  }), [toast]);

  const [state, setState] = useState<UseUsersState>({
    users: [],
    loading: true,
    error: null,
    totalUsers: 0,
    totalPages: 0,
    currentPage: initialParams?.page || 1,
    pageSize: initialParams?.limit || 10,
    searchQuery: initialParams?.search || "",
  });

  // Abort any ongoing requests when component unmounts
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Fetch users with proper error handling and loading states
  const fetchUsers = useCallback(async (showLoading = true) => {
    // Abort any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    if (showLoading) {
      setState(prev => ({ ...prev, loading: true, error: null }));
    }

    try {
      const params = new URLSearchParams({
        page: state.currentPage.toString(),
        limit: state.pageSize.toString(),
      });

      if (state.searchQuery.trim()) {
        params.append("search", state.searchQuery.trim());
      }

      const response = await retryApiCall(
        () => fetch(`/api/users?${params}`, {
          signal: abortControllerRef.current?.signal,
        }),
        {
          maxAttempts: 3,
          onRetry: (error, attempt) => {
            console.warn(`Retrying fetch users (attempt ${attempt}):`, error.message);
            toast({
              title: "Retrying...",
              description: `Attempting to reconnect (${attempt}/3)`
            });
          },
        }
      );

      const result: ApiResponse<UserListResponse> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to fetch users");
      }

      const { data: users, pagination } = result.data;

      setState(prev => ({
        ...prev,
        users,
        totalUsers: pagination.total,
        totalPages: pagination.totalPages,
        loading: false,
        error: null,
      }));
    } catch (error) {
      // Don't show error if request was aborted
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }

      console.error("Error fetching users:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch users";
      
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
      
      toast({
        variant: "destructive",
        title: "Failed to Load Users",
        description: "Unable to fetch user data after multiple attempts. Please check your connection and try again."
      });
    }
  }, [state.currentPage, state.pageSize, state.searchQuery, toast]);

  // Create user with optimistic updates
  const createUser = useCallback(async (data: UserFormData): Promise<User> => {
    try {
      const response = await retryApiCall(
        () => fetch("/api/users", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data),
        }),
        {
          maxAttempts: 2, // Less retries for create operations
          onRetry: (error, attempt) => {
            console.warn(`Retrying create user (attempt ${attempt}):`, error.message);
          },
        }
      );

      const result: ApiResponse<User> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to create user");
      }

      const newUser = result.data;

      // Optimistic update - add user to current list if it would be visible
      setState(prev => {
        const updatedUsers = [newUser, ...prev.users];
        return {
          ...prev,
          users: updatedUsers.slice(0, prev.pageSize), // Keep only current page size
          totalUsers: prev.totalUsers + 1,
          totalPages: Math.ceil((prev.totalUsers + 1) / prev.pageSize),
        };
      });

      toast({
        title: "User Created",
        description: `${newUser.firstName} ${newUser.lastName} has been successfully added to the system.`
      });

      return newUser;
    } catch (error) {
      console.error("Error creating user:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to create user";
      
      toast({
        variant: "destructive",
        title: "Failed to Create User",
        description: errorMessage
      });
      
      throw error;
    }
  }, [toast]);

  // Update user with optimistic updates
  const updateUser = useCallback(async (id: number, data: UserFormData): Promise<User> => {
    // Store original user for rollback
    const originalUser = state.users.find(u => u.id === id);
    
    if (originalUser) {
      // Optimistic update
      setState(prev => ({
        ...prev,
        users: prev.users.map(user => 
          user.id === id 
            ? { ...user, ...data, updatedAt: new Date() }
            : user
        ),
      }));
    }

    try {
      const response = await retryApiCall(
        () => fetch(`/api/users/${id}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data),
        }),
        {
          maxAttempts: 2,
          onRetry: (error, attempt) => {
            console.warn(`Retrying update user (attempt ${attempt}):`, error.message);
          },
        }
      );

      const result: ApiResponse<User> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to update user");
      }

      const updatedUser = result.data;

      // Update with actual server response
      setState(prev => ({
        ...prev,
        users: prev.users.map(user => 
          user.id === id ? updatedUser : user
        ),
      }));

      toast({
        title: "User Updated",
        description: `${updatedUser.firstName} ${updatedUser.lastName}'s information has been successfully updated.`
      });

      return updatedUser;
    } catch (error) {
      // Rollback optimistic update
      if (originalUser) {
        setState(prev => ({
          ...prev,
          users: prev.users.map(user => 
            user.id === id ? originalUser : user
          ),
        }));
      }

      console.error("Error updating user:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to update user";
      
      toast({
        variant: "destructive",
        title: "Failed to Update User",
        description: errorMessage
      });
      
      throw error;
    }
  }, [state.users, toast]);

  // Delete user with optimistic updates
  const deleteUser = useCallback(async (id: number): Promise<void> => {
    // Store original user for rollback
    const originalUser = state.users.find(u => u.id === id);
    const originalIndex = state.users.findIndex(u => u.id === id);
    
    if (originalUser) {
      // Optimistic update - remove user from list
      setState(prev => ({
        ...prev,
        users: prev.users.filter(user => user.id !== id),
        totalUsers: prev.totalUsers - 1,
        totalPages: Math.ceil((prev.totalUsers - 1) / prev.pageSize),
      }));
    }

    try {
      const response = await retryApiCall(
        () => fetch(`/api/users/${id}`, {
          method: "DELETE",
        }),
        {
          maxAttempts: 2,
          onRetry: (error, attempt) => {
            console.warn(`Retrying delete user (attempt ${attempt}):`, error.message);
          },
        }
      );

      toast({
        title: "User Deleted",
        description: `${originalUser?.firstName} ${originalUser?.lastName} has been permanently removed from the system.`
      });
    } catch (error) {
      // Rollback optimistic update
      if (originalUser && originalIndex !== -1) {
        setState(prev => {
          const newUsers = [...prev.users];
          newUsers.splice(originalIndex, 0, originalUser);
          return {
            ...prev,
            users: newUsers,
            totalUsers: prev.totalUsers + 1,
            totalPages: Math.ceil((prev.totalUsers + 1) / prev.pageSize),
          };
        });
      }

      console.error("Error deleting user:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to delete user";
      
      toast({
        variant: "destructive",
        title: "Failed to Delete User",
        description: errorMessage
      });
      
      throw error;
    }
  }, [state.users, toast]);

  // Change password
  const changePassword = useCallback(async (id: number, data: PasswordChangeData): Promise<void> => {
    try {
      const response = await retryApiCall(
        () => fetch(`/api/users/${id}/password`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ password: data.password }),
        }),
        {
          maxAttempts: 2,
          onRetry: (error, attempt) => {
            console.warn(`Retrying change password (attempt ${attempt}):`, error.message);
          },
        }
      );

      toast({
        title: "Password Changed",
        description: "The user's password has been successfully updated. They will need to use the new password for their next login."
      });
    } catch (error) {
      console.error("Error changing password:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to change password";
      
      toast({
        variant: "destructive",
        title: "Failed to Change Password",
        description: errorMessage
      });
      
      throw error;
    }
  }, [toast]);

  // Search and pagination handlers
  const setSearchQuery = useCallback((query: string) => {
    setState(prev => ({
      ...prev,
      searchQuery: query,
      currentPage: 1, // Reset to first page when searching
    }));

    // Show info toast for search
    if (query.trim() && query.length >= 3) {
      toast({
        title: "Searching Users",
        description: `Searching for users matching "${query.trim()}"`
      });
    }
  }, [toast]);

  const setCurrentPage = useCallback((page: number) => {
    setState(prev => ({ ...prev, currentPage: page }));
  }, []);

  const setPageSize = useCallback((size: number) => {
    setState(prev => ({
      ...prev,
      pageSize: size,
      currentPage: 1, // Reset to first page when changing page size
    }));
  }, []);

  // Utility functions
  const refresh = useCallback(() => {
    fetchUsers(true);
  }, [fetchUsers]);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Auto-fetch when dependencies change
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  return {
    // State
    users: state.users,
    loading: state.loading,
    error: state.error,
    totalUsers: state.totalUsers,
    totalPages: state.totalPages,
    currentPage: state.currentPage,
    pageSize: state.pageSize,
    searchQuery: state.searchQuery,
    
    // Actions
    fetchUsers,
    createUser,
    updateUser,
    deleteUser,
    changePassword,
    setSearchQuery,
    setCurrentPage,
    setPageSize,
    refresh,
    clearError,
  };
}