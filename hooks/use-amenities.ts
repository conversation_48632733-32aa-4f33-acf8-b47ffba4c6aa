"use client";

import { useState, useCallback, useEffect, useRef, useMemo } from "react";
import { useToast } from "@/hooks/use-toast";
import { retryApiCall, withRetry } from "@/lib/utils/retry";
import { 
  Amenity, 
  AmenityFormData, 
  AmenityListResponse, 
  ApiResponse,
  AmenitySearchParams 
} from "@/lib/types";

interface UseAmenitiesState {
  amenities: Amenity[];
  loading: boolean;
  error: string | null;
  totalAmenities: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  searchQuery: string;
}

interface UseAmenitiesActions {
  fetchAmenities: (showLoading?: boolean) => Promise<void>;
  createAmenity: (data: AmenityFormData) => Promise<Amenity>;
  updateAmenity: (id: number, data: AmenityFormData) => Promise<Amenity>;
  deleteAmenity: (id: number) => Promise<void>;
  setSearchQuery: (query: string) => void;
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
  refresh: () => void;
  clearError: () => void;
}

interface UseAmenitiesReturn extends UseAmenitiesState, UseAmenitiesActions {}

export function useAmenities(initialParams?: Partial<AmenitySearchParams>): UseAmenitiesReturn {
  const { toast } = useToast();
  const abortControllerRef = useRef<AbortController | null>(null);

  // Enhanced toast notification system with better user feedback
  const showToast = useMemo(() => ({
    success: (title: string, description: string, duration?: number) => {
      toast({ 
        title, 
        description,
        duration: duration || 4000,
        className: "border-green-200 bg-green-50 text-green-900"
      });
    },
    error: (title: string, description: string, action?: { label: string; onClick: () => void }) => {
      toast({ 
        variant: "destructive", 
        title, 
        description,
        duration: 6000, // Longer duration for errors
      });
      
      // Note: Custom action buttons in toast are not supported in this toast implementation
      // The action parameter is kept for future enhancement
    },
    info: (title: string, description: string, duration?: number) => {
      toast({ 
        title, 
        description,
        duration: duration || 3000,
        className: "border-blue-200 bg-blue-50 text-blue-900"
      });
    },
    warning: (title: string, description: string) => {
      toast({
        title,
        description,
        duration: 5000,
        className: "border-yellow-200 bg-yellow-50 text-yellow-900"
      });
    },
    loading: (title: string, description: string) => {
      return toast({
        title,
        description,
        duration: Infinity, // Keep loading toast until dismissed
        className: "border-gray-200 bg-gray-50 text-gray-900"
      });
    }
  }), [toast]);

  const [state, setState] = useState<UseAmenitiesState>({
    amenities: [],
    loading: true,
    error: null,
    totalAmenities: 0,
    totalPages: 0,
    currentPage: initialParams?.page || 1,
    pageSize: initialParams?.limit || 10,
    searchQuery: initialParams?.search || "",
  });

  // Abort any ongoing requests when component unmounts
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Fetch amenities with proper error handling and loading states
  const fetchAmenities = useCallback(async (showLoading = true) => {
    // Abort any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    if (showLoading) {
      setState(prev => ({ ...prev, loading: true, error: null }));
    }

    try {
      const params = new URLSearchParams({
        page: state.currentPage.toString(),
        limit: state.pageSize.toString(),
      });

      if (state.searchQuery.trim()) {
        params.append("search", state.searchQuery.trim());
      }

      const response = await retryApiCall(
        () => fetch(`/api/amenities?${params}`, {
          signal: abortControllerRef.current?.signal,
        }),
        {
          maxAttempts: 3,
          onRetry: (error, attempt) => {
            console.warn(`Retrying fetch amenities (attempt ${attempt}):`, error.message);
            
            const isNetworkError = error.message.includes('fetch') || error.message.includes('Network');
            const isServerError = error.message.includes('500') || error.message.includes('502');
            
            let retryMessage = `Attempting to reconnect (${attempt}/3)`;
            if (isNetworkError) {
              retryMessage = `Network issue detected. Retrying connection (${attempt}/3)`;
            } else if (isServerError) {
              retryMessage = `Server error detected. Retrying request (${attempt}/3)`;
            }
            
            showToast.warning("Connection Issue", retryMessage);
          },
        }
      );

      const result: ApiResponse<AmenityListResponse> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to fetch amenities");
      }

      const { data: amenities, pagination } = result.data;

      setState(prev => ({
        ...prev,
        amenities,
        totalAmenities: pagination.total,
        totalPages: pagination.totalPages,
        loading: false,
        error: null,
      }));
    } catch (error) {
      // Don't show error if request was aborted
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }

      console.error("Error fetching amenities:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch amenities";
      
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
      
      // Enhanced error categorization and user feedback
      const isNetworkError = errorMessage.includes('fetch') || errorMessage.includes('Network') || !navigator.onLine;
      const isServerError = errorMessage.includes('500') || errorMessage.includes('502') || errorMessage.includes('503');
      const isAuthError = errorMessage.includes('401') || errorMessage.includes('403');
      
      let title = "Failed to Load Amenities";
      let description = "Unable to fetch amenity data. Please try again.";
      
      if (isNetworkError) {
        title = "Network Connection Error";
        description = navigator.onLine 
          ? "Unable to connect to the server. Please check your internet connection and try again."
          : "You appear to be offline. Please check your internet connection.";
      } else if (isServerError) {
        title = "Server Error";
        description = "The server is temporarily unavailable. Our team has been notified. Please try again in a few moments.";
      } else if (isAuthError) {
        title = "Authentication Error";
        description = "Your session may have expired. Please refresh the page and try again.";
      }
      
      showToast.error(title, description, {
        label: "Retry",
        onClick: () => fetchAmenities(true)
      });
    }
  }, [state.currentPage, state.pageSize, state.searchQuery, toast]);

  // Create amenity with optimistic updates and enhanced feedback
  const createAmenity = useCallback(async (data: AmenityFormData): Promise<Amenity> => {
    // Show loading toast
    const loadingToast = showToast.loading(
      "Creating Amenity", 
      `Adding "${data.name}" to the system...`
    );

    try {
      const response = await retryApiCall(
        () => fetch("/api/amenities", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data),
        }),
        {
          maxAttempts: 2, // Less retries for create operations
          onRetry: (error, attempt) => {
            console.warn(`Retrying create amenity (attempt ${attempt}):`, error.message);
            showToast.warning(
              "Retrying Creation", 
              `Attempting to create "${data.name}" (${attempt}/2)`
            );
          },
        }
      );

      const result: ApiResponse<Amenity> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to create amenity");
      }

      const newAmenity = result.data;

      // Optimistic update - add amenity to current list if it would be visible
      setState(prev => {
        const updatedAmenities = [newAmenity, ...prev.amenities];
        return {
          ...prev,
          amenities: updatedAmenities.slice(0, prev.pageSize), // Keep only current page size
          totalAmenities: prev.totalAmenities + 1,
          totalPages: Math.ceil((prev.totalAmenities + 1) / prev.pageSize),
        };
      });

      // Dismiss loading toast
      loadingToast.dismiss();

      showToast.success(
        "Amenity Created Successfully", 
        `"${newAmenity.name}" has been added to the system and is now available for use.`,
        5000
      );

      return newAmenity;
    } catch (error) {
      // Dismiss loading toast
      loadingToast.dismiss();
      
      console.error("Error creating amenity:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to create amenity";
      
      // Enhanced error handling with specific messages
      let title = "Failed to Create Amenity";
      let description = errorMessage;
      
      if (errorMessage.includes('already exists') || errorMessage.includes('unique')) {
        title = "Duplicate Amenity Name";
        description = `An amenity named "${data.name}" already exists. Please choose a different name.`;
      } else if (errorMessage.includes('validation') || errorMessage.includes('invalid')) {
        title = "Invalid Amenity Data";
        description = "Please check the amenity information and try again.";
      } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        title = "Network Error";
        description = "Unable to connect to the server. Please check your connection and try again.";
      }
      
      showToast.error(title, description, {
        label: "Retry",
        onClick: () => createAmenity(data)
      });
      
      throw error;
    }
  }, [showToast]);

  // Update amenity with optimistic updates and enhanced feedback
  const updateAmenity = useCallback(async (id: number, data: AmenityFormData): Promise<Amenity> => {
    // Store original amenity for rollback
    const originalAmenity = state.amenities.find(a => a.id === id);
    
    if (!originalAmenity) {
      throw new Error("Amenity not found in current list");
    }

    // Show loading toast
    const loadingToast = showToast.loading(
      "Updating Amenity", 
      `Saving changes to "${originalAmenity.name}"...`
    );

    // Optimistic update
    setState(prev => ({
      ...prev,
      amenities: prev.amenities.map(amenity => 
        amenity.id === id 
          ? { ...amenity, ...data, updatedAt: new Date() }
          : amenity
      ),
    }));

    try {
      const response = await retryApiCall(
        () => fetch(`/api/amenities/${id}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data),
        }),
        {
          maxAttempts: 2,
          onRetry: (error, attempt) => {
            console.warn(`Retrying update amenity (attempt ${attempt}):`, error.message);
            showToast.warning(
              "Retrying Update", 
              `Attempting to save changes to "${originalAmenity.name}" (${attempt}/2)`
            );
          },
        }
      );

      const result: ApiResponse<Amenity> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to update amenity");
      }

      const updatedAmenity = result.data;

      // Update with actual server response
      setState(prev => ({
        ...prev,
        amenities: prev.amenities.map(amenity => 
          amenity.id === id ? updatedAmenity : amenity
        ),
      }));

      // Dismiss loading toast
      loadingToast.dismiss();

      const nameChanged = originalAmenity.name !== updatedAmenity.name;
      const iconChanged = originalAmenity.icon !== updatedAmenity.icon;
      
      let changeDescription = "The amenity has been successfully updated.";
      if (nameChanged && iconChanged) {
        changeDescription = `Name changed to "${updatedAmenity.name}" and icon updated.`;
      } else if (nameChanged) {
        changeDescription = `Name changed from "${originalAmenity.name}" to "${updatedAmenity.name}".`;
      } else if (iconChanged) {
        changeDescription = `Icon updated for "${updatedAmenity.name}".`;
      }

      showToast.success(
        "Amenity Updated Successfully", 
        changeDescription,
        4000
      );

      return updatedAmenity;
    } catch (error) {
      // Dismiss loading toast
      loadingToast.dismiss();
      
      // Rollback optimistic update
      setState(prev => ({
        ...prev,
        amenities: prev.amenities.map(amenity => 
          amenity.id === id ? originalAmenity : amenity
        ),
      }));

      console.error("Error updating amenity:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to update amenity";
      
      // Enhanced error handling
      let title = "Failed to Update Amenity";
      let description = errorMessage;
      
      if (errorMessage.includes('not found')) {
        title = "Amenity Not Found";
        description = `The amenity "${originalAmenity.name}" no longer exists. It may have been deleted by another user.`;
      } else if (errorMessage.includes('already exists') || errorMessage.includes('unique')) {
        title = "Duplicate Amenity Name";
        description = `An amenity named "${data.name}" already exists. Please choose a different name.`;
      } else if (errorMessage.includes('validation') || errorMessage.includes('invalid')) {
        title = "Invalid Amenity Data";
        description = "Please check the amenity information and try again.";
      }
      
      showToast.error(title, description, {
        label: "Retry",
        onClick: () => updateAmenity(id, data)
      });
      
      throw error;
    }
  }, [state.amenities, showToast]);

  // Delete amenity with optimistic updates and enhanced feedback
  const deleteAmenity = useCallback(async (id: number): Promise<void> => {
    // Store original amenity for rollback
    const originalAmenity = state.amenities.find(a => a.id === id);
    const originalIndex = state.amenities.findIndex(a => a.id === id);
    
    if (!originalAmenity) {
      throw new Error("Amenity not found in current list");
    }

    // Show loading toast
    const loadingToast = showToast.loading(
      "Deleting Amenity", 
      `Removing "${originalAmenity.name}" from the system...`
    );

    // Optimistic update - remove amenity from list
    setState(prev => ({
      ...prev,
      amenities: prev.amenities.filter(amenity => amenity.id !== id),
      totalAmenities: prev.totalAmenities - 1,
      totalPages: Math.ceil((prev.totalAmenities - 1) / prev.pageSize),
    }));

    try {
      const response = await retryApiCall(
        () => fetch(`/api/amenities/${id}`, {
          method: "DELETE",
        }),
        {
          maxAttempts: 2,
          onRetry: (error, attempt) => {
            console.warn(`Retrying delete amenity (attempt ${attempt}):`, error.message);
            showToast.warning(
              "Retrying Deletion", 
              `Attempting to delete "${originalAmenity.name}" (${attempt}/2)`
            );
          },
        }
      );

      // Dismiss loading toast
      loadingToast.dismiss();

      showToast.success(
        "Amenity Deleted Successfully", 
        `"${originalAmenity.name}" has been permanently removed from the system.`,
        4000
      );
    } catch (error) {
      // Dismiss loading toast
      loadingToast.dismiss();
      
      // Rollback optimistic update
      if (originalIndex !== -1) {
        setState(prev => {
          const newAmenities = [...prev.amenities];
          newAmenities.splice(originalIndex, 0, originalAmenity);
          return {
            ...prev,
            amenities: newAmenities,
            totalAmenities: prev.totalAmenities + 1,
            totalPages: Math.ceil((prev.totalAmenities + 1) / prev.pageSize),
          };
        });
      }

      console.error("Error deleting amenity:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to delete amenity";
      
      // Enhanced error handling
      let title = "Failed to Delete Amenity";
      let description = errorMessage;
      
      if (errorMessage.includes('associated') || errorMessage.includes('constraint') || errorMessage.includes('P2003')) {
        title = "Cannot Delete Amenity";
        description = `"${originalAmenity.name}" cannot be deleted because it is currently associated with one or more resources. Please remove these associations first.`;
      } else if (errorMessage.includes('not found')) {
        title = "Amenity Not Found";
        description = `The amenity "${originalAmenity.name}" no longer exists. It may have already been deleted.`;
      } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        title = "Network Error";
        description = "Unable to connect to the server. Please check your connection and try again.";
      }
      
      showToast.error(title, description, {
        label: "Retry",
        onClick: () => deleteAmenity(id)
      });
      
      throw error;
    }
  }, [state.amenities, showToast]);

  // Enhanced search functionality with better user feedback
  const setSearchQuery = useCallback((query: string) => {
    const trimmedQuery = query.trim();
    
    setState(prev => ({
      ...prev,
      searchQuery: query,
      currentPage: 1, // Reset to first page when searching
    }));

    // Enhanced search feedback
    if (trimmedQuery) {
      if (trimmedQuery.length >= 3) {
        showToast.info(
          "Searching Amenities", 
          `Looking for amenities matching "${trimmedQuery}"`,
          2000
        );
      } else if (trimmedQuery.length > 0) {
        showToast.info(
          "Search Query Too Short", 
          "Enter at least 3 characters to search",
          2000
        );
      }
    } else {
      // Query cleared
      showToast.info(
        "Search Cleared", 
        "Showing all amenities",
        1500
      );
    }
  }, [showToast]);

  const setCurrentPage = useCallback((page: number) => {
    setState(prev => ({ ...prev, currentPage: page }));
  }, []);

  const setPageSize = useCallback((size: number) => {
    setState(prev => ({
      ...prev,
      pageSize: size,
      currentPage: 1, // Reset to first page when changing page size
    }));
  }, []);

  // Utility functions
  const refresh = useCallback(() => {
    fetchAmenities(true);
  }, [fetchAmenities]);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Auto-fetch when dependencies change
  useEffect(() => {
    fetchAmenities();
  }, [fetchAmenities]);

  return {
    // State
    amenities: state.amenities,
    loading: state.loading,
    error: state.error,
    totalAmenities: state.totalAmenities,
    totalPages: state.totalPages,
    currentPage: state.currentPage,
    pageSize: state.pageSize,
    searchQuery: state.searchQuery,
    
    // Actions
    fetchAmenities,
    createAmenity,
    updateAmenity,
    deleteAmenity,
    setSearchQuery,
    setCurrentPage,
    setPageSize,
    refresh,
    clearError,
  };
}