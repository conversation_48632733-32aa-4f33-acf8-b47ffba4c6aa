"use client";

import { useState, useCallback, useEffect, useRef, useMemo } from "react";
import { useToast } from "@/hooks/use-toast";
import { retryApiCall, withRetry } from "@/lib/utils/retry";
import { categorizeResourceError, getRetryStrategy } from "@/lib/utils/form-errors";
import { useResourceErrorMonitor } from "@/lib/utils/error-monitoring";
import {
  Resource,
  ResourceFormData,
  ResourceListResponse,
  ApiResponse,
  ResourceSearchParams,
  ResourceType,
  getResourceTypeDisplayName,
} from "@/lib/types";

interface UseResourcesState {
  resources: Resource[];
  loading: boolean;
  error: string | null;
  totalResources: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  searchQuery: string;
  selectedType: ResourceType | "ALL";
}

interface UseResourcesActions {
  fetchResources: (showLoading?: boolean) => Promise<void>;
  createResource: (data: ResourceFormData) => Promise<Resource>;
  updateResource: (id: number, data: ResourceFormData) => Promise<Resource>;
  deleteResource: (id: number) => Promise<void>;
  setSearchQuery: (query: string) => void;
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
  setSelectedType: (type: ResourceType | "ALL") => void;
  refresh: () => void;
  clearError: () => void;
}

interface UseResourcesReturn extends UseResourcesState, UseResourcesActions {}

export function useResources(
  initialParams?: Partial<ResourceSearchParams>
): UseResourcesReturn {
  const { toast } = useToast();
  const { captureError } = useResourceErrorMonitor();
  const abortControllerRef = useRef<AbortController | null>(null);

  // Enhanced toast notification system with better user feedback
  const showToast = useMemo(
    () => ({
      success: (title: string, description: string, duration?: number) => {
        toast({
          title,
          description,
          duration: duration || 4000,
          className: "border-green-200 bg-green-50 text-green-900",
        });
      },
      error: (
        title: string,
        description: string,
        action?: { label: string; onClick: () => void }
      ) => {
        toast({
          variant: "destructive",
          title,
          description,
          duration: 6000, // Longer duration for errors
        });

        // Note: Custom action buttons in toast are not supported in this toast implementation
        // The action parameter is kept for future enhancement
      },
      info: (title: string, description: string, duration?: number) => {
        toast({
          title,
          description,
          duration: duration || 3000,
          className: "border-blue-200 bg-blue-50 text-blue-900",
        });
      },
      warning: (title: string, description: string) => {
        toast({
          title,
          description,
          duration: 5000,
          className: "border-yellow-200 bg-yellow-50 text-yellow-900",
        });
      },
      loading: (title: string, description: string) => {
        return toast({
          title,
          description,
          duration: Infinity, // Keep loading toast until dismissed
          className: "border-gray-200 bg-gray-50 text-gray-900",
        });
      },
    }),
    [toast]
  );

  const [state, setState] = useState<UseResourcesState>({
    resources: [],
    loading: true,
    error: null,
    totalResources: 0,
    totalPages: 0,
    currentPage: initialParams?.page || 1,
    pageSize: initialParams?.limit || 10,
    searchQuery: initialParams?.search || "",
    selectedType: initialParams?.type || "ALL",
  });

  // Abort any ongoing requests when component unmounts
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Fetch resources with proper error handling and loading states
  const fetchResources = useCallback(
    async (showLoading = true) => {
      // Abort any ongoing request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      abortControllerRef.current = new AbortController();

      if (showLoading) {
        setState((prev) => ({ ...prev, loading: true, error: null }));
      }

      try {
        const params = new URLSearchParams({
          page: state.currentPage.toString(),
          limit: state.pageSize.toString(),
        });

        if (state.searchQuery.trim()) {
          params.append("search", state.searchQuery.trim());
        }

        if (state.selectedType !== "ALL") {
          params.append("type", state.selectedType);
        }

        const response = await retryApiCall(
          () =>
            fetch(`/api/resources?${params}`, {
              signal: abortControllerRef.current?.signal,
            }),
          {
            maxAttempts: 3,
            onRetry: (error, attempt) => {
              console.warn(
                `Retrying fetch resources (attempt ${attempt}):`,
                error.message
              );

              const isNetworkError =
                error.message.includes("fetch") ||
                error.message.includes("Network");
              const isServerError =
                error.message.includes("500") || error.message.includes("502");

              let retryMessage = `Attempting to reconnect (${attempt}/3)`;
              if (isNetworkError) {
                retryMessage = `Network issue detected. Retrying connection (${attempt}/3)`;
              } else if (isServerError) {
                retryMessage = `Server error detected. Retrying request (${attempt}/3)`;
              }

              showToast.warning("Connection Issue", retryMessage);
            },
          }
        );

        const result: ApiResponse<ResourceListResponse> = await response.json();

        if (!result.success || !result.data) {
          throw new Error(result.error || "Failed to fetch resources");
        }

        const { data: resources, pagination } = result.data;

        setState((prev) => ({
          ...prev,
          resources,
          totalResources: pagination.total,
          totalPages: pagination.totalPages,
          loading: false,
          error: null,
        }));
      } catch (error) {
        // Don't show error if request was aborted
        if (error instanceof Error && error.name === "AbortError") {
          return;
        }

        console.error("Error fetching resources:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Failed to fetch resources";

        setState((prev) => ({
          ...prev,
          loading: false,
          error: errorMessage,
        }));

        // Enhanced error categorization and monitoring
        const errorCategory = categorizeResourceError(error);
        
        // Capture error for monitoring
        captureError(error, 'fetch-resources', {
          searchQuery: state.searchQuery,
          selectedType: state.selectedType,
          currentPage: state.currentPage,
          pageSize: state.pageSize
        });

        let title = "Failed to Load Resources";
        let description = errorCategory.userMessage;

        switch (errorCategory.type) {
          case 'network':
            title = "Network Connection Error";
            break;
          case 'server':
            title = "Server Error";
            break;
          case 'authorization':
            title = "Authentication Error";
            break;
          default:
            title = "Failed to Load Resources";
        }

        showToast.error(title, description, {
          label: "Retry",
          onClick: () => fetchResources(true),
        });
      }
    },
    [
      state.currentPage,
      state.pageSize,
      state.searchQuery,
      state.selectedType,
      toast,
    ]
  );

  // Create resource with optimistic updates and enhanced feedback
  const createResource = useCallback(
    async (data: ResourceFormData): Promise<Resource> => {
      // Show loading toast
      const loadingToast = showToast.loading(
        "Creating Resource",
        `Adding "${data.name}" to the system...`
      );

      try {
        const response = await retryApiCall(
          () =>
            fetch("/api/resources", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify(data),
            }),
          {
            maxAttempts: 2, // Less retries for create operations
            onRetry: (error, attempt) => {
              console.warn(
                `Retrying create resource (attempt ${attempt}):`,
                error.message
              );
              showToast.warning(
                "Retrying Creation",
                `Attempting to create "${data.name}" (${attempt}/2)`
              );
            },
          }
        );

        const result: ApiResponse<Resource> = await response.json();

        if (!result.success || !result.data) {
          throw new Error(result.error || "Failed to create resource");
        }

        const newResource = result.data;

        // Optimistic update - add resource to current list if it would be visible
        setState((prev) => {
          const updatedResources = [newResource, ...prev.resources];
          return {
            ...prev,
            resources: updatedResources.slice(0, prev.pageSize), // Keep only current page size
            totalResources: prev.totalResources + 1,
            totalPages: Math.ceil((prev.totalResources + 1) / prev.pageSize),
          };
        });

        // Dismiss loading toast
        loadingToast.dismiss();

        const resourceTypeDisplay = getResourceTypeDisplayName(
          newResource.type
        );
        showToast.success(
          "Resource Created Successfully",
          `"${newResource.name}" (${resourceTypeDisplay}) has been added to the system and is now available for booking.`,
          5000
        );

        return newResource;
      } catch (error) {
        // Dismiss loading toast
        loadingToast.dismiss();

        console.error("Error creating resource:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Failed to create resource";

        // Enhanced error handling with categorization
        const errorCategory = categorizeResourceError(error);
        
        // Capture error for monitoring
        captureError(error, 'create-resource', {
          resourceName: data.name,
          resourceType: data.type,
          basePrice: data.basePrice,
          amenityCount: data.amenityIds?.length || 0
        });

        let title = "Failed to Create Resource";
        let description = errorCategory.userMessage;

        switch (errorCategory.type) {
          case 'conflict':
            title = "Duplicate Resource Name";
            description = `A resource named "${data.name}" already exists. Please choose a different name.`;
            break;
          case 'validation':
            title = "Invalid Resource Data";
            break;
          case 'network':
            title = "Network Error";
            break;
          case 'server':
            title = "Server Error";
            break;
          default:
            title = "Failed to Create Resource";
        }

        showToast.error(title, description, {
          label: "Retry",
          onClick: () => createResource(data),
        });

        throw error;
      }
    },
    [showToast]
  );

  // Update resource with optimistic updates and enhanced feedback
  const updateResource = useCallback(
    async (id: number, data: ResourceFormData): Promise<Resource> => {
      // Store original resource for rollback
      const originalResource = state.resources.find((r) => r.id === id);

      if (!originalResource) {
        throw new Error("Resource not found in current list");
      }

      // Show loading toast
      const loadingToast = showToast.loading(
        "Updating Resource",
        `Saving changes to "${originalResource.name}"...`
      );

      // Optimistic update
      setState((prev) => ({
        ...prev,
        resources: prev.resources.map((resource) =>
          resource.id === id
            ? {
                ...resource,
                name: data.name,
                type: data.type,
                basePrice: data.basePrice,
                details: data.details || null,
                seatingStyle: data.seatingStyle || null,
                numberOfAttendees: data.numberOfAttendees || null,
                numberOfDesks: data.numberOfDesks || null,
                numberOfChairs: data.numberOfChairs || null,
                updatedAt: new Date(),
              }
            : resource
        ),
      }));

      try {
        const response = await retryApiCall(
          () =>
            fetch(`/api/resources/${id}`, {
              method: "PUT",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify(data),
            }),
          {
            maxAttempts: 2,
            onRetry: (error, attempt) => {
              console.warn(
                `Retrying update resource (attempt ${attempt}):`,
                error.message
              );
              showToast.warning(
                "Retrying Update",
                `Attempting to save changes to "${originalResource.name}" (${attempt}/2)`
              );
            },
          }
        );

        const result: ApiResponse<Resource> = await response.json();

        if (!result.success || !result.data) {
          throw new Error(result.error || "Failed to update resource");
        }

        const updatedResource = result.data;

        // Update with actual server response
        setState((prev) => ({
          ...prev,
          resources: prev.resources.map((resource) =>
            resource.id === id ? updatedResource : resource
          ),
        }));

        // Dismiss loading toast
        loadingToast.dismiss();

        const nameChanged = originalResource.name !== updatedResource.name;
        const typeChanged = originalResource.type !== updatedResource.type;
        const priceChanged =
          originalResource.basePrice !== updatedResource.basePrice;

        let changeDescription = "The resource has been successfully updated.";
        if (nameChanged && typeChanged) {
          changeDescription = `Name changed to "${updatedResource.name}" and type changed to ${getResourceTypeDisplayName(updatedResource.type)}.`;
        } else if (nameChanged) {
          changeDescription = `Name changed from "${originalResource.name}" to "${updatedResource.name}".`;
        } else if (typeChanged) {
          changeDescription = `Type changed to ${getResourceTypeDisplayName(updatedResource.type)}.`;
        } else if (priceChanged) {
          changeDescription = `Base price updated to ${updatedResource.basePrice.toLocaleString()} IQD.`;
        }

        showToast.success(
          "Resource Updated Successfully",
          changeDescription,
          4000
        );

        return updatedResource;
      } catch (error) {
        // Dismiss loading toast
        loadingToast.dismiss();

        // Rollback optimistic update
        setState((prev) => ({
          ...prev,
          resources: prev.resources.map((resource) =>
            resource.id === id ? originalResource : resource
          ),
        }));

        console.error("Error updating resource:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Failed to update resource";

        // Enhanced error handling with categorization
        const errorCategory = categorizeResourceError(error);
        
        // Capture error for monitoring
        captureError(error, 'update-resource', {
          resourceId: id,
          resourceName: data.name,
          resourceType: data.type,
          originalName: originalResource.name,
          originalType: originalResource.type
        });

        let title = "Failed to Update Resource";
        let description = errorCategory.userMessage;

        switch (errorCategory.type) {
          case 'not_found':
            title = "Resource Not Found";
            description = `The resource "${originalResource.name}" no longer exists. It may have been deleted by another user.`;
            break;
          case 'conflict':
            title = "Duplicate Resource Name";
            description = `A resource named "${data.name}" already exists. Please choose a different name.`;
            break;
          case 'validation':
            title = "Invalid Resource Data";
            break;
          case 'network':
            title = "Network Error";
            break;
          case 'server':
            title = "Server Error";
            break;
          default:
            title = "Failed to Update Resource";
        }

        showToast.error(title, description, {
          label: "Retry",
          onClick: () => updateResource(id, data),
        });

        throw error;
      }
    },
    [state.resources, showToast]
  );

  // Delete resource with optimistic updates and enhanced feedback
  const deleteResource = useCallback(
    async (id: number): Promise<void> => {
      // Store original resource for rollback
      const originalResource = state.resources.find((r) => r.id === id);
      const originalIndex = state.resources.findIndex((r) => r.id === id);

      if (!originalResource) {
        throw new Error("Resource not found in current list");
      }

      // Show loading toast
      const loadingToast = showToast.loading(
        "Deleting Resource",
        `Removing "${originalResource.name}" from the system...`
      );

      // Optimistic update - remove resource from list
      setState((prev) => ({
        ...prev,
        resources: prev.resources.filter((resource) => resource.id !== id),
        totalResources: prev.totalResources - 1,
        totalPages: Math.ceil((prev.totalResources - 1) / prev.pageSize),
      }));

      try {
        const response = await retryApiCall(
          () =>
            fetch(`/api/resources/${id}`, {
              method: "DELETE",
            }),
          {
            maxAttempts: 2,
            onRetry: (error, attempt) => {
              console.warn(
                `Retrying delete resource (attempt ${attempt}):`,
                error.message
              );
              showToast.warning(
                "Retrying Deletion",
                `Attempting to delete "${originalResource.name}" (${attempt}/2)`
              );
            },
          }
        );

        // Dismiss loading toast
        loadingToast.dismiss();

        const resourceTypeDisplay = getResourceTypeDisplayName(
          originalResource.type
        );
        showToast.success(
          "Resource Deleted Successfully",
          `"${originalResource.name}" (${resourceTypeDisplay}) has been permanently removed from the system.`,
          4000
        );
      } catch (error) {
        // Dismiss loading toast
        loadingToast.dismiss();

        // Rollback optimistic update
        if (originalIndex !== -1) {
          setState((prev) => {
            const newResources = [...prev.resources];
            newResources.splice(originalIndex, 0, originalResource);
            return {
              ...prev,
              resources: newResources,
              totalResources: prev.totalResources + 1,
              totalPages: Math.ceil((prev.totalResources + 1) / prev.pageSize),
            };
          });
        }

        console.error("Error deleting resource:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Failed to delete resource";

        // Enhanced error handling with categorization
        const errorCategory = categorizeResourceError(error);
        
        // Capture error for monitoring
        captureError(error, 'delete-resource', {
          resourceId: id,
          resourceName: originalResource.name,
          resourceType: originalResource.type
        });

        let title = "Failed to Delete Resource";
        let description = errorCategory.userMessage;

        switch (errorCategory.type) {
          case 'conflict':
            title = "Cannot Delete Resource";
            description = `"${originalResource.name}" cannot be deleted because it has existing bookings. Please cancel or complete these bookings first.`;
            break;
          case 'not_found':
            title = "Resource Not Found";
            description = `The resource "${originalResource.name}" no longer exists. It may have already been deleted.`;
            break;
          case 'network':
            title = "Network Error";
            break;
          case 'server':
            title = "Server Error";
            break;
          default:
            title = "Failed to Delete Resource";
        }

        showToast.error(title, description, {
          label: "Retry",
          onClick: () => deleteResource(id),
        });

        throw error;
      }
    },
    [state.resources, showToast]
  );

  // Enhanced search functionality with better user feedback
  const setSearchQuery = useCallback(
    (query: string) => {
      const trimmedQuery = query.trim();

      setState((prev) => ({
        ...prev,
        searchQuery: query,
        currentPage: 1, // Reset to first page when searching
      }));

      // Enhanced search feedback
      if (trimmedQuery) {
        if (trimmedQuery.length >= 3) {
          showToast.info(
            "Searching Resources",
            `Looking for resources matching "${trimmedQuery}"`,
            2000
          );
        } else if (trimmedQuery.length > 0) {
          showToast.info(
            "Search Query Too Short",
            "Enter at least 3 characters to search",
            2000
          );
        }
      } else {
        // Query cleared
        showToast.info("Search Cleared", "Showing all resources", 1500);
      }
    },
    [showToast]
  );

  // Resource type filtering functionality
  const setSelectedType = useCallback(
    (type: ResourceType | "ALL") => {
      setState((prev) => ({
        ...prev,
        selectedType: type,
        currentPage: 1, // Reset to first page when filtering
      }));

      // Enhanced filtering feedback
      if (type === "ALL") {
        showToast.info("Filter Cleared", "Showing all resource types", 1500);
      } else {
        const typeDisplay = getResourceTypeDisplayName(type);
        showToast.info(
          "Filter Applied",
          `Showing only ${typeDisplay} resources`,
          2000
        );
      }
    },
    [showToast]
  );

  const setCurrentPage = useCallback((page: number) => {
    setState((prev) => ({ ...prev, currentPage: page }));
  }, []);

  const setPageSize = useCallback((size: number) => {
    setState((prev) => ({
      ...prev,
      pageSize: size,
      currentPage: 1, // Reset to first page when changing page size
    }));
  }, []);

  // Utility functions
  const refresh = useCallback(() => {
    fetchResources(true);
  }, [fetchResources]);

  const clearError = useCallback(() => {
    setState((prev) => ({ ...prev, error: null }));
  }, []);

  // Auto-fetch when dependencies change
  useEffect(() => {
    fetchResources();
  }, [fetchResources]);

  return {
    // State
    resources: state.resources,
    loading: state.loading,
    error: state.error,
    totalResources: state.totalResources,
    totalPages: state.totalPages,
    currentPage: state.currentPage,
    pageSize: state.pageSize,
    searchQuery: state.searchQuery,
    selectedType: state.selectedType,

    // Actions
    fetchResources,
    createResource,
    updateResource,
    deleteResource,
    setSearchQuery,
    setCurrentPage,
    setPageSize,
    setSelectedType,
    refresh,
    clearError,
  };
}
