"use client";

import { useState, useCallback, useEffect, useRef, useMemo } from "react";
import { useToast } from "@/hooks/use-toast";
import { retryApiCall } from "@/lib/utils/retry";
import { cachedFetch, generateCacheKey, invalidateCache } from "@/lib/utils/cache";
import { 
  Invoice, 
  InvoiceFormData, 
  InvoiceListResponse, 
  Payment,
  PaymentFormData,
  PaymentListResponse,
  ApiResponse,
  InvoiceSearchParams,
  InvoiceStatus,
  LineItemFormData,
  calculateInvoiceStatus,
  calculateInvoiceBalance,
  formatCurrency
} from "@/lib/types";

interface UseInvoicesState {
  invoices: Invoice[];
  loading: boolean;
  error: string | null;
  totalInvoices: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  searchQuery: string;
  selectedStatus?: InvoiceStatus;
  selectedCustomerId?: number;
  selectedBookingId?: number;
  // Payment-specific state
  payments: Payment[];
  paymentsLoading: boolean;
  paymentsError: string | null;
}

interface UseInvoicesActions {
  fetchInvoices: (showLoading?: boolean) => Promise<void>;
  fetchInvoicePayments: (invoiceId: number) => Promise<void>;
  createInvoice: (data: InvoiceFormData) => Promise<Invoice>;
  updateInvoice: (id: number, data: InvoiceFormData) => Promise<Invoice>;
  deleteInvoice: (id: number) => Promise<void>;
  addPayment: (invoiceId: number, data: PaymentFormData) => Promise<Payment>;
  updatePayment: (paymentId: number, data: PaymentFormData) => Promise<Payment>;
  deletePayment: (paymentId: number) => Promise<void>;
  updateLineItems: (invoiceId: number, lineItems: LineItemFormData[]) => Promise<Invoice>;
  recalculateInvoiceStatus: (invoiceId: number) => Promise<Invoice>;
  setSearchQuery: (query: string) => void;
  setStatusFilter: (status?: InvoiceStatus) => void;
  setCustomerFilter: (customerId?: number) => void;
  setBookingFilter: (bookingId?: number) => void;
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
  refresh: () => void;
  clearError: () => void;
  clearFilters: () => void;
}

interface UseInvoicesReturn extends UseInvoicesState, UseInvoicesActions {}

export function useInvoices(initialParams?: Partial<InvoiceSearchParams>): UseInvoicesReturn {
  const { toast } = useToast();
  const abortControllerRef = useRef<AbortController | null>(null);
  const paymentsAbortControllerRef = useRef<AbortController | null>(null);

  // Enhanced toast notification system with better user feedback
  const showToast = useMemo(() => ({
    success: (title: string, description: string, duration?: number) => {
      toast({ 
        title, 
        description,
        duration: duration || 4000,
        className: "border-green-200 bg-green-50 text-green-900"
      });
    },
    error: (title: string, description: string, action?: { label: string; onClick: () => void }) => {
      toast({ 
        variant: "destructive", 
        title, 
        description,
        duration: 6000, // Longer duration for errors
      });
    },
    info: (title: string, description: string, duration?: number) => {
      toast({ 
        title, 
        description,
        duration: duration || 3000,
        className: "border-blue-200 bg-blue-50 text-blue-900"
      });
    },
    warning: (title: string, description: string) => {
      toast({
        title,
        description,
        duration: 5000,
        className: "border-yellow-200 bg-yellow-50 text-yellow-900"
      });
    },
    loading: (title: string, description: string) => {
      return toast({
        title,
        description,
        duration: Infinity, // Keep loading toast until dismissed
        className: "border-gray-200 bg-gray-50 text-gray-900"
      });
    }
  }), [toast]);

  const [state, setState] = useState<UseInvoicesState>({
    invoices: [],
    loading: true,
    error: null,
    totalInvoices: 0,
    totalPages: 0,
    currentPage: initialParams?.page || 1,
    pageSize: initialParams?.limit || 10,
    searchQuery: initialParams?.search || "",
    selectedStatus: initialParams?.status,
    selectedCustomerId: initialParams?.customerId,
    selectedBookingId: initialParams?.bookingId,
    // Payment-specific state
    payments: [],
    paymentsLoading: false,
    paymentsError: null,
  });

  // Abort any ongoing requests when component unmounts
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (paymentsAbortControllerRef.current) {
        paymentsAbortControllerRef.current.abort();
      }
    };
  }, []);

  // Fetch invoices with proper error handling and loading states
  const fetchInvoices = useCallback(async (showLoading = true) => {
    // Abort any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    if (showLoading) {
      setState(prev => ({ ...prev, loading: true, error: null }));
    }

    try {
      const params = new URLSearchParams({
        page: state.currentPage.toString(),
        limit: state.pageSize.toString(),
      });

      if (state.searchQuery.trim()) {
        params.append("search", state.searchQuery.trim());
      }

      if (state.selectedStatus) {
        params.append("status", state.selectedStatus);
      }

      if (state.selectedCustomerId) {
        params.append("customerId", state.selectedCustomerId.toString());
      }

      if (state.selectedBookingId) {
        params.append("bookingId", state.selectedBookingId.toString());
      }

      const response = await retryApiCall(
        () => fetch(`/api/invoices?${params}`, {
          signal: abortControllerRef.current?.signal,
        }),
        {
          maxAttempts: 3,
          onRetry: (error, attempt) => {
            console.warn(`Retrying fetch invoices (attempt ${attempt}):`, error.message);
            
            const isNetworkError = error.message.includes('fetch') || error.message.includes('Network');
            const isServerError = error.message.includes('500') || error.message.includes('502');
            
            let retryMessage = `Attempting to reconnect (${attempt}/3)`;
            if (isNetworkError) {
              retryMessage = `Network issue detected. Retrying connection (${attempt}/3)`;
            } else if (isServerError) {
              retryMessage = `Server error detected. Retrying request (${attempt}/3)`;
            }
            
            showToast.warning("Connection Issue", retryMessage);
          },
        }
      );

      const result: ApiResponse<InvoiceListResponse> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to fetch invoices");
      }

      const { data: invoices, pagination } = result.data;

      setState(prev => ({
        ...prev,
        invoices,
        totalInvoices: pagination.total,
        totalPages: pagination.totalPages,
        loading: false,
        error: null,
      }));
    } catch (error) {
      // Don't show error if request was aborted
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }

      console.error("Error fetching invoices:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch invoices";
      
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
      
      // Enhanced error categorization and user feedback
      const isNetworkError = errorMessage.includes('fetch') || errorMessage.includes('Network') || !navigator.onLine;
      const isServerError = errorMessage.includes('500') || errorMessage.includes('502') || errorMessage.includes('503');
      const isAuthError = errorMessage.includes('401') || errorMessage.includes('403');
      
      let title = "Failed to Load Invoices";
      let description = "Unable to fetch invoice data. Please try again.";
      
      if (isNetworkError) {
        title = "Network Connection Error";
        description = navigator.onLine 
          ? "Unable to connect to the server. Please check your internet connection and try again."
          : "You appear to be offline. Please check your internet connection.";
      } else if (isServerError) {
        title = "Server Error";
        description = "The server is temporarily unavailable. Our team has been notified. Please try again in a few moments.";
      } else if (isAuthError) {
        title = "Authentication Error";
        description = "Your session may have expired. Please refresh the page and try again.";
      }
      
      showToast.error(title, description, {
        label: "Retry",
        onClick: () => fetchInvoices(true)
      });
    }
  }, [state.currentPage, state.pageSize, state.searchQuery, state.selectedStatus, state.selectedCustomerId, state.selectedBookingId, showToast]);

  // Fetch payments for a specific invoice
  const fetchInvoicePayments = useCallback(async (invoiceId: number) => {
    // Abort any ongoing payments request
    if (paymentsAbortControllerRef.current) {
      paymentsAbortControllerRef.current.abort();
    }

    paymentsAbortControllerRef.current = new AbortController();

    setState(prev => ({ ...prev, paymentsLoading: true, paymentsError: null }));

    try {
      const response = await retryApiCall(
        () => fetch(`/api/invoices/${invoiceId}/payments`, {
          signal: paymentsAbortControllerRef.current?.signal,
        }),
        {
          maxAttempts: 3,
          onRetry: (error, attempt) => {
            console.warn(`Retrying fetch payments (attempt ${attempt}):`, error.message);
            showToast.warning("Loading Payments", `Retrying payment data (${attempt}/3)`);
          },
        }
      );

      const result: ApiResponse<PaymentListResponse> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to fetch payments");
      }

      setState(prev => ({
        ...prev,
        payments: result.data?.data || [],
        paymentsLoading: false,
        paymentsError: null,
      }));
    } catch (error) {
      // Don't show error if request was aborted
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }

      console.error("Error fetching payments:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch payments";
      
      setState(prev => ({
        ...prev,
        paymentsLoading: false,
        paymentsError: errorMessage,
      }));
      
      showToast.error(
        "Failed to Load Payments", 
        "Unable to load payment information. Please try again.",
        {
          label: "Retry",
          onClick: () => fetchInvoicePayments(invoiceId)
        }
      );
    }
  }, [showToast]); 
 // Create invoice with optimistic updates and enhanced feedback
  const createInvoice = useCallback(async (data: InvoiceFormData): Promise<Invoice> => {
    // Show loading toast
    const loadingToast = showToast.loading(
      "Creating Invoice", 
      "Generating invoice with line items..."
    );

    try {
      // No transformation needed - both catering and normal items use quantity × unit_price
      const transformedData = data;

      const response = await retryApiCall(
        () => fetch("/api/invoices", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(transformedData),
        }),
        {
          maxAttempts: 2, // Less retries for create operations
          onRetry: (error, attempt) => {
            console.warn(`Retrying create invoice (attempt ${attempt}):`, error.message);
            showToast.warning(
              "Retrying Creation", 
              `Attempting to create invoice (${attempt}/2)`
            );
          },
        }
      );

      const result: ApiResponse<Invoice> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to create invoice");
      }

      const newInvoice = result.data;

      // Optimistic update - add invoice to current list if it would be visible
      setState(prev => {
        const updatedInvoices = [newInvoice, ...prev.invoices];
        return {
          ...prev,
          invoices: updatedInvoices.slice(0, prev.pageSize), // Keep only current page size
          totalInvoices: prev.totalInvoices + 1,
          totalPages: Math.ceil((prev.totalInvoices + 1) / prev.pageSize),
        };
      });

      // Dismiss loading toast
      loadingToast.dismiss();

      const totalAmount = formatCurrency(newInvoice.total);
      showToast.success(
        "Invoice Created Successfully", 
        `Invoice for "${newInvoice.booking.customer.name}" has been created with total amount ${totalAmount}.`,
        5000
      );

      return newInvoice;
    } catch (error) {
      // Dismiss loading toast
      loadingToast.dismiss();
      
      console.error("Error creating invoice:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to create invoice";
      
      // Enhanced error handling with specific messages
      let title = "Failed to Create Invoice";
      let description = errorMessage;
      
      if (errorMessage.includes('already exists') || errorMessage.includes('duplicate')) {
        title = "Invoice Already Exists";
        description = "An invoice for this booking already exists. Please check the booking details.";
      } else if (errorMessage.includes('not found')) {
        title = "Booking Not Found";
        description = "The selected booking no longer exists. Please refresh and try again.";
      } else if (errorMessage.includes('validation') || errorMessage.includes('invalid')) {
        title = "Invalid Invoice Data";
        description = "Please check the invoice information and line items.";
      } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        title = "Network Error";
        description = "Unable to connect to the server. Please check your connection and try again.";
      }
      
      showToast.error(title, description, {
        label: "Retry",
        onClick: () => createInvoice(data)
      });
      
      throw error;
    }
  }, [showToast]);

  // Update invoice with optimistic updates and enhanced feedback
  const updateInvoice = useCallback(async (id: number, data: InvoiceFormData): Promise<Invoice> => {
    // Store original invoice for rollback
    const originalInvoice = state.invoices.find(i => i.id === id);
    
    if (!originalInvoice) {
      throw new Error("Invoice not found in current list");
    }

    // Show loading toast
    const loadingToast = showToast.loading(
      "Updating Invoice", 
      `Saving changes to invoice for "${originalInvoice.booking.customer.name}"...`
    );

    // Optimistic update - we can't fully merge InvoiceFormData with Invoice
    // so we'll just mark it as loading and wait for the server response
    setState(prev => ({
      ...prev,
      invoices: prev.invoices.map(invoice => 
        invoice.id === id 
          ? { ...invoice, updatedAt: new Date() }
          : invoice
      ),
    }));

    try {
      // No transformation needed - both catering and normal items use quantity × unit_price
      const transformedData = data;

      const response = await retryApiCall(
        () => fetch(`/api/invoices/${id}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(transformedData),
        }),
        {
          maxAttempts: 2,
          onRetry: (error, attempt) => {
            console.warn(`Retrying update invoice (attempt ${attempt}):`, error.message);
            showToast.warning(
              "Retrying Update", 
              `Attempting to save changes (${attempt}/2)`
            );
          },
        }
      );

      const result: ApiResponse<Invoice> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to update invoice");
      }

      const updatedInvoice = result.data;

      // Update with actual server response
      setState(prev => ({
        ...prev,
        invoices: prev.invoices.map(invoice => 
          invoice.id === id ? updatedInvoice : invoice
        ),
      }));

      // Dismiss loading toast
      loadingToast.dismiss();

      const totalAmount = formatCurrency(updatedInvoice.total);
      const statusChanged = originalInvoice.status !== updatedInvoice.status;
      
      let changeDescription = "The invoice has been successfully updated.";
      if (statusChanged) {
        changeDescription = `Invoice status changed to "${updatedInvoice.status}" with total amount ${totalAmount}.`;
      } else {
        changeDescription = `Invoice updated with total amount ${totalAmount}.`;
      }

      showToast.success(
        "Invoice Updated Successfully", 
        changeDescription,
        4000
      );

      return updatedInvoice;
    } catch (error) {
      // Dismiss loading toast
      loadingToast.dismiss();
      
      // Rollback optimistic update
      setState(prev => ({
        ...prev,
        invoices: prev.invoices.map(invoice => 
          invoice.id === id ? originalInvoice : invoice
        ),
      }));

      console.error("Error updating invoice:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to update invoice";
      
      // Enhanced error handling
      let title = "Failed to Update Invoice";
      let description = errorMessage;
      
      if (errorMessage.includes('not found')) {
        title = "Invoice Not Found";
        description = `The invoice no longer exists. It may have been deleted by another user.`;
      } else if (errorMessage.includes('validation') || errorMessage.includes('invalid')) {
        title = "Invalid Invoice Data";
        description = "Please check the invoice information and line items.";
      } else if (errorMessage.includes('payments exist')) {
        title = "Cannot Update Invoice";
        description = "This invoice cannot be modified because it has existing payments.";
      }
      
      showToast.error(title, description, {
        label: "Retry",
        onClick: () => updateInvoice(id, data)
      });
      
      throw error;
    }
  }, [state.invoices, showToast]);

  // Delete invoice with optimistic updates and enhanced feedback
  const deleteInvoice = useCallback(async (id: number): Promise<void> => {
    // Store original invoice for rollback
    const originalInvoice = state.invoices.find(i => i.id === id);
    const originalIndex = state.invoices.findIndex(i => i.id === id);
    
    if (!originalInvoice) {
      throw new Error("Invoice not found in current list");
    }

    // Show loading toast
    const loadingToast = showToast.loading(
      "Deleting Invoice", 
      `Removing invoice for "${originalInvoice.booking.customer.name}"...`
    );

    // Optimistic update - remove invoice from list
    setState(prev => ({
      ...prev,
      invoices: prev.invoices.filter(invoice => invoice.id !== id),
      totalInvoices: prev.totalInvoices - 1,
      totalPages: Math.ceil((prev.totalInvoices - 1) / prev.pageSize),
    }));

    try {
      const response = await retryApiCall(
        () => fetch(`/api/invoices/${id}`, {
          method: "DELETE",
        }),
        {
          maxAttempts: 2,
          onRetry: (error, attempt) => {
            console.warn(`Retrying delete invoice (attempt ${attempt}):`, error.message);
            showToast.warning(
              "Retrying Deletion", 
              `Attempting to delete invoice (${attempt}/2)`
            );
          },
        }
      );

      const result: ApiResponse = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || "Failed to delete invoice");
      }

      // Dismiss loading toast
      loadingToast.dismiss();

      showToast.success(
        "Invoice Deleted Successfully", 
        `The invoice for "${originalInvoice.booking.customer.name}" has been permanently removed.`,
        4000
      );
    } catch (error) {
      // Dismiss loading toast
      loadingToast.dismiss();
      
      // Rollback optimistic update
      if (originalIndex !== -1) {
        setState(prev => {
          const newInvoices = [...prev.invoices];
          newInvoices.splice(originalIndex, 0, originalInvoice);
          return {
            ...prev,
            invoices: newInvoices,
            totalInvoices: prev.totalInvoices + 1,
            totalPages: Math.ceil((prev.totalInvoices + 1) / prev.pageSize),
          };
        });
      }

      console.error("Error deleting invoice:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to delete invoice";
      
      // Enhanced error handling
      let title = "Failed to Delete Invoice";
      let description = errorMessage;
      
      if (errorMessage.includes('associated') || errorMessage.includes('constraint') || errorMessage.includes('P2003')) {
        title = "Cannot Delete Invoice";
        description = `This invoice cannot be deleted because it has existing payments. Please remove all payments first.`;
      } else if (errorMessage.includes('not found')) {
        title = "Invoice Not Found";
        description = `The invoice no longer exists. It may have already been deleted.`;
      } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        title = "Network Error";
        description = "Unable to connect to the server. Please check your connection and try again.";
      }
      
      showToast.error(title, description, {
        label: "Retry",
        onClick: () => deleteInvoice(id)
      });
      
      throw error;
    }
  }, [state.invoices, showToast]);

  // Add payment to invoice with automatic status calculation
  const addPayment = useCallback(async (invoiceId: number, data: PaymentFormData): Promise<Payment> => {
    const invoice = state.invoices.find(i => i.id === invoiceId);
    const customerName = invoice?.booking.customer.name || "Unknown Customer";

    // Show loading toast
    const loadingToast = showToast.loading(
      "Recording Payment", 
      `Adding ${formatCurrency(data.amount)} payment for "${customerName}"...`
    );

    try {
      const response = await retryApiCall(
        () => fetch(`/api/invoices/${invoiceId}/payments`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data),
        }),
        {
          maxAttempts: 2,
          onRetry: (error, attempt) => {
            console.warn(`Retrying add payment (attempt ${attempt}):`, error.message);
            showToast.warning(
              "Retrying Payment", 
              `Attempting to record payment (${attempt}/2)`
            );
          },
        }
      );

      const result: ApiResponse<Payment> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to add payment");
      }

      const newPayment = result.data;

      // Update payments list
      setState(prev => ({
        ...prev,
        payments: [newPayment, ...prev.payments],
      }));

      // Update invoice with new payment amount and status
      if (invoice) {
        const newPaidAmount = Number(invoice.paid) + data.amount;
        const newStatus = calculateInvoiceStatus(Number(invoice.total), newPaidAmount);
        
        setState(prev => ({
          ...prev,
          invoices: prev.invoices.map(inv => 
            inv.id === invoiceId 
              ? { ...inv, paid: newPaidAmount, status: newStatus }
              : inv
          ),
        }));
      }

      // Dismiss loading toast
      loadingToast.dismiss();

      const remainingBalance = invoice ? calculateInvoiceBalance({ ...invoice, paid: Number(invoice.paid) + data.amount }) : 0;
      const balanceText = remainingBalance > 0 
        ? ` Remaining balance: ${formatCurrency(remainingBalance)}.`
        : " Invoice is now fully paid.";

      showToast.success(
        "Payment Recorded Successfully", 
        `Payment of ${formatCurrency(data.amount)} has been added for "${customerName}".${balanceText}`,
        5000
      );

      return newPayment;
    } catch (error) {
      // Dismiss loading toast
      loadingToast.dismiss();
      
      console.error("Error adding payment:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to add payment";
      
      // Enhanced error handling
      let title = "Failed to Record Payment";
      let description = errorMessage;
      
      if (errorMessage.includes('exceeds') || errorMessage.includes('overpayment')) {
        title = "Payment Amount Too High";
        description = "The payment amount exceeds the remaining invoice balance. Please enter a valid amount.";
      } else if (errorMessage.includes('not found')) {
        title = "Invoice Not Found";
        description = "The invoice no longer exists. Please refresh and try again.";
      } else if (errorMessage.includes('validation') || errorMessage.includes('invalid')) {
        title = "Invalid Payment Data";
        description = "Please check the payment information and try again.";
      }
      
      showToast.error(title, description, {
        label: "Retry",
        onClick: () => addPayment(invoiceId, data)
      });
      
      throw error;
    }
  }, [state.invoices, state.payments, showToast]);

  // Update payment with enhanced feedback
  const updatePayment = useCallback(async (paymentId: number, data: PaymentFormData): Promise<Payment> => {
    // Store original payment for rollback
    const originalPayment = state.payments.find(p => p.id === paymentId);
    
    if (!originalPayment) {
      throw new Error("Payment not found in current list");
    }

    // Show loading toast
    const loadingToast = showToast.loading(
      "Updating Payment", 
      `Saving changes to ${formatCurrency(originalPayment.amount)} payment...`
    );

    // Optimistic update
    setState(prev => ({
      ...prev,
      payments: prev.payments.map(payment => 
        payment.id === paymentId 
          ? { ...payment, ...data, updatedAt: new Date() }
          : payment
      ),
    }));

    try {
      const response = await retryApiCall(
        () => fetch(`/api/payments/${paymentId}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data),
        }),
        {
          maxAttempts: 2,
          onRetry: (error, attempt) => {
            console.warn(`Retrying update payment (attempt ${attempt}):`, error.message);
            showToast.warning(
              "Retrying Update", 
              `Attempting to save payment changes (${attempt}/2)`
            );
          },
        }
      );

      const result: ApiResponse<Payment> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to update payment");
      }

      const updatedPayment = result.data;

      // Update with actual server response
      setState(prev => ({
        ...prev,
        payments: prev.payments.map(payment => 
          payment.id === paymentId ? updatedPayment : payment
        ),
      }));

      // Recalculate invoice status if payment amount changed
      if (originalPayment.amount !== updatedPayment.amount) {
        await recalculateInvoiceStatus(updatedPayment.invoiceId);
      }

      // Dismiss loading toast
      loadingToast.dismiss();

      const amountChanged = originalPayment.amount !== updatedPayment.amount;
      let changeDescription = "The payment has been successfully updated.";
      if (amountChanged) {
        changeDescription = `Payment amount changed from ${formatCurrency(originalPayment.amount)} to ${formatCurrency(updatedPayment.amount)}.`;
      }

      showToast.success(
        "Payment Updated Successfully", 
        changeDescription,
        4000
      );

      return updatedPayment;
    } catch (error) {
      // Dismiss loading toast
      loadingToast.dismiss();
      
      // Rollback optimistic update
      setState(prev => ({
        ...prev,
        payments: prev.payments.map(payment => 
          payment.id === paymentId ? originalPayment : payment
        ),
      }));

      console.error("Error updating payment:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to update payment";
      
      showToast.error("Failed to Update Payment", errorMessage, {
        label: "Retry",
        onClick: () => updatePayment(paymentId, data)
      });
      
      throw error;
    }
  }, [state.payments, showToast]);

  // Delete payment with automatic status recalculation
  const deletePayment = useCallback(async (paymentId: number): Promise<void> => {
    // Store original payment for rollback
    const originalPayment = state.payments.find(p => p.id === paymentId);
    const originalIndex = state.payments.findIndex(p => p.id === paymentId);
    
    if (!originalPayment) {
      throw new Error("Payment not found in current list");
    }

    // Show loading toast
    const loadingToast = showToast.loading(
      "Deleting Payment", 
      `Removing ${formatCurrency(originalPayment.amount)} payment...`
    );

    // Optimistic update - remove payment from list
    setState(prev => ({
      ...prev,
      payments: prev.payments.filter(payment => payment.id !== paymentId),
    }));

    try {
      const response = await retryApiCall(
        () => fetch(`/api/payments/${paymentId}`, {
          method: "DELETE",
        }),
        {
          maxAttempts: 2,
          onRetry: (error, attempt) => {
            console.warn(`Retrying delete payment (attempt ${attempt}):`, error.message);
            showToast.warning(
              "Retrying Deletion", 
              `Attempting to delete payment (${attempt}/2)`
            );
          },
        }
      );

      const result: ApiResponse = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || "Failed to delete payment");
      }

      // Recalculate invoice status after payment deletion
      await recalculateInvoiceStatus(originalPayment.invoiceId);

      // Dismiss loading toast
      loadingToast.dismiss();

      showToast.success(
        "Payment Deleted Successfully", 
        `The ${formatCurrency(originalPayment.amount)} payment has been removed and invoice status updated.`,
        4000
      );
    } catch (error) {
      // Dismiss loading toast
      loadingToast.dismiss();
      
      // Rollback optimistic update
      if (originalIndex !== -1) {
        setState(prev => {
          const newPayments = [...prev.payments];
          newPayments.splice(originalIndex, 0, originalPayment);
          return {
            ...prev,
            payments: newPayments,
          };
        });
      }

      console.error("Error deleting payment:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to delete payment";
      
      showToast.error("Failed to Delete Payment", errorMessage, {
        label: "Retry",
        onClick: () => deletePayment(paymentId)
      });
      
      throw error;
    }
  }, [state.payments, showToast]);

  // Update line items for an invoice
  const updateLineItems = useCallback(async (invoiceId: number, lineItems: LineItemFormData[]): Promise<Invoice> => {
    const invoice = state.invoices.find(i => i.id === invoiceId);
    const customerName = invoice?.booking.customer.name || "Unknown Customer";

    // Show loading toast
    const loadingToast = showToast.loading(
      "Updating Line Items", 
      `Updating invoice items for "${customerName}"...`
    );

    try {
      const response = await retryApiCall(
        () => fetch(`/api/invoices/${invoiceId}/line-items`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ lineItems }),
        }),
        {
          maxAttempts: 2,
          onRetry: (error, attempt) => {
            console.warn(`Retrying update line items (attempt ${attempt}):`, error.message);
            showToast.warning(
              "Retrying Update", 
              `Attempting to update line items (${attempt}/2)`
            );
          },
        }
      );

      const result: ApiResponse<Invoice> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to update line items");
      }

      const updatedInvoice = result.data;

      // Update invoice in state
      setState(prev => ({
        ...prev,
        invoices: prev.invoices.map(inv => 
          inv.id === invoiceId ? updatedInvoice : inv
        ),
      }));

      // Dismiss loading toast
      loadingToast.dismiss();

      const totalAmount = formatCurrency(updatedInvoice.total);
      showToast.success(
        "Line Items Updated Successfully", 
        `Invoice for "${customerName}" updated with new total amount ${totalAmount}.`,
        4000
      );

      return updatedInvoice;
    } catch (error) {
      // Dismiss loading toast
      loadingToast.dismiss();
      
      console.error("Error updating line items:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to update line items";
      
      showToast.error("Failed to Update Line Items", errorMessage, {
        label: "Retry",
        onClick: () => updateLineItems(invoiceId, lineItems)
      });
      
      throw error;
    }
  }, [state.invoices, showToast]);

  // Recalculate invoice status based on payments
  const recalculateInvoiceStatus = useCallback(async (invoiceId: number): Promise<Invoice> => {
    try {
      const response = await retryApiCall(
        () => fetch(`/api/invoices/${invoiceId}/recalculate`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
        }),
        {
          maxAttempts: 2,
          onRetry: (error, attempt) => {
            console.warn(`Retrying recalculate status (attempt ${attempt}):`, error.message);
          },
        }
      );

      const result: ApiResponse<Invoice> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to recalculate invoice status");
      }

      const updatedInvoice = result.data;

      // Update invoice in state
      setState(prev => ({
        ...prev,
        invoices: prev.invoices.map(inv => 
          inv.id === invoiceId ? updatedInvoice : inv
        ),
      }));

      return updatedInvoice;
    } catch (error) {
      console.error("Error recalculating invoice status:", error);
      throw error;
    }
  }, []);

  // Enhanced search functionality with better user feedback
  const setSearchQuery = useCallback((query: string) => {
    const trimmedQuery = query.trim();
    
    setState(prev => ({
      ...prev,
      searchQuery: query,
      currentPage: 1, // Reset to first page when searching
    }));

    // Enhanced search feedback
    if (trimmedQuery) {
      if (trimmedQuery.length >= 3) {
        showToast.info(
          "Searching Invoices", 
          `Looking for invoices matching "${trimmedQuery}"`,
          2000
        );
      } else if (trimmedQuery.length > 0) {
        showToast.info(
          "Search Query Too Short", 
          "Enter at least 3 characters to search",
          2000
        );
      }
    } else {
      // Query cleared
      showToast.info(
        "Search Cleared", 
        "Showing all invoices",
        1500
      );
    }
  }, [showToast]);

  // Filter by status
  const setStatusFilter = useCallback((status?: InvoiceStatus) => {
    setState(prev => ({
      ...prev,
      selectedStatus: status,
      currentPage: 1, // Reset to first page when filtering
    }));

    if (status) {
      showToast.info("Filter Applied", `Showing ${status.toLowerCase().replace('_', ' ')} invoices`, 2000);
    } else {
      showToast.info("Filter Cleared", "Showing invoices with all statuses", 1500);
    }
  }, [showToast]);

  // Filter by customer
  const setCustomerFilter = useCallback((customerId?: number) => {
    setState(prev => ({
      ...prev,
      selectedCustomerId: customerId,
      currentPage: 1, // Reset to first page when filtering
    }));

    if (customerId) {
      showToast.info("Filter Applied", "Showing invoices for selected customer", 2000);
    } else {
      showToast.info("Filter Cleared", "Showing invoices for all customers", 1500);
    }
  }, [showToast]);

  // Filter by booking
  const setBookingFilter = useCallback((bookingId?: number) => {
    setState(prev => ({
      ...prev,
      selectedBookingId: bookingId,
      currentPage: 1, // Reset to first page when filtering
    }));

    if (bookingId) {
      showToast.info("Filter Applied", "Showing invoice for selected booking", 2000);
    } else {
      showToast.info("Filter Cleared", "Showing invoices for all bookings", 1500);
    }
  }, [showToast]);

  const setCurrentPage = useCallback((page: number) => {
    setState(prev => ({ ...prev, currentPage: page }));
  }, []);

  const setPageSize = useCallback((size: number) => {
    setState(prev => ({
      ...prev,
      pageSize: size,
      currentPage: 1, // Reset to first page when changing page size
    }));
  }, []);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setState(prev => ({
      ...prev,
      searchQuery: "",
      selectedStatus: undefined,
      selectedCustomerId: undefined,
      selectedBookingId: undefined,
      currentPage: 1,
    }));

    showToast.info("Filters Cleared", "Showing all invoices", 2000);
  }, [showToast]);

  // Utility functions
  const refresh = useCallback(() => {
    fetchInvoices(true);
  }, [fetchInvoices]);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null, paymentsError: null }));
  }, []);

  // Auto-fetch when dependencies change
  useEffect(() => {
    fetchInvoices();
  }, [fetchInvoices]);

  return {
    // State
    invoices: state.invoices,
    loading: state.loading,
    error: state.error,
    totalInvoices: state.totalInvoices,
    totalPages: state.totalPages,
    currentPage: state.currentPage,
    pageSize: state.pageSize,
    searchQuery: state.searchQuery,
    selectedStatus: state.selectedStatus,
    selectedCustomerId: state.selectedCustomerId,
    selectedBookingId: state.selectedBookingId,
    // Payment-specific state
    payments: state.payments,
    paymentsLoading: state.paymentsLoading,
    paymentsError: state.paymentsError,
    
    // Actions
    fetchInvoices,
    fetchInvoicePayments,
    createInvoice,
    updateInvoice,
    deleteInvoice,
    addPayment,
    updatePayment,
    deletePayment,
    updateLineItems,
    recalculateInvoiceStatus,
    setSearchQuery,
    setStatusFilter,
    setCustomerFilter,
    setBookingFilter,
    setCurrentPage,
    setPageSize,
    refresh,
    clearError,
    clearFilters,
  };
}