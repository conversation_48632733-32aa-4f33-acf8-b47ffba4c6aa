# Implementation Plan

- [x] 1. Set up core resource types and validation schemas
  - Add resource types to lib/types.ts following the same patterns as amenity types
  - Implement comprehensive Zod validation schemas in lib/validations/resource.ts with type-specific validation rules
  - Create resource type configuration utilities for dynamic form field management
  - Add resource type display utilities and helper functions
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5, 6.6, 8.1, 8.2_

- [x] 2. Implement resources API endpoints with full CRUD operations
  - Create GET and POST handlers in app/api/resources/route.ts following amenities management patterns
  - Implement individual resource operations in app/api/resources/[id]/route.ts (GET, PUT, DELETE)
  - Add proper error handling, validation, and response formatting consistent with amenities API
  - Include foreign key constraint handling for resources with existing bookings
  - Add support for amenity associations and stage styles relationships
  - _Requirements: 1.1, 2.8, 3.4, 4.4, 5.5, 7.5_

- [x] 3. Create custom hook for resources data management
  - Implement use-resources.ts hook following the exact patterns of use-amenities.ts
  - Add state management for resources list, pagination, search, and type filtering
  - Implement CRUD operations with optimistic updates and error handling
  - Add retry logic and toast notifications for user feedback
  - Include resource type filtering functionality
  - _Requirements: 1.2, 2.9, 3.5, 4.4, 5.1, 5.4_

- [x] 4. Build resources table component with responsive design and filtering
  - Create resources-table.tsx component following amenities-table.tsx patterns
  - Implement responsive design with table view for desktop and card view for mobile
  - Add sorting functionality for name, type, base price, capacity, and creation date columns
  - Include action dropdown with edit and delete options
  - Add resource type filtering dropdown with all resource types
  - Display amenities count with tooltip showing amenity names
  - Add capacity display logic based on resource type (attendees, desks, chairs)
  - Format price display with currency symbol (only IQD is supported)
  - _Requirements: 1.1, 1.4, 1.5, 1.6, 4.1, 8.3_

- [x] 5. Create resource type selector component
  - Build resource-type-selector.tsx component with radio button group
  - Add descriptive text and visual indicators for each resource type
  - Implement form field updates when resource type changes
  - Add validation feedback for resource type selection
  - _Requirements: 2.3, 3.3, 6.1_

- [x] 6. Implement amenities selector component for resource associations
  - Create amenities-selector.tsx component with multi-select checkbox functionality
  - Display available amenities with icons and names
  - Add search/filter functionality for amenities list
  - Include selected amenities counter and visual indicators
  - Implement accessible keyboard navigation
  - _Requirements: 2.7, 7.1, 7.2, 7.4_

- [x] 7. Build stage styles selector component for indoor event halls
  - Create stage-styles-selector.tsx component with checkbox options
  - Add visual representations of stage configurations (Podium and Panel)
  - Show component only when resource type is indoor event hall
  - Implement proper validation for stage styles selection
  - _Requirements: 6.5_

- [x] 8. Implement resource form dialog with dynamic fields
  - Create resource-form-dialog.tsx following amenity-form-dialog.tsx patterns
  - Build dynamic form fields based on selected resource type
  - Integrate resource type selector, amenities selector, and stage styles selector
  - Add form validation using react-hook-form and Zod schemas
  - Implement real-time validation feedback and error display
  - Add loading states and proper form submission handling
  - Include price input with currency formatting and validation
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 2.8, 2.9, 3.1, 3.2, 3.3, 3.4, 8.1, 8.4_

- [x] 9. Create delete confirmation dialog with booking validation
  - Implement delete-confirmation-dialog.tsx following amenities management patterns
  - Add proper confirmation messaging and loading states
  - Handle foreign key constraint errors when resource has existing bookings
  - Display appropriate error messages for resources that cannot be deleted
  - _Requirements: 4.1, 4.2, 4.3, 4.5_

- [x] 10. Build supporting components for resources management
  - Create resources-table-skeleton.tsx for loading states following amenities patterns
  - Implement resource-management-error-boundary.tsx for error recovery
  - Reuse existing search-input.tsx and pagination-controls.tsx components
  - _Requirements: 1.7, 5.1, 5.3_

- [x] 11. Implement main resources page with complete functionality
  - Create app/dashboard/resources/page.tsx following amenities/page.tsx structure
  - Integrate all components and manage dialog states
  - Add search functionality with real-time filtering
  - Implement resource type filtering with dropdown selection
  - Add pagination controls and page size management
  - Include responsive design with proper mobile layout
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 2.1, 3.1, 4.1, 5.1_

- [x] 12. Add comprehensive error handling and user feedback
  - Implement proper error boundaries and fallback UI
  - Add toast notifications for all CRUD operations
  - Handle network errors with retry mechanisms
  - Add proper loading states throughout the application
  - Include specific error handling for resource type validation
  - _Requirements: 2.8, 2.9, 3.4, 4.4, 5.2, 5.4_

- [x] 13. Add mobile optimization and responsive features
  - Implement minimum touch targets for mobile devices
  - Test responsive design across different screen sizes
  - Optimize form layouts for mobile interaction
  - Ensure proper touch interaction for all selectors and buttons
  - _Requirements: 1.6, 2.1, 3.1, 4.1_
