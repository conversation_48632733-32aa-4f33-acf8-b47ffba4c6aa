# Design Document

## Overview

The resource management system will implement full CRUD operations following the exact same architectural patterns as the existing amenities and user management systems. This ensures consistency in code structure, error handling, API design, and user experience across the application.

The system will manage facility resources including event halls, training rooms, meeting rooms, desks, and private offices. Each resource type has specific configuration options, pricing, capacity limits, and can be associated with multiple amenities. The system handles complex relationships between resources, amenities, stage styles, and bookings.

## Architecture

### Component Structure

The resource management will mirror the amenities management architecture:

```
app/dashboard/resources/
├── page.tsx                    # Main resources page (mirrors amenities/page.tsx)
└── components/
    ├── resources-table.tsx     # Table component with responsive design
    ├── resource-form-dialog.tsx # Create/edit modal dialog
    ├── delete-confirmation-dialog.tsx # Delete confirmation modal
    ├── resource-type-selector.tsx # Resource type selection component
    ├── amenities-selector.tsx  # Multi-select amenities component
    ├── stage-styles-selector.tsx # Multi-select stage styles for indoor halls
    ├── search-input.tsx        # Search functionality (reused from amenities)
    ├── pagination-controls.tsx # Pagination (reused from amenities)
    ├── resources-table-skeleton.tsx # Loading skeleton
    └── resource-management-error-boundary.tsx # Error boundary

app/api/resources/
├── route.ts                    # GET (list) and POST (create) endpoints
└── [id]/
    └── route.ts               # GET (single), PUT (update), DELETE endpoints

hooks/
└── use-resources.ts           # Custom hook for resources data management

lib/validations/
└── resource.ts                # Zod validation schemas

lib/types.ts                   # Updated with resource types
```

### Resource Type Configuration System

The system will use dynamic form fields based on resource type:

```typescript
const RESOURCE_TYPE_CONFIG = {
  INDOOR_EVENT_HALL: {
    fields: ['seatingStyle', 'numberOfAttendees', 'stageStyles'],
    required: ['seatingStyle', 'numberOfAttendees'],
    allowMultipleStageStyles: true
  },
  OUTDOOR_EVENT_HALL: {
    fields: ['seatingStyle', 'numberOfAttendees'],
    required: ['seatingStyle', 'numberOfAttendees'],
    allowMultipleStageStyles: false
  },
  TRAINING_ROOM: {
    fields: ['seatingStyle', 'numberOfAttendees'],
    required: ['numberOfAttendees'],
    allowMultipleStageStyles: false
  },
  MEETING_ROOM: {
    fields: ['numberOfAttendees'],
    required: ['numberOfAttendees'],
    allowMultipleStageStyles: false
  },
  DESK: {
    fields: [],
    required: [],
    allowMultipleStageStyles: false
  },
  PRIVATE_OFFICE: {
    fields: ['numberOfDesks', 'numberOfChairs'],
    required: ['numberOfDesks', 'numberOfChairs'],
    allowMultipleStageStyles: false
  }
}
```

## Components and Interfaces

### 1. Main Resources Page (`app/dashboard/resources/page.tsx`)

**Purpose:** Main container component that orchestrates all resource management functionality.

**Key Features:**
- Uses `useResources` hook for state management
- Manages dialog states (create, edit, delete)
- Implements the same responsive design patterns as amenities management
- Provides search, filtering, and pagination functionality
- Handles resource type filtering

**State Management:**
```typescript
interface DialogState {
  resourceForm: {
    open: boolean;
    resource: Resource | null;
    loading: boolean;
  };
  deleteConfirmation: {
    open: boolean;
    resource: Resource | null;
    loading: boolean;
  };
}

interface FilterState {
  resourceType: ResourceType | 'ALL';
  searchQuery: string;
  currentPage: number;
  pageSize: number;
}
```

### 2. Resources Table (`components/resources-table.tsx`)

**Purpose:** Responsive table component that displays resources with sorting, filtering, and actions.

**Key Features:**
- Responsive design: table on desktop, cards on mobile
- Sortable columns (name, type, basePrice, capacity, createdAt)
- Resource type filtering dropdown
- Action dropdown with edit/delete options
- Amenities count display with tooltip showing amenity names
- Capacity display based on resource type
- Price formatting with currency symbol

**Props Interface:**
```typescript
interface ResourcesTableProps {
  resources: Resource[];
  loading: boolean;
  onEdit: (resource: Resource) => void;
  onDelete: (resourceId: number) => void;
  onRefresh: () => void;
  onTypeFilter: (type: ResourceType | 'ALL') => void;
  selectedType: ResourceType | 'ALL';
}
```

### 3. Resource Form Dialog (`components/resource-form-dialog.tsx`)

**Purpose:** Modal dialog for creating and editing resources with dynamic form fields.

**Key Features:**
- Form validation using react-hook-form and Zod
- Dynamic form fields based on selected resource type
- Amenities multi-selector with visual indicators
- Stage styles multi-selector for indoor event halls
- Real-time validation feedback
- Loading states during submission
- Error handling with detailed messages
- Price input with currency formatting

**Props Interface:**
```typescript
interface ResourceFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  resource?: Resource | null;
  onSubmit: (data: ResourceFormData) => Promise<void>;
  loading?: boolean;
  availableAmenities: Amenity[];
}
```

### 4. Resource Type Selector Component

**Purpose:** Component for selecting resource type with descriptions and visual indicators.

**Key Features:**
- Radio button group with resource type options
- Descriptive text for each resource type
- Visual icons for each type
- Triggers form field updates when type changes

### 5. Amenities Selector Component

**Purpose:** Multi-select component for associating amenities with resources.

**Key Features:**
- Checkbox list of available amenities
- Visual amenity icons and names
- Search/filter functionality for amenities
- Selected amenities counter
- Accessible keyboard navigation

### 6. Stage Styles Selector Component

**Purpose:** Multi-select component for indoor event hall stage styles.

**Key Features:**
- Checkbox options for Podium and Panel styles
- Visual representations of stage configurations
- Only visible for indoor event halls

### 7. Custom Hook (`hooks/use-resources.ts`)

**Purpose:** Centralized state management and API interactions for resources.

**Key Features:**
- Follows the same patterns as `use-amenities.ts`
- Optimistic updates for better UX
- Retry logic with exponential backoff
- Toast notifications for user feedback
- Pagination, search, and filtering state management
- Resource type filtering

**Return Interface:**
```typescript
interface UseResourcesReturn {
  // State
  resources: Resource[];
  loading: boolean;
  error: string | null;
  totalResources: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  searchQuery: string;
  selectedType: ResourceType | 'ALL';
  
  // Actions
  fetchResources: (showLoading?: boolean) => Promise<void>;
  createResource: (data: ResourceFormData) => Promise<Resource>;
  updateResource: (id: number, data: ResourceFormData) => Promise<Resource>;
  deleteResource: (id: number) => Promise<void>;
  setSearchQuery: (query: string) => void;
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
  setSelectedType: (type: ResourceType | 'ALL') => void;
  refresh: () => void;
  clearError: () => void;
}
```

## Data Models

### Resource Type Definitions

```typescript
export type ResourceType = 
  | 'INDOOR_EVENT_HALL'
  | 'OUTDOOR_EVENT_HALL'
  | 'TRAINING_ROOM'
  | 'MEETING_ROOM'
  | 'DESK'
  | 'PRIVATE_OFFICE';

export type SeatingStyle = 'CINEMA' | 'ROUND_TABLE';
export type StageStyle = 'PODIUM' | 'PANEL';

export interface Resource {
  id: number;
  name: string;
  type: ResourceType;
  basePrice: number;
  details?: string | null;
  seatingStyle?: SeatingStyle | null;
  numberOfAttendees?: number | null;
  numberOfDesks?: number | null;
  numberOfChairs?: number | null;
  amenities: Amenity[];
  stageStyles: ResourceStageStyle[];
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface ResourceStageStyle {
  id: number;
  resourceId: number;
  style: StageStyle;
  createdAt: Date;
  updatedAt: Date;
}

export interface ResourceFormData {
  name: string;
  type: ResourceType;
  basePrice: number;
  details?: string;
  seatingStyle?: SeatingStyle;
  numberOfAttendees?: number;
  numberOfDesks?: number;
  numberOfChairs?: number;
  amenityIds: number[];
  stageStyles: StageStyle[];
}
```

### API Response Types

```typescript
export type ResourceListResponse = PaginatedResponse<Resource>;

export interface ResourceSearchParams {
  search?: string;
  type?: ResourceType;
  page?: number;
  limit?: number;
}
```

## Error Handling

### Validation Schema (`lib/validations/resource.ts`)

```typescript
export const resourceCreateSchema = z.object({
  name: z.string()
    .min(1, "Resource name is required")
    .max(100, "Resource name must be less than 100 characters")
    .regex(/^[a-zA-Z0-9\s\-_&()]+$/, "Name can only contain letters, numbers, spaces, and basic punctuation")
    .transform(val => val.trim())
    .refine(val => val.length > 0, "Cannot be empty or only spaces"),
  
  type: z.nativeEnum(ResourceType, {
    errorMap: () => ({ message: "Please select a valid resource type" })
  }),
  
  basePrice: z.number()
    .min(0, "Base price must be a positive number")
    .max(999999.99, "Base price cannot exceed 999,999.99"),
  
  details: z.string().max(500, "Details must be less than 500 characters").optional(),
  
  seatingStyle: z.nativeEnum(SeatingStyle).optional(),
  
  numberOfAttendees: z.number()
    .min(1, "Number of attendees must be at least 1")
    .max(10000, "Number of attendees cannot exceed 10,000")
    .optional(),
  
  numberOfDesks: z.number()
    .min(1, "Number of desks must be at least 1")
    .max(100, "Number of desks cannot exceed 100")
    .optional(),
  
  numberOfChairs: z.number()
    .min(1, "Number of chairs must be at least 1")
    .max(500, "Number of chairs cannot exceed 500")
    .optional(),
  
  amenityIds: z.array(z.number()).default([]),
  
  stageStyles: z.array(z.nativeEnum(StageStyle)).default([])
}).refine((data) => {
  // Type-specific validation rules
  const config = RESOURCE_TYPE_CONFIG[data.type];
  
  for (const field of config.required) {
    if (!data[field as keyof typeof data]) {
      return false;
    }
  }
  
  return true;
}, {
  message: "Required fields for this resource type are missing"
});

export const resourceUpdateSchema = resourceCreateSchema;

export const resourceSearchSchema = z.object({
  search: z.string().optional(),
  type: z.nativeEnum(ResourceType).optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10)
});
```

### API Error Handling

The API endpoints will follow the same error handling patterns as amenities management:

1. **Validation Errors (400):** Zod validation failures with detailed field-level errors
2. **Not Found Errors (404):** When resource doesn't exist
3. **Conflict Errors (409):** Unique constraint violations (duplicate names)
4. **Foreign Key Errors (409):** When trying to delete resources with existing bookings
5. **Server Errors (500):** Unexpected errors with generic messages

### Client-Side Error Handling

- Form validation errors displayed inline with fields
- Server errors shown in alert components
- Toast notifications for operation feedback
- Retry mechanisms for network failures
- Error boundaries for component-level error recovery



## Performance Considerations

### Optimization Strategies

1. **Component Memoization:** Use React.memo for expensive components
2. **Callback Optimization:** Use useCallback for event handlers
3. **State Updates:** Implement optimistic updates for better perceived performance
4. **API Caching:** Implement proper cache headers and client-side caching
5. **Pagination:** Limit data fetching to current page only
6. **Lazy Loading:** Load amenities data only when needed
7. **Debounced Search:** Prevent excessive API calls during search typing

### Loading States

1. **Skeleton Loading:** Show skeleton components during initial load
2. **Progressive Loading:** Load critical data first, then secondary data
3. **Optimistic Updates:** Update UI immediately, rollback on errors
4. **Conditional Loading:** Load type-specific data only when relevant

## Security Considerations

### Input Validation

1. **Server-Side Validation:** All inputs validated on the server using Zod schemas
2. **SQL Injection Prevention:** Use Prisma ORM parameterized queries
3. **XSS Prevention:** Sanitize and validate all user inputs
4. **CSRF Protection:** Implement proper CSRF tokens for state-changing operations

### Authorization

1. **Role-Based Access:** Only administrators can manage resources
2. **Session Validation:** Verify user authentication for all operations
3. **Audit Trail:** Track who created/updated each resource
4. **Rate Limiting:** Implement rate limiting for API endpoints

## Accessibility

### Mobile Accessibility

1. **Touch Targets:** Minimum 44px touch targets for mobile
2. **Responsive Design:** Proper scaling and layout on all devices
3. **Reduced Motion:** Respect user preferences for reduced motion