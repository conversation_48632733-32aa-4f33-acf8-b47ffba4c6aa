# Implementation Plan

- [x] 1. Set up catering validation schemas and types
  - Create comprehensive Zod validation schemas for catering CRUD operations with revenue sharing constraints
  - Define TypeScript interfaces for catering data models and form data
  - Implement currency formatting utilities and revenue sharing calculation functions
  - _Requirements: 2.2, 2.5, 3.2, 6.1, 6.3, 7.1, 7.2_

- [x] 2. Enhance catering API endpoints with full CRUD operations
  - Update existing GET and POST endpoints in `/api/catering/route.ts` with proper validation and error handling
  - Create new `/api/catering/[id]/route.ts` file with GET, PUT, and DELETE endpoints
  - Implement booking association checking to prevent deletion of catering offers with existing bookings
  - Add proper error responses and status codes following the same patterns as amenities API
  - _Requirements: 2.4, 3.4, 4.2, 4.5, 5.5, 8.2_

- [x] 3. Create custom hook for catering data management
  - Implement `hooks/use-catering.ts` following the same patterns as `use-amenities.ts`
  - Add state management for catering list, loading states, and error handling
  - Implement CRUD operations with optimistic updates and retry logic
  - Add search and pagination functionality with debounced search
  - Include booking association checking functionality
  - _Requirements: 1.3, 2.6, 3.6, 4.4, 5.3, 5.4_

- [x] 4. Build catering table component with responsive design
  - Create `components/catering-table.tsx` with responsive table/card layout
  - Implement sortable columns for offer name, price per person, and revenue sharing
  - Add proper currency formatting for all monetary values and percentage formatting for revenue shares
  - Include action dropdown with edit and delete options
  - Add empty state handling and booking association indicators
  - _Requirements: 1.1, 1.4, 1.5, 1.7, 4.1, 5.1_

- [x] 5. Create catering form dialog with revenue sharing validation
  - Build `components/catering-form-dialog.tsx` for create and edit operations
  - Implement form validation using react-hook-form with Zod schema integration
  - Create revenue sharing calculator component with real-time 100% validation
  - Add currency input formatting and validation for price per person
  - Include loading states and comprehensive error handling
  - _Requirements: 2.1, 2.3, 2.4, 2.8, 3.1, 3.3, 6.2, 6.4, 7.3_

- [x] 6. Implement delete confirmation dialog with booking constraint handling
  - Create `components/delete-confirmation-dialog.tsx` for catering deletion
  - Add booking association checking before allowing deletion
  - Display appropriate error messages when deletion is prevented due to existing bookings
  - Show booking count information when deletion is blocked
  - _Requirements: 4.1, 4.3, 4.5, 4.6_

- [x] 7. Create catering table skeleton loading component
  - Build `components/catering-table-skeleton.tsx` following the same patterns as amenities
  - Implement skeleton rows that match the actual table structure
  - Add responsive skeleton design for both table and card layouts
  - _Requirements: 1.6, 5.1_

- [x] 8. Build main catering management page
  - Update `app/dashboard/catering/page.tsx` to implement full catering management functionality
  - Integrate all components (table, form dialog, delete dialog, search, pagination)
  - Implement state management using the custom catering hook
  - Add proper error boundary and loading state handling
  - Follow the same layout and styling patterns as amenities management page
  - _Requirements: 1.1, 1.2, 2.1, 3.1, 4.1, 5.1, 5.2_

- [x] 9. Add search and pagination controls
  - Integrate existing search and pagination components with catering management
  - Implement real-time search filtering by offer name
  - Add configurable page size options and proper pagination controls
  - Ensure search and pagination state is properly managed
  - _Requirements: 1.2, 1.3, 5.1_

- [x] 10. Implement error boundary for catering management
  - Create `components/catering-management-error-boundary.tsx`
  - Add proper error recovery and user feedback mechanisms
  - Follow the same error boundary patterns as other management systems
  - _Requirements: 5.2_

- [x] 11. Add comprehensive error handling and user feedback
  - Implement toast notifications for all CRUD operations
  - Add proper validation error display with field-level feedback
  - Include retry mechanisms for failed API calls
  - Add loading states for all async operations
  - _Requirements: 2.5, 3.5, 5.4, 6.6_

- [x] 12. Test and validate revenue sharing calculations
  - Create utility functions for revenue split calculations
  - Test percentage validation and 100% constraint enforcement
  - Validate currency formatting and monetary precision
  - Ensure proper integration with booking system calculations
  - _Requirements: 6.1, 6.3, 6.5, 7.4, 7.5_
