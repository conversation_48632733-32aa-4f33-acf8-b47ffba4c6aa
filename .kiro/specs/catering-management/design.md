# Design Document

## Overview

The catering management system will implement full CRUD operations following the exact same architectural patterns as the existing amenities, resource, and user management systems. This ensures consistency in code structure, error handling, API design, and user experience across the application.

The system will manage catering offers with detailed pricing configurations, revenue sharing between first party and vendor, and integration with the booking system. Each catering offer includes per-person pricing, revenue split percentages, and maintains referential integrity with existing bookings to prevent data inconsistencies.

## Architecture

### Component Structure

The catering management will mirror the amenities and resource management architecture:

```
app/dashboard/catering/
├── page.tsx                    # Main catering page (mirrors amenities/page.tsx)
└── components/
    ├── catering-table.tsx      # Table component with responsive design
    ├── catering-form-dialog.tsx # Create/edit modal dialog
    ├── delete-confirmation-dialog.tsx # Delete confirmation modal
    ├── revenue-sharing-calculator.tsx # Revenue sharing input component
    ├── search-input.tsx        # Search functionality (reused from amenities)
    ├── pagination-controls.tsx # Pagination (reused from amenities)
    ├── catering-table-skeleton.tsx # Loading skeleton
    └── catering-management-error-boundary.tsx # Error boundary

app/api/catering/
├── route.ts                    # GET (list) and POST (create) endpoints (enhanced)
└── [id]/
    └── route.ts               # GET (single), PUT (update), DELETE endpoints

hooks/
└── use-catering.ts            # Custom hook for catering data management

lib/validations/
└── catering.ts                # Zod validation schemas

lib/types.ts                   # Updated with catering types
```

### Revenue Sharing System

The system will implement a revenue sharing calculator that ensures proper financial tracking:

```typescript
interface RevenueSharing {
  firstPartyShare: number;  // Percentage (0-100)
  vendorShare: number;      // Percentage (0-100)
  // Constraint: firstPartyShare + vendorShare = 100
}

const calculateRevenueSplit = (totalAmount: number, sharing: RevenueSharing) => ({
  firstPartyAmount: (totalAmount * sharing.firstPartyShare) / 100,
  vendorAmount: (totalAmount * sharing.vendorShare) / 100
});
```

### Pricing System

The system will handle monetary values with proper precision and formatting:

```typescript
const CURRENCY_CONFIG = {
  symbol: '$',
  decimalPlaces: 2,
  thousandsSeparator: ',',
  decimalSeparator: '.'
};

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};
```

## Components and Interfaces

### 1. Main Catering Page (`app/dashboard/catering/page.tsx`)

**Purpose:** Main container component that orchestrates all catering management functionality.

**Key Features:**
- Uses `useCatering` hook for state management
- Manages dialog states (create, edit, delete)
- Implements the same responsive design patterns as amenities management
- Provides search and pagination functionality
- Displays revenue sharing information clearly
- Handles monetary formatting consistently

**State Management:**
```typescript
interface DialogState {
  cateringForm: {
    open: boolean;
    catering: Catering | null;
    loading: boolean;
  };
  deleteConfirmation: {
    open: boolean;
    catering: Catering | null;
    loading: boolean;
    bookingCount?: number;
  };
}
```

### 2. Catering Table (`components/catering-table.tsx`)

**Purpose:** Responsive table component that displays catering offers with sorting and actions.

**Key Features:**
- Responsive design: table on desktop, cards on mobile
- Sortable columns (offerName, pricePerPerson, firstPartyShare, vendorShare, createdAt)
- Action dropdown with edit/delete options
- Currency formatting for all monetary values
- Percentage formatting for revenue sharing
- Booking association indicators
- Empty state handling with appropriate messaging

**Props Interface:**
```typescript
interface CateringTableProps {
  catering: Catering[];
  loading: boolean;
  onEdit: (catering: Catering) => void;
  onDelete: (cateringId: number) => void;
  onRefresh: () => void;
}
```

### 3. Catering Form Dialog (`components/catering-form-dialog.tsx`)

**Purpose:** Modal dialog for creating and editing catering offers with revenue sharing validation.

**Key Features:**
- Form validation using react-hook-form and Zod
- Revenue sharing calculator with real-time validation
- Currency input formatting and validation
- Percentage input with automatic 100% validation
- Real-time validation feedback
- Loading states during submission
- Error handling with detailed messages
- Visual revenue split preview

**Props Interface:**
```typescript
interface CateringFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  catering?: Catering | null;
  onSubmit: (data: CateringFormData) => Promise<void>;
  loading?: boolean;
}
```

### 4. Revenue Sharing Calculator Component

**Purpose:** Specialized component for configuring and validating revenue sharing percentages.

**Key Features:**
- Dual input fields for first party and vendor shares
- Real-time validation that shares sum to 100%
- Visual indicators for valid/invalid configurations
- Automatic calculation of remaining percentage
- Clear error messages for invalid configurations
- Preview of actual dollar amounts based on sample pricing

**Props Interface:**
```typescript
interface RevenueSharingCalculatorProps {
  firstPartyShare: number;
  vendorShare: number;
  onFirstPartyChange: (value: number) => void;
  onVendorChange: (value: number) => void;
  samplePrice?: number; // For preview calculations
  errors?: {
    firstPartyShare?: string;
    vendorShare?: string;
    total?: string;
  };
}
```

### 5. Custom Hook (`hooks/use-catering.ts`)

**Purpose:** Centralized state management and API interactions for catering offers.

**Key Features:**
- Follows the same patterns as `use-amenities.ts` and `use-resources.ts`
- Optimistic updates for better UX
- Retry logic with exponential backoff
- Toast notifications for user feedback
- Pagination and search state management
- Revenue sharing validation
- Booking association tracking

**Return Interface:**
```typescript
interface UseCateringReturn {
  // State
  catering: Catering[];
  loading: boolean;
  error: string | null;
  totalCatering: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  searchQuery: string;
  
  // Actions
  fetchCatering: (showLoading?: boolean) => Promise<void>;
  createCatering: (data: CateringFormData) => Promise<Catering>;
  updateCatering: (id: number, data: CateringFormData) => Promise<Catering>;
  deleteCatering: (id: number) => Promise<void>;
  checkBookingAssociations: (id: number) => Promise<number>;
  setSearchQuery: (query: string) => void;
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
  refresh: () => void;
  clearError: () => void;
}
```

## Data Models

### Catering Type Definitions

```typescript
export interface Catering {
  id: number;
  offerName: string;
  pricePerPerson: number;
  firstPartyShare: number;  // Percentage (0-100)
  vendorShare: number;      // Percentage (0-100)
  bookings: CateringOnBooking[];
  lineItems: LineItem[];
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface CateringFormData {
  offerName: string;
  pricePerPerson: number;
  firstPartyShare: number;
  vendorShare: number;
}

export interface CateringOnBooking {
  id: number;
  bookingId: number;
  cateringId: number;
  quantity: number;
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface RevenueSplit {
  totalAmount: number;
  firstPartyAmount: number;
  vendorAmount: number;
  firstPartyPercentage: number;
  vendorPercentage: number;
}
```

### API Response Types

```typescript
export type CateringListResponse = PaginatedResponse<Catering>;

export interface CateringSearchParams {
  search?: string;
  page?: number;
  limit?: number;
}

export interface CateringWithBookingCount extends Catering {
  _count: {
    bookings: number;
  };
}
```

## Error Handling

### Validation Schema (`lib/validations/catering.ts`)

```typescript
export const cateringCreateSchema = z.object({
  offerName: z.string()
    .min(1, "Offer name is required")
    .max(100, "Offer name must be less than 100 characters")
    .regex(/^[a-zA-Z0-9\s\-_&()]+$/, "Name can only contain letters, numbers, spaces, and basic punctuation")
    .transform(val => val.trim())
    .refine(val => val.length > 0, "Cannot be empty or only spaces"),
  
  pricePerPerson: z.number()
    .min(0.01, "Price per person must be greater than $0.00")
    .max(9999.99, "Price per person cannot exceed $9,999.99")
    .refine(val => Number.isFinite(val), "Price must be a valid number")
    .transform(val => Math.round(val * 100) / 100), // Ensure 2 decimal places
  
  firstPartyShare: z.number()
    .min(0, "First party share must be at least 0%")
    .max(100, "First party share cannot exceed 100%")
    .refine(val => Number.isInteger(val), "Share must be a whole number"),
  
  vendorShare: z.number()
    .min(0, "Vendor share must be at least 0%")
    .max(100, "Vendor share cannot exceed 100%")
    .refine(val => Number.isInteger(val), "Share must be a whole number")
}).refine((data) => {
  return data.firstPartyShare + data.vendorShare === 100;
}, {
  message: "First party share and vendor share must add up to exactly 100%",
  path: ["vendorShare"] // Show error on vendor share field
});

export const cateringUpdateSchema = cateringCreateSchema;

export const cateringSearchSchema = z.object({
  search: z.string().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10)
});
```

### API Error Handling

The API endpoints will follow the same error handling patterns as amenities and resource management:

1. **Validation Errors (400):** Zod validation failures with detailed field-level errors
2. **Not Found Errors (404):** When catering offer doesn't exist
3. **Conflict Errors (409):** Unique constraint violations (duplicate offer names)
4. **Foreign Key Errors (409):** When trying to delete catering offers with existing bookings
5. **Server Errors (500):** Unexpected errors with generic messages

### Client-Side Error Handling

- Form validation errors displayed inline with fields
- Revenue sharing validation with clear 100% requirement messaging
- Server errors shown in alert components
- Toast notifications for operation feedback
- Retry mechanisms for network failures
- Error boundaries for component-level error recovery
- Specific error handling for booking association constraints



## Performance Considerations

### Optimization Strategies

1. **Component Memoization:** Use React.memo for expensive components
2. **Callback Optimization:** Use useCallback for event handlers
3. **State Updates:** Implement optimistic updates for better perceived performance
4. **API Caching:** Implement proper cache headers and client-side caching
5. **Pagination:** Limit data fetching to current page only
6. **Currency Calculations:** Optimize monetary calculations for performance
7. **Debounced Search:** Prevent excessive API calls during search typing

### Loading States

1. **Skeleton Loading:** Show skeleton components during initial load
2. **Progressive Loading:** Load critical data first, then secondary data
3. **Optimistic Updates:** Update UI immediately, rollback on errors
4. **Conditional Loading:** Load booking association data only when needed

## Security Considerations

### Input Validation

1. **Server-Side Validation:** All inputs validated on the server using Zod schemas
2. **SQL Injection Prevention:** Use Prisma ORM parameterized queries
3. **XSS Prevention:** Sanitize and validate all user inputs
4. **CSRF Protection:** Implement proper CSRF tokens for state-changing operations
5. **Monetary Value Validation:** Strict validation of financial data

### Authorization

1. **Role-Based Access:** Only administrators can manage catering offers
2. **Session Validation:** Verify user authentication for all operations
3. **Audit Trail:** Track who created/updated each catering offer
4. **Rate Limiting:** Implement rate limiting for API endpoints
5. **Financial Data Protection:** Extra security measures for pricing information

## Accessibility

### Mobile Accessibility

1. **Touch Targets:** Minimum 44px touch targets for mobile
2. **Responsive Design:** Proper scaling and layout on all devices
3. **Reduced Motion:** Respect user preferences for reduced motion
4. **Currency Input:** Accessible currency input with proper labeling
5. **Percentage Input:** Clear labeling and validation feedback for revenue sharing



## Integration with Booking System

### Data Relationships

The catering management system maintains proper relationships with the booking system:

```typescript
// Booking association check before deletion
const checkCateringBookings = async (cateringId: number): Promise<number> => {
  const bookingCount = await prisma.cateringOnBooking.count({
    where: { cateringId }
  });
  return bookingCount;
};

// Prevent deletion if bookings exist
const deleteCatering = async (id: number) => {
  const bookingCount = await checkCateringBookings(id);
  if (bookingCount > 0) {
    throw new Error(`Cannot delete catering offer. It is associated with ${bookingCount} booking(s).`);
  }
  // Proceed with deletion
};
```

### Revenue Calculation Integration

The system provides utilities for booking system integration:

```typescript
export const calculateCateringCost = (
  pricePerPerson: number,
  quantity: number,
  firstPartyShare: number,
  vendorShare: number
): RevenueSplit => {
  const totalAmount = pricePerPerson * quantity;
  return {
    totalAmount,
    firstPartyAmount: (totalAmount * firstPartyShare) / 100,
    vendorAmount: (totalAmount * vendorShare) / 100,
    firstPartyPercentage: firstPartyShare,
    vendorPercentage: vendorShare
  };
};
```