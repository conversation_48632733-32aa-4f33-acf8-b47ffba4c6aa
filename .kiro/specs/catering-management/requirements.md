# Requirements Document

## Introduction

This feature implements comprehensive CRUD (Create, Read, Update, Delete) operations for catering management, following the exact same design patterns and architecture as the existing amenities, resource, and user management systems. The catering management system will allow administrators to manage catering offers with pricing details, revenue sharing configurations, and integration with the booking system, providing a consistent user experience across the application.

## Requirements

### Requirement 1

**User Story:** As an administrator, I want to view all catering offers in a paginated table with search functionality, so that I can efficiently browse and locate specific catering options.

#### Acceptance Criteria

1. WHEN the catering page loads THEN the system SHALL display all catering offers in a responsive table format
2. WHEN there are more than 10 catering offers THEN the system SHALL provide pagination controls with configurable page sizes
3. WHEN a user enters a search query THEN the system SHALL filter catering offers by name in real-time
4. WHEN the table is displayed THEN the system SHALL show offer name, price per person, first party share, vendor share, and action buttons
5. WHEN the page is viewed on mobile devices THEN the system SHALL display catering offers in a card format instead of a table
6. WHEN catering offers are loading THEN the system SHALL display appropriate skeleton loading states
7. WHEN displaying pricing information THEN the system SHALL format all monetary values with appropriate currency symbols and decimal places

### Requirement 2

**User Story:** As an administrator, I want to create new catering offers with detailed pricing and revenue sharing configuration, so that I can expand the available catering options.

#### Acceptance Criteria

1. WHEN I click the "Add Catering Offer" button THEN the system SHALL open a modal dialog for catering offer creation
2. WHEN creating a catering offer THEN the system SHALL require offer name, price per person, first party share, and vendor share as mandatory fields
3. WHEN I enter pricing information THEN the system SHALL validate that all prices are positive numbers with appropriate decimal precision
4. WHEN I enter revenue sharing percentages THEN the system SHALL validate that first party share and vendor share add up to 100%
5. WHEN I submit valid catering data THEN the system SHALL create the catering offer and show a success notification
6. WHEN I submit invalid data THEN the system SHALL display appropriate validation errors with specific field-level feedback
7. WHEN the catering offer is created successfully THEN the system SHALL update the catering list without requiring a page refresh
8. WHEN entering monetary values THEN the system SHALL provide proper currency formatting and validation

### Requirement 3

**User Story:** As an administrator, I want to edit existing catering offers, so that I can update pricing, revenue sharing, and offer details when needed.

#### Acceptance Criteria

1. WHEN I click the edit action for a catering offer THEN the system SHALL open a pre-populated edit dialog
2. WHEN editing a catering offer THEN the system SHALL allow modification of offer name, pricing, and revenue sharing percentages
3. WHEN I modify revenue sharing percentages THEN the system SHALL validate that they still add up to 100%
4. WHEN I save valid changes THEN the system SHALL update the catering offer and show a success notification
5. WHEN I save invalid data THEN the system SHALL display appropriate validation errors
6. WHEN the catering offer is updated successfully THEN the system SHALL reflect changes in the catering list immediately
7. WHEN updating pricing THEN the system SHALL maintain proper monetary formatting and precision

### Requirement 4

**User Story:** As an administrator, I want to delete catering offers that are no longer available, so that I can maintain an accurate catering catalog.

#### Acceptance Criteria

1. WHEN I click the delete action for a catering offer THEN the system SHALL show a confirmation dialog
2. WHEN I confirm deletion THEN the system SHALL permanently remove the catering offer and show a success notification
3. WHEN I cancel deletion THEN the system SHALL close the dialog without making changes
4. WHEN a catering offer is deleted successfully THEN the system SHALL remove it from the catering list immediately
5. IF a catering offer has existing bookings THEN the system SHALL prevent deletion and show an appropriate error message explaining the constraint
6. WHEN deletion is prevented due to existing bookings THEN the system SHALL provide information about the number of associated bookings

### Requirement 5

**User Story:** As an administrator, I want the catering management to follow the same design patterns as amenities, resource, and user management, so that I have a consistent experience across the application.

#### Acceptance Criteria

1. WHEN using the catering management interface THEN the system SHALL use the same component structure as amenities and resource management
2. WHEN performing CRUD operations THEN the system SHALL use the same error handling patterns as other management systems
3. WHEN loading data THEN the system SHALL use the same loading states and retry mechanisms as other management systems
4. WHEN displaying notifications THEN the system SHALL use the same toast notification system as other management systems
5. WHEN handling API responses THEN the system SHALL use the same response format and error handling as other management systems
6. WHEN displaying forms THEN the system SHALL use the same form validation and styling patterns as other management systems

### Requirement 6

**User Story:** As an administrator, I want to configure revenue sharing between first party and vendor for each catering offer, so that financial arrangements are properly tracked and managed.

#### Acceptance Criteria

1. WHEN creating or editing a catering offer THEN the system SHALL require both first party share and vendor share percentages
2. WHEN entering revenue sharing percentages THEN the system SHALL validate that both values are between 0 and 100
3. WHEN saving revenue sharing configuration THEN the system SHALL validate that first party share plus vendor share equals exactly 100%
4. WHEN displaying revenue sharing THEN the system SHALL show percentages with appropriate formatting (e.g., "60%" not "0.6")
5. WHEN calculating revenue splits THEN the system SHALL use the configured percentages to determine actual monetary amounts
6. WHEN revenue sharing validation fails THEN the system SHALL display clear error messages explaining the 100% requirement

### Requirement 7

**User Story:** As an administrator, I want to set per-person pricing for catering offers, so that the booking system can calculate total catering costs based on attendee count.

#### Acceptance Criteria

1. WHEN creating or editing a catering offer THEN the system SHALL require a price per person as a positive monetary value
2. WHEN entering per-person pricing THEN the system SHALL validate that the price is a valid monetary amount with appropriate decimal precision
3. WHEN displaying per-person pricing THEN the system SHALL show prices in a consistent currency format
4. WHEN saving pricing information THEN the system SHALL store prices with appropriate precision for monetary calculations
5. WHEN pricing is used in calculations THEN the system SHALL maintain accuracy for financial computations
6. WHEN displaying pricing in lists THEN the system SHALL format all monetary values consistently across the interface

### Requirement 8

**User Story:** As an administrator, I want catering offers to integrate properly with the booking system, so that catering can be associated with bookings and tracked appropriately.

#### Acceptance Criteria

1. WHEN a catering offer exists THEN the system SHALL maintain referential integrity with the booking system
2. WHEN a catering offer is associated with bookings THEN the system SHALL prevent deletion of the offer
3. WHEN displaying catering offers THEN the system SHALL indicate which offers are currently in use by bookings
4. WHEN managing catering offers THEN the system SHALL provide visibility into booking associations
5. WHEN catering offers are updated THEN the system SHALL ensure existing booking relationships remain valid