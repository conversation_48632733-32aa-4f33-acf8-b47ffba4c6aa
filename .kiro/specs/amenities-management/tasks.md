# Implementation Plan

- [x] 1. Set up core amenity types and validation schemas
  - Create amenity types in lib/types.ts following the same patterns as user types
  - Implement Zod validation schemas in lib/validations/amenity.ts with comprehensive validation rules
  - Create icon mapping utilities for AmenityIcon enum to Lucide React icons
  - _Requirements: 6.1, 6.3, 6.4, 6.5_

- [x] 2. Implement amenities API endpoints with full CRUD operations
  - Create GET and POST handlers in app/api/amenities/route.ts following user management patterns
  - Implement individual amenity operations in app/api/amenities/[id]/route.ts (GET, PUT, DELETE)
  - Add proper error handling, validation, and response formatting consistent with user API
  - Include foreign key constraint handling for amenities associated with resources
  - _Requirements: 1.1, 2.4, 3.3, 4.4, 5.5_

- [x] 3. Create custom hook for amenities data management
  - Implement use-amenities.ts hook following the exact patterns of use-users.ts
  - Add state management for amenities list, pagination, search, and loading states
  - Implement CRUD operations with optimistic updates and error handling
  - Add retry logic and toast notifications for user feedback
  - _Requirements: 1.2, 2.6, 3.5, 4.4, 5.1, 5.4_

- [x] 4. Build amenities table component with responsive design
  - Create amenities-table.tsx component following users-table.tsx patterns
  - Implement responsive design with table view for desktop and card view for mobile
  - Add sorting functionality for name, icon, and creation date columns
  - Include action dropdown with edit and delete options
  - Add icon display with visual representation using the icon mapping
  - _Requirements: 1.1, 1.4, 1.5, 4.1, 6.2_

- [x] 5. Implement amenity form dialog for create and edit operations
  - Create amenity-form-dialog.tsx following user-form-dialog.tsx patterns
  - Build icon selector component with visual preview and grid layout
  - Add form validation using react-hook-form and Zod schemas
  - Implement real-time validation feedback and error display
  - Add loading states and proper form submission handling
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 3.1, 3.2, 3.3, 3.4, 6.4_

- [x] 6. Create delete confirmation dialog
  - Implement delete-confirmation-dialog.tsx following user management patterns
  - Add proper confirmation messaging and loading states
  - Handle foreign key constraint errors when amenity is associated with resources
  - _Requirements: 4.1, 4.2, 4.3, 4.5_

- [x] 7. Build supporting components for amenities management
  - Create amenities-table-skeleton.tsx for loading states following user patterns
  - Implement amenity-management-error-boundary.tsx for error recovery
  - Reuse existing search-input.tsx and pagination-controls.tsx components
  - _Requirements: 1.6, 5.1, 5.3_

- [x] 8. Implement main amenities page with complete functionality
  - Create app/dashboard/amenities/page.tsx following users/page.tsx structure
  - Integrate all components and manage dialog states
  - Add search functionality with real-time filtering
  - Implement pagination controls and page size management
  - Add responsive design with proper mobile layout
  - _Requirements: 1.1, 1.2, 1.3, 1.5, 2.1, 3.1, 4.1, 5.1_

- [x] 9. Add comprehensive error handling and user feedback
  - Implement proper error boundaries and fallback UI
  - Add toast notifications for all CRUD operations
  - Handle network errors with retry mechanisms
  - Add proper loading states throughout the application
  - _Requirements: 2.4, 2.5, 3.4, 4.4, 5.2, 5.4_

- [x] 10. Add accessibility features and mobile optimization
  - Implement minimum touch targets for mobile devices
  - Test responsive design across different screen sizes
  - _Requirements: 1.5, 2.1, 3.1, 4.1_
