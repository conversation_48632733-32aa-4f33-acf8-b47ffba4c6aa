# Design Document

## Overview

The amenities management system will implement full CRUD operations following the exact same architectural patterns as the existing user management system. This ensures consistency in code structure, error handling, API design, and user experience across the application.

The system will manage facility amenities with visual icons, providing administrators with an intuitive interface to maintain the amenities catalog. Each amenity will have a unique name and an associated icon from a predefined set of AmenityIcon enum values.

## Architecture

### Component Structure

The amenities management will mirror the user management architecture:

```
app/dashboard/amenities/
├── page.tsx                    # Main amenities page (mirrors users/page.tsx)
└── components/
    ├── amenities-table.tsx     # Table component with responsive design
    ├── amenity-form-dialog.tsx # Create/edit modal dialog
    ├── delete-confirmation-dialog.tsx # Delete confirmation modal
    ├── search-input.tsx        # Search functionality (reused from users)
    ├── pagination-controls.tsx # Pagination (reused from users)
    ├── amenities-table-skeleton.tsx # Loading skeleton
    └── amenity-management-error-boundary.tsx # Error boundary

app/api/amenities/
├── route.ts                    # GET (list) and POST (create) endpoints
└── [id]/
    └── route.ts               # GET (single), PUT (update), DELETE endpoints

hooks/
└── use-amenities.ts           # Custom hook for amenities data management

lib/validations/
└── amenity.ts                 # Zod validation schemas

lib/types.ts                   # Updated with amenity types
```

### Icon Management System

The system will use a mapping between AmenityIcon enum values and Lucide React icons:

```typescript
const AMENITY_ICON_MAP = {
  PROJECTOR: Projector,
  WHITEBOARD: Presentation,
  SMARTBOARD: Monitor,
  TABLE: Table,
  WIFI: Wifi,
  AIR_CONDITIONER: Wind,
  TV: Tv,
  MICROPHONE: Mic,
  SPEAKER: Volume2,
  STAGE: Theater,
  COFFEE_MACHINE: Coffee,
  WATER_DISPENSER: Droplets,
  CATERING: UtensilsCrossed,
  SECURITY: Shield,
  PARKING: Car
}
```

## Components and Interfaces

### 1. Main Amenities Page (`app/dashboard/amenities/page.tsx`)

**Purpose:** Main container component that orchestrates all amenities management functionality.

**Key Features:**
- Uses `useAmenities` hook for state management
- Manages dialog states (create, edit, delete)
- Implements the same responsive design patterns as user management
- Provides search and pagination functionality

**State Management:**
```typescript
interface DialogState {
  amenityForm: {
    open: boolean;
    amenity: Amenity | null;
    loading: boolean;
  };
  deleteConfirmation: {
    open: boolean;
    amenity: Amenity | null;
    loading: boolean;
  };
}
```

### 2. Amenities Table (`components/amenities-table.tsx`)

**Purpose:** Responsive table component that displays amenities with sorting and actions.

**Key Features:**
- Responsive design: table on desktop, cards on mobile
- Sortable columns (name, icon, createdAt)
- Action dropdown with edit/delete options
- Icon display with visual representation
- Empty state handling with appropriate messaging

**Props Interface:**
```typescript
interface AmenitiesTableProps {
  amenities: Amenity[];
  loading: boolean;
  onEdit: (amenity: Amenity) => void;
  onDelete: (amenityId: number) => void;
  onRefresh: () => void;
}
```

### 3. Amenity Form Dialog (`components/amenity-form-dialog.tsx`)

**Purpose:** Modal dialog for creating and editing amenities.

**Key Features:**
- Form validation using react-hook-form and Zod
- Icon selector with visual preview
- Real-time validation feedback
- Loading states during submission
- Error handling with detailed messages

**Props Interface:**
```typescript
interface AmenityFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  amenity?: Amenity | null;
  onSubmit: (data: AmenityFormData) => Promise<void>;
  loading?: boolean;
}
```

### 4. Icon Selector Component

**Purpose:** Custom component for selecting amenity icons with visual preview.

**Key Features:**
- Grid layout of available icons
- Visual preview of selected icon
- Search/filter functionality for icons
- Accessible keyboard navigation

### 5. Custom Hook (`hooks/use-amenities.ts`)

**Purpose:** Centralized state management and API interactions for amenities.

**Key Features:**
- Follows the same patterns as `use-users.ts`
- Optimistic updates for better UX
- Retry logic with exponential backoff
- Toast notifications for user feedback
- Pagination and search state management

**Return Interface:**
```typescript
interface UseAmenitiesReturn {
  // State
  amenities: Amenity[];
  loading: boolean;
  error: string | null;
  totalAmenities: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  searchQuery: string;
  
  // Actions
  fetchAmenities: (showLoading?: boolean) => Promise<void>;
  createAmenity: (data: AmenityFormData) => Promise<Amenity>;
  updateAmenity: (id: number, data: AmenityFormData) => Promise<Amenity>;
  deleteAmenity: (id: number) => Promise<void>;
  setSearchQuery: (query: string) => void;
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
  refresh: () => void;
  clearError: () => void;
}
```

## Data Models

### Amenity Type Definition

```typescript
export interface Amenity {
  id: number;
  name: string;
  icon: AmenityIcon;
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface AmenityFormData {
  name: string;
  icon: AmenityIcon;
}

export type AmenityIcon = 
  | 'PROJECTOR'
  | 'WHITEBOARD'
  | 'SMARTBOARD'
  | 'TABLE'
  | 'WIFI'
  | 'AIR_CONDITIONER'
  | 'TV'
  | 'MICROPHONE'
  | 'SPEAKER'
  | 'STAGE'
  | 'COFFEE_MACHINE'
  | 'WATER_DISPENSER'
  | 'CATERING'
  | 'SECURITY'
  | 'PARKING';
```

### API Response Types

```typescript
export type AmenityListResponse = PaginatedResponse<Amenity>;

export interface AmenitySearchParams {
  search?: string;
  page?: number;
  limit?: number;
}
```

## Error Handling

### Validation Schema (`lib/validations/amenity.ts`)

```typescript
export const amenityCreateSchema = z.object({
  name: z.string()
    .min(1, "Amenity name is required")
    .max(100, "Amenity name must be less than 100 characters")
    .regex(/^[a-zA-Z0-9\s\-_&()]+$/, "Name can only contain letters, numbers, spaces, and basic punctuation")
    .transform(val => val.trim())
    .refine(val => val.length > 0, "Cannot be empty or only spaces"),
  icon: z.nativeEnum(AmenityIcon, {
    errorMap: () => ({ message: "Please select a valid icon" })
  })
});

export const amenityUpdateSchema = amenityCreateSchema;

export const amenitySearchSchema = z.object({
  search: z.string().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10)
});
```

### API Error Handling

The API endpoints will follow the same error handling patterns as user management:

1. **Validation Errors (400):** Zod validation failures with detailed field-level errors
2. **Not Found Errors (404):** When amenity doesn't exist
3. **Conflict Errors (409):** Unique constraint violations (duplicate names)
4. **Foreign Key Errors (409):** When trying to delete amenities associated with resources
5. **Server Errors (500):** Unexpected errors with generic messages

### Client-Side Error Handling

- Form validation errors displayed inline with fields
- Server errors shown in alert components
- Toast notifications for operation feedback
- Retry mechanisms for network failures
- Error boundaries for component-level error recovery

## Testing Strategy

### Unit Tests

1. **Validation Schemas:** Test all validation rules and edge cases
2. **Custom Hook:** Test state management, API calls, and error handling
3. **Components:** Test rendering, user interactions, and prop handling
4. **Utility Functions:** Test icon mapping and helper functions

### Integration Tests

1. **API Endpoints:** Test CRUD operations with various scenarios
2. **Form Submission:** Test end-to-end form validation and submission
3. **Search and Pagination:** Test filtering and pagination functionality
4. **Error Scenarios:** Test error handling and recovery

### E2E Tests

1. **Complete CRUD Flow:** Create, read, update, delete amenities
2. **Search Functionality:** Test search with various queries
3. **Responsive Design:** Test mobile and desktop layouts
4. **Error Recovery:** Test error scenarios and user recovery paths

## Performance Considerations

### Optimization Strategies

1. **Component Memoization:** Use React.memo for expensive components
2. **Callback Optimization:** Use useCallback for event handlers
3. **State Updates:** Implement optimistic updates for better perceived performance
4. **API Caching:** Implement proper cache headers and client-side caching
5. **Pagination:** Limit data fetching to current page only
6. **Icon Loading:** Lazy load icons and implement icon sprite optimization

### Loading States

1. **Skeleton Loading:** Show skeleton components during initial load
2. **Progressive Loading:** Load critical data first, then secondary data
3. **Optimistic Updates:** Update UI immediately, rollback on errors
4. **Debounced Search:** Prevent excessive API calls during search typing

## Security Considerations

### Input Validation

1. **Server-Side Validation:** All inputs validated on the server using Zod schemas
2. **SQL Injection Prevention:** Use Prisma ORM parameterized queries
3. **XSS Prevention:** Sanitize and validate all user inputs
4. **CSRF Protection:** Implement proper CSRF tokens for state-changing operations

### Authorization

1. **Role-Based Access:** Only administrators can manage amenities
2. **Session Validation:** Verify user authentication for all operations
3. **Audit Trail:** Track who created/updated each amenity
4. **Rate Limiting:** Implement rate limiting for API endpoints

## Accessibility

### Mobile Accessibility

1. **Touch Targets:** Minimum 44px touch targets for mobile
2. **Responsive Design:** Proper scaling and layout on all devices
4. **Reduced Motion:** Respect user preferences for reduced motion