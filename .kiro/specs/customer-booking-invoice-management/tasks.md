# Implementation Plan

- [x] 1. Set up type definitions and validation schemas
  - Create comprehensive TypeScript interfaces for Customer, Booking, Invoice, and Payment entities
  - Implement Zod validation schemas for all form data and API requests
  - Add type definitions to lib/types.ts following existing patterns
  - _Requirements: 1.1, 2.1, 6.1, 7.1, 9.2, 9.5_

- [x] 2. Enhance API routes with proper error handling and validation
- [x] 2.1 Enhance customers API route
  - Update app/api/customers/route.ts to follow established patterns from users/amenities APIs
  - Add comprehensive error handling, validation, and pagination support
  - Implement search functionality and proper response formatting
  - _Requirements: 1.1, 1.2, 1.3, 9.2, 9.5_

- [x] 2.2 Enhance bookings API route
  - Update app/api/bookings/route.ts with proper validation and error handling
  - Add support for resource and catering relationships in booking creation
  - Implement calendar data formatting for ilamy calendar integration
  - _Requirements: 3.1, 4.1, 4.4, 4.5, 4.6, 12.4_

- [x] 2.3 Enhance invoices API route
  - Update app/api/invoices/route.ts with comprehensive CRUD operations
  - Add line item management and payment status calculations
  - Implement proper relationship handling with bookings and payments
  - _Requirements: 6.1, 6.2, 6.3, 7.1, 7.4, 7.5_

- [x] 2.4 Create individual resource API routes
  - Implement app/api/customers/[id]/route.ts for individual customer operations
  - Implement app/api/bookings/[id]/route.ts for individual booking operations
  - Implement app/api/invoices/[id]/route.ts for individual invoice operations
  - Add proper validation for delete operations to maintain referential integrity
  - _Requirements: 2.8, 2.9, 4.7, 4.9, 7.7, 11.1, 11.2, 11.3_

- [x] 2.5 Create invoice generation API route
  - Implement app/api/bookings/[id]/invoice/route.ts for one-click invoice creation
  - Add logic to generate line items from booking resources and catering
  - Implement catering offer reference and revenue sharing calculations
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5, 5.6, 8.1, 8.2, 8.3_

- [x] 2.6 Create payments API route
  - Implement app/api/invoices/[id]/payments/route.ts for payment management
  - Add payment recording with automatic invoice status updates
  - Implement payment validation and balance calculations
  - _Requirements: 10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 10.8_

- [x] 3. Create custom hooks for data management
- [x] 3.1 Implement useCustomers hook
  - Create hooks/use-customers.ts following the pattern from use-users.ts
  - Add CRUD operations, search, pagination, and error handling
  - Implement optimistic updates and loading states
  - _Requirements: 1.1, 1.2, 1.3, 2.7, 2.8, 9.1, 9.3_

- [x] 3.2 Implement useBookings hook
  - Create hooks/use-bookings.ts with booking-specific operations
  - Add calendar data formatting and event management
  - Implement resource and catering relationship handling
  - _Requirements: 3.1, 4.7, 4.9, 12.4, 12.5_

- [x] 3.3 Implement useInvoices hook
  - Create hooks/use-invoices.ts with invoice and payment management
  - Add line item management and status calculations
  - Implement payment tracking and balance calculations
  - _Requirements: 6.1, 6.2, 7.7, 10.3, 10.4, 10.5_

- [x] 4. Create customer management components
- [x] 4.1 Create customer list page
  - Implement app/dashboard/customers/page.tsx following the pattern from users page
  - Add responsive table with search, pagination, and action buttons
  - Implement mobile card layout and loading states
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 9.1_

- [x] 4.2 Create customer table component
  - Implement app/dashboard/customers/components/customers-table.tsx
  - Add responsive design with proper column handling
  - Implement action buttons for edit and delete operations
  - _Requirements: 1.4, 1.5, 1.7, 9.1, 9.6_

- [x] 4.3 Create customer form components
  - Implement customer form dialog for create/edit operations
  - Add comprehensive form validation and error handling
  - Create customer information form with all required fields
  - _Requirements: 2.5, 2.8, 2.9, 9.6_

- [x] 4.4 Create customer standalone pages
  - Implement standalone create customer page with single tab
  - Implement standalone edit customer page with Customer Information and Invoices tabs
  - Add proper navigation and form submission handling
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.6, 2.10_

- [x] 5. Create booking management components
- [x] 5.1 Create booking calendar page
  - Implement app/dashboard/bookings/page.tsx with ilamy calendar integration
  - Add calendar event display with booking information
  - Implement event click handlers and booking details popover
  - _Requirements: 3.1, 3.2, 3.3, 3.6, 3.7, 12.1, 12.2, 12.3_

- [x] 5.2 Integrate ilamy calendar library
  - Install and configure ilamy calendar library
  - Create BookingCalendar component with proper event formatting
  - Implement calendar navigation and responsive design
  - _Requirements: 12.1, 12.2, 12.6, 12.7_

- [x] 5.3 Create booking form components
  - Implement booking form with customer selection dropdown
  - Add resource multi-select interface with available resources
  - Create catering selection with quantity management
  - _Requirements: 4.2, 4.4, 4.5, 4.6, 9.6_

- [x] 5.4 Create booking standalone pages
  - Implement standalone create booking page with Booking Information and Invoice tabs
  - Implement standalone edit booking page with proper data pre-population
  - Add form validation and submission handling
  - _Requirements: 4.1, 4.3, 4.7, 4.8, 4.9_

- [ ] 6. Create invoice management components
- [x] 6.1 Create invoice list page
  - Implement app/dashboard/invoices/page.tsx following established patterns
  - Add responsive table with financial data formatting
  - Implement search by customer name and invoice ID
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5, 6.6, 6.7_

- [x] 6.2 Create invoice table component
  - Implement invoices table with proper monetary formatting
  - Add status indicators and payment information display
  - Create responsive design with mobile card layout
  - _Requirements: 6.4, 6.5, 6.7, 9.1, 9.6_

- [x] 6.3 Create invoice form components
  - Implement invoice form with booking selection
  - Add line item management with add/edit/remove functionality
  - Create total calculations and validation
  - _Requirements: 7.4, 7.5, 7.6, 7.8_

- [x] 6.4 Create invoice standalone pages
  - Implement standalone create invoice page with Invoice Details and Payments tabs
  - Implement standalone edit invoice page with proper data pre-population
  - Add form validation and submission handling
  - _Requirements: 7.1, 7.2, 7.3, 7.7, 7.8_

- [x] 7. Implement payment management functionality
- [x] 7.1 Create payment components
  - Implement payment form dialog for recording payments
  - Add payment method selection and validation
  - Create payment list display with status and details
  - _Requirements: 10.1, 10.2, 10.7, 10.8_

- [x] 7.2 Implement payment status calculations
  - Add automatic invoice status updates when payments are recorded
  - Implement balance calculations and remaining amount display
  - Add validation to prevent overpayments
  - _Requirements: 10.3, 10.4, 10.5, 10.6, 10.8_

- [x] 8. Implement one-click invoice generation
- [x] 8.1 Create invoice generation logic
  - Implement automatic line item creation from booking resources
  - Add catering line items with proper offer references
  - Calculate totals and set initial invoice status
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5, 5.6_

- [x] 8.2 Implement catering integration
  - Add proper catering offer references in line items
  - Implement revenue sharing calculations and display
  - Ensure referential integrity with catering offers
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5, 8.6, 8.7_

- [x] 9. Add data integrity and validation
- [x] 9.1 Implement referential integrity checks
  - Add validation to prevent customer deletion with existing bookings
  - Implement booking deletion prevention when invoice exists
  - Add invoice deletion prevention when payments exist
  - _Requirements: 11.1, 11.2, 11.3_

- [x] 9.2 Add relationship maintenance
  - Ensure proper relationship updates when entities are modified
  - Implement cascading updates for related data
  - Add accurate count displays for related entities
  - _Requirements: 11.4, 11.5, 11.6, 11.7_

- [x] 10. Create error boundaries and loading states
- [x] 10.1 Implement error boundaries
  - Create error boundary components following existing patterns
  - Add retry mechanisms and user-friendly error messages
  - Implement proper error logging and monitoring
  - _Requirements: 9.2, 9.4_

- [x] 10.2 Add loading states and skeletons
  - Create skeleton components for all table views
  - Implement loading states for form submissions
  - Add proper loading indicators for calendar data
  - _Requirements: 1.6, 6.6, 9.3_

- [x] 11. Add navigation and routing
- [x] 11.1 Update dashboard navigation
  - Add customers, bookings, and invoices to dashboard sidebar
  - Implement proper routing between list and standalone pages
  - _Requirements: 9.7_

- [x] 11.2 Implement page transitions
  - Add proper navigation between create/edit pages and list views
  - Implement success notifications and redirect handling
  - Add confirmation dialogs for destructive actions
  - _Requirements: 2.7, 4.7, 7.7, 9.4_

- [x] 12. Testing and validation
- [x] 12.1 Create unit tests
  - Write tests for all validation schemas
  - Test custom hooks with various scenarios
  - Add tests for utility functions and calculations
  - _Requirements: 9.2, 9.5_

- [x] 12.2 Add integration tests
  - Test API routes with various input scenarios
  - Test database relationships and constraints
  - Verify calendar integration and event formatting
  - _Requirements: 12.1, 12.2, 12.3_

- [x] 13. Final integration and polish
- [x] 13.1 End-to-end workflow testing
  - Test complete customer creation to invoice payment workflow
  - Verify one-click invoice generation from bookings
  - Test calendar booking creation and management
  - _Requirements: 5.8, 12.4, 12.5_

- [x] 13.2 UI/UX consistency review
  - Ensure all components follow established design patterns
  - Verify responsive design across all screen sizes
  - _Requirements: 9.1, 9.6_

- [x] 13.3 Performance optimization
  - Optimize database queries and API response times
  - Implement proper caching strategies
  - Test calendar performance with large datasets
  - _Requirements: 12.5, 12.6_
