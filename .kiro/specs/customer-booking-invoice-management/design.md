# Design Document

## Overview

The Customer, Booking, and Invoice Management system extends the existing application architecture to provide comprehensive management capabilities for customer relationships, booking scheduling, and financial transactions. The system follows established patterns from the existing amenities, resources, and catering management modules while introducing enhanced functionality for complex business workflows.

The design emphasizes consistency with existing UI patterns, robust data relationships, and efficient calendar-based booking visualization using the ilamy calendar library. The system supports standalone create/update pages with tabbed interfaces, one-click invoice generation from bookings, and integrated payment tracking.

## Architecture

### System Components

The system follows the established Next.js App Router architecture with the following key components:

1. **Frontend Components**
   - Page components for list views (customers, bookings, invoices)
   - Standalone create/edit pages with tabbed interfaces
   - Calendar component for booking visualization
   - Reusable UI components following existing patterns

2. **API Layer**
   - RESTful API routes following existing patterns
   - Enhanced error handling and validation
   - Transaction support for complex operations

3. **Data Layer**
   - Prisma ORM with existing database schema
   - Referential integrity enforcement
   - Audit trail support

4. **Business Logic**
   - Custom hooks for data management
   - Invoice generation algorithms
   - Payment status calculation

### Integration Points

- **Existing Management Systems**: Follows patterns from users, amenities, resources, and catering management
- **Calendar Library**: Integration with ilamy calendar for booking visualization
- **Database**: Extends existing Prisma schema with proper relationships
- **Authentication**: Uses existing authentication patterns

## Components and Interfaces

### Page Components

#### Customer Management
```typescript
// app/dashboard/customers/page.tsx
- CustomerListPage: Main list view with search and pagination
- CustomerCreatePage: Standalone create page with form
- CustomerEditPage: Standalone edit page with tabs (Customer Info, Invoices)
```

#### Booking Management
```typescript
// app/dashboard/bookings/page.tsx
- BookingCalendarPage: Calendar view using ilamy library
- BookingCreatePage: Standalone create page with form
- BookingEditPage: Standalone edit page with tabs (Booking Info, Invoice)
```

#### Invoice Management
```typescript
// app/dashboard/invoices/page.tsx
- InvoiceListPage: Main list view with search and pagination
- InvoiceCreatePage: Standalone create page with form
- InvoiceEditPage: Standalone edit page with tabs (Invoice Details, Payments)
```

### Reusable Components

Following the established component structure:

```typescript
// Table Components
- CustomersTable: Responsive table with actions
- InvoicesTable: Responsive table with financial data
- CustomersTableSkeleton: Loading states
- InvoicesTableSkeleton: Loading states

// Form Components
- CustomerFormDialog: Customer creation/editing
- BookingFormDialog: Booking creation/editing
- InvoiceFormDialog: Invoice creation/editing
- PaymentFormDialog: Payment recording

// Calendar Components
- BookingCalendar: ilamy calendar integration
- BookingEventPopover: Event details display

// Utility Components
- DeleteConfirmationDialog: Consistent deletion confirmation
- SearchInput: Reused from existing components
- PaginationControls: Reused from existing components
```

### Custom Hooks

Following the established hook patterns:

```typescript
// hooks/use-customers.ts
export const useCustomers = () => {
  // Customer CRUD operations
  // Search and pagination
  // Error handling
}

// hooks/use-bookings.ts
export const useBookings = () => {
  // Booking CRUD operations
  // Calendar data formatting
  // Resource and catering integration
}

// hooks/use-invoices.ts
export const useInvoices = () => {
  // Invoice CRUD operations
  // Payment management
  // Status calculations
}
```

### API Routes

Following the established API patterns:

```typescript
// app/api/customers/route.ts
GET /api/customers - List customers with search/pagination
POST /api/customers - Create customer

// app/api/customers/[id]/route.ts
GET /api/customers/[id] - Get customer details
PUT /api/customers/[id] - Update customer
DELETE /api/customers/[id] - Delete customer (with validation)

// app/api/bookings/route.ts
GET /api/bookings - List bookings with filtering
POST /api/bookings - Create booking

// app/api/bookings/[id]/route.ts
GET /api/bookings/[id] - Get booking details
PUT /api/bookings/[id] - Update booking
DELETE /api/bookings/[id] - Delete booking (with validation)

// app/api/bookings/[id]/invoice/route.ts
POST /api/bookings/[id]/invoice - Generate invoice from booking

// app/api/invoices/route.ts
GET /api/invoices - List invoices with search/pagination
POST /api/invoices - Create invoice

// app/api/invoices/[id]/route.ts
GET /api/invoices/[id] - Get invoice details
PUT /api/invoices/[id] - Update invoice
DELETE /api/invoices/[id] - Delete invoice (with validation)

// app/api/invoices/[id]/payments/route.ts
GET /api/invoices/[id]/payments - List payments for invoice
POST /api/invoices/[id]/payments - Add payment to invoice
```

## Data Models

### Enhanced Type Definitions

```typescript
// Customer Types
export interface Customer {
  id: number
  name: string
  email: string
  phoneNumber?: string | null
  companyName?: string | null
  specialization?: string | null
  industry?: string | null
  website?: string | null
  linkedIn?: string | null
  socialMedia?: string | null
  notes?: string | null
  bookings: Booking[]
  createdAt: Date
  updatedAt: Date
  createdById?: number | null
  updatedById?: number | null
}

export interface CustomerFormData {
  name: string
  email: string
  phoneNumber?: string
  companyName?: string
  specialization?: string
  industry?: string
  website?: string
  linkedIn?: string
  socialMedia?: string
  notes?: string
}

// Booking Types
export type BookingStatus = 'PENDING' | 'CONFIRMED' | 'CANCELLED'

export interface Booking {
  id: number
  customer: Customer
  customerId: number
  resources: Resource[]
  status: BookingStatus
  start: Date
  end: Date
  invoice?: Invoice | null
  caterings: CateringOnBooking[]
  createdAt: Date
  updatedAt: Date
  createdById?: number | null
  updatedById?: number | null
}

export interface BookingFormData {
  customerId: number
  resourceIds: number[]
  status: BookingStatus
  start: Date
  end: Date
  caterings: {
    cateringId: number
    quantity: number
  }[]
}

// Calendar Event Type for ilamy integration
export interface CalendarEvent {
  id: string
  title: string
  start: Date
  end: Date
  backgroundColor?: string
  borderColor?: string
  textColor?: string
  extendedProps: {
    bookingId: number
    customerName: string
    status: BookingStatus
    resourceNames: string[]
  }
}

// Invoice Types
export type InvoiceStatus = 'PENDING' | 'PARTIALLY_PAID' | 'PAID' | 'CANCELLED'

export interface Invoice {
  id: number
  booking: Booking
  bookingId: number
  lineItems: LineItem[]
  payments: Payment[]
  status: InvoiceStatus
  total: number
  paid: number
  pdfUrl?: string | null
  createdAt: Date
  updatedAt: Date
  createdById?: number | null
  updatedById?: number | null
}

export interface InvoiceFormData {
  bookingId: number
  lineItems: {
    description: string
    amount: number
    quantity: number
    isCatering: boolean
    cateringId?: number
  }[]
}

// Payment Types
export type PaymentMethod = 'CASH' | 'CREDIT_CARD' | 'DEBIT_CARD' | 'BANK_TRANSFER' | 'CHECK' | 'ONLINE' | 'OTHER'
export type PaymentStatus = 'PENDING' | 'COMPLETED' | 'FAILED' | 'REFUNDED'

export interface Payment {
  id: number
  invoice: Invoice
  invoiceId: number
  amount: number
  method: PaymentMethod
  status: PaymentStatus
  reference?: string | null
  notes?: string | null
  paidAt?: Date | null
  createdAt: Date
  updatedAt: Date
  createdById?: number | null
  updatedById?: number | null
}

export interface PaymentFormData {
  amount: number
  method: PaymentMethod
  reference?: string
  notes?: string
  paidAt?: Date
}
```

### Database Relationships

The system leverages existing Prisma schema with the following key relationships:

1. **Customer → Bookings**: One-to-many relationship
2. **Booking → Customer**: Many-to-one relationship
3. **Booking → Resources**: Many-to-many relationship
4. **Booking → Invoice**: One-to-one relationship
5. **Booking → Caterings**: Many-to-many through CateringOnBooking
6. **Invoice → LineItems**: One-to-many relationship
7. **Invoice → Payments**: One-to-many relationship
8. **LineItem → Catering**: Many-to-one relationship (optional)

## Error Handling

### Validation Strategy

Following the established validation patterns:

```typescript
// lib/validations/customer.ts
export const customerCreateSchema = z.object({
  name: z.string().min(1, "Name is required").max(255),
  email: z.string().email("Invalid email format"),
  phoneNumber: z.string().optional(),
  companyName: z.string().optional(),
  // ... other fields
})

// lib/validations/booking.ts
export const bookingCreateSchema = z.object({
  customerId: z.number().positive(),
  resourceIds: z.array(z.number().positive()).min(1),
  status: z.enum(['PENDING', 'CONFIRMED', 'CANCELLED']),
  start: z.date(),
  end: z.date(),
  // ... validation for date ranges, conflicts
})

// lib/validations/invoice.ts
export const invoiceCreateSchema = z.object({
  bookingId: z.number().positive(),
  lineItems: z.array(z.object({
    description: z.string().min(1),
    amount: z.number().positive(),
    quantity: z.number().positive(),
    isCatering: z.boolean(),
    cateringId: z.number().optional()
  })).min(1)
})
```

### Error Handling Patterns

Following the established error handling from existing management systems:

1. **API Level**: Consistent error response format with proper HTTP status codes
2. **Component Level**: Error boundaries for graceful failure handling
3. **Hook Level**: Error state management with retry mechanisms
4. **Validation Level**: Zod schema validation with user-friendly messages

### Data Integrity Constraints

1. **Referential Integrity**: Prevent deletion of customers with bookings
2. **Business Rules**: Prevent booking deletion if invoice exists
3. **Payment Validation**: Ensure payments don't exceed invoice total
4. **Date Validation**: Ensure booking end date is after start date

## Testing Strategy

### Unit Testing

Following the established testing patterns:

```typescript
// lib/validations/__tests__/customer.test.ts
// lib/validations/__tests__/booking.test.ts
// lib/validations/__tests__/invoice.test.ts

// hooks/__tests__/use-customers.test.ts
// hooks/__tests__/use-bookings.test.ts
// hooks/__tests__/use-invoices.test.ts
```

### Integration Testing

1. **API Route Testing**: Test CRUD operations and error scenarios
2. **Database Testing**: Test relationships and constraints
3. **Calendar Integration**: Test ilamy calendar event formatting
4. **Invoice Generation**: Test one-click invoice creation logic

### Component Testing

1. **Table Components**: Test rendering, sorting, and actions
2. **Form Components**: Test validation and submission
3. **Calendar Component**: Test event display and interactions
4. **Dialog Components**: Test opening, closing, and data flow

## Calendar Integration

### ilamy Calendar Implementation

```typescript
// components/booking-calendar.tsx
import { Calendar } from '@ilamy/calendar'

export const BookingCalendar = ({ bookings, onEventClick, onDateSelect }) => {
  const events = bookings.map(booking => ({
    id: booking.id.toString(),
    title: `${booking.customer.name} - ${booking.resources.map(r => r.name).join(', ')}`,
    start: booking.start,
    end: booking.end,
    backgroundColor: getStatusColor(booking.status),
    extendedProps: {
      bookingId: booking.id,
      customerName: booking.customer.name,
      status: booking.status,
      resourceNames: booking.resources.map(r => r.name)
    }
  }))

  return (
    <Calendar
      events={events}
      onEventClick={onEventClick}
      onDateSelect={onDateSelect}
      // Additional ilamy calendar configuration
    />
  )
}
```

### Event Formatting

Events will be formatted according to ilamy calendar specifications:
- **Title**: Customer name and resource names
- **Color Coding**: Different colors for booking statuses
- **Extended Properties**: Additional booking metadata for popover display

## Performance Considerations

### Data Loading

1. **Pagination**: Consistent pagination across all list views
2. **Lazy Loading**: Load related data only when needed
3. **Caching**: Implement appropriate caching strategies
4. **Search Optimization**: Debounced search with efficient queries

### Calendar Performance

1. **Event Batching**: Load events in date ranges
2. **Virtual Scrolling**: For large numbers of events
3. **Optimistic Updates**: Immediate UI updates with background sync

## Security Considerations

### Data Protection

1. **Input Validation**: Comprehensive validation on all inputs
2. **SQL Injection Prevention**: Prisma ORM provides protection
3. **XSS Prevention**: Proper data sanitization
4. **CSRF Protection**: Following Next.js security patterns

### Access Control

1. **Authentication**: Leverage existing authentication system
2. **Authorization**: Role-based access control
3. **Audit Trail**: Track all CRUD operations with user attribution

## Migration Strategy

### Database Migration

The existing Prisma schema already includes the necessary tables for customers, bookings, and invoices. No additional migrations are required.

### Component Migration

1. **Gradual Implementation**: Implement one management area at a time
2. **Pattern Consistency**: Follow established component patterns
3. **Testing**: Comprehensive testing before deployment

### API Enhancement

1. **Backward Compatibility**: Enhance existing API routes without breaking changes
2. **Error Handling**: Improve error handling to match established patterns
3. **Validation**: Add comprehensive validation to existing routes