---
alwaysApply: true
---


FIRST OF ALL:

Remember, my tech stack is allways as follows:

Full Stack:
- NextJS latest
- file based routing (app routing)
- shadcn
- react hook form
- zod
- use pnpm exclusively, NEVER use npm, bun, or yarn

NEVER USE TECH STACK OF OLDER VERSIONS NOR DIFFERENT TECHNOLOGY

ALWAYS USE context7 MCP TO PULL THE LATEST DOCS OF the framework or tech you are working on immediately

ALWAYS follow the YAGNI principle

Write the code as if the guy who ends up maintaining your code will be a violent psychopath who knows where you live, and follow the YAGNI principle