"use client";

import React, { useState, useMemo, useCallback } from 'react';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, isToday, isSameMonth, addMonths, subMonths, startOfWeek, endOfWeek } from 'date-fns';
import { ChevronLeft, ChevronRight, Plus, Clock, Users, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarEvent, BookingStatus } from '@/lib/types';
import { cn } from '@/lib/utils';

interface BookingCalendarProps {
  events: CalendarEvent[];
  onEventClick?: (event: CalendarEvent) => void;
  onDateSelect?: (date: Date) => void;
  onAddBooking?: () => void;
  loading?: boolean;
  className?: string;
}

const WEEKDAYS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

const getStatusColor = (status: BookingStatus) => {
  switch (status) {
    case 'CONFIRMED':
      return 'bg-green-100 border-green-300 text-green-800';
    case 'PENDING':
      return 'bg-yellow-100 border-yellow-300 text-yellow-800';
    case 'CANCELLED':
      return 'bg-red-100 border-red-300 text-red-800';
    default:
      return 'bg-gray-100 border-gray-300 text-gray-800';
  }
};

const EventPopoverContent: React.FC<{ event: CalendarEvent; onEventClick?: (event: CalendarEvent) => void }> = React.memo(({ 
  event, 
  onEventClick 
}) => (
  <div className="p-4 space-y-3 min-w-[280px]">
    <div className="flex items-start justify-between">
      <h4 className="font-semibold text-gray-900 leading-tight">{event.title}</h4>
      <Badge variant="outline" className={cn("ml-2", getStatusColor(event.extendedProps.status))}>
        {event.extendedProps.status}
      </Badge>
    </div>
    
    <div className="space-y-2 text-sm text-gray-600">
      <div className="flex items-center gap-2">
        <Users className="h-4 w-4" />
        <span>{event.extendedProps.customerName}</span>
      </div>
      
      <div className="flex items-center gap-2">
        <Clock className="h-4 w-4" />
        <span>
          {format(event.start, 'MMM d, yyyy h:mm a')} - {format(event.end, 'h:mm a')}
        </span>
      </div>
      
      {event.extendedProps.resourceNames.length > 0 && (
        <div className="flex items-start gap-2">
          <MapPin className="h-4 w-4 mt-0.5" />
          <div className="flex flex-wrap gap-1">
            {event.extendedProps.resourceNames.map((resource, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {resource}
              </Badge>
            ))}
          </div>
        </div>
      )}
    </div>
    
    {onEventClick && (
      <div className="pt-2 border-t">
        <Button 
          size="sm" 
          onClick={() => onEventClick(event)}
          className="w-full"
        >
          View Details
        </Button>
      </div>
    )}
  </div>
));

export const BookingCalendar: React.FC<BookingCalendarProps> = React.memo(({
  events,
  onEventClick,
  onDateSelect,
  onAddBooking,
  loading = false,
  className
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  // Calculate calendar days
  const calendarDays = useMemo(() => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    const calendarStart = startOfWeek(monthStart);
    const calendarEnd = endOfWeek(monthEnd);
    
    return eachDayOfInterval({ start: calendarStart, end: calendarEnd });
  }, [currentDate]);

  // Group events by date
  const eventsByDate = useMemo(() => {
    const grouped: Record<string, CalendarEvent[]> = {};
    
    events.forEach(event => {
      const dateKey = format(event.start, 'yyyy-MM-dd');
      if (!grouped[dateKey]) {
        grouped[dateKey] = [];
      }
      grouped[dateKey].push(event);
    });
    
    return grouped;
  }, [events]);

  const handlePreviousMonth = useCallback(() => {
    setCurrentDate(prev => subMonths(prev, 1));
  }, []);

  const handleNextMonth = useCallback(() => {
    setCurrentDate(prev => addMonths(prev, 1));
  }, []);

  const handleDateClick = useCallback((date: Date) => {
    setSelectedDate(date);
    onDateSelect?.(date);
  }, [onDateSelect]);

  const handleTodayClick = useCallback(() => {
    setCurrentDate(new Date());
    setSelectedDate(new Date());
  }, []);

  return (
    <Card className={cn("w-full", className)}>
      <CardContent className="p-6">
        {/* Calendar Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <h2 className="text-2xl font-bold text-gray-900">
              {format(currentDate, 'MMMM yyyy')}
            </h2>
            <Button
              variant="outline"
              size="sm"
              onClick={handleTodayClick}
              className="text-sm"
            >
              Today
            </Button>
          </div>
          
          <div className="flex items-center gap-2">
            {onAddBooking && (
              <Button onClick={onAddBooking} size="sm" className="gap-2">
                <Plus className="h-4 w-4" />
                Add Booking
              </Button>
            )}
            
            <div className="flex items-center gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePreviousMonth}
                disabled={loading}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleNextMonth}
                disabled={loading}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-7 gap-1">
          {/* Weekday Headers */}
          {WEEKDAYS.map(day => (
            <div
              key={day}
              className="h-10 flex items-center justify-center text-sm font-medium text-gray-500 border-b"
            >
              {day}
            </div>
          ))}

          {/* Calendar Days */}
          {calendarDays.map(day => {
            const dateKey = format(day, 'yyyy-MM-dd');
            const dayEvents = eventsByDate[dateKey] || [];
            const isCurrentMonth = isSameMonth(day, currentDate);
            const isSelected = selectedDate && isSameDay(day, selectedDate);
            const isTodayDate = isToday(day);

            return (
              <div
                key={day.toISOString()}
                className={cn(
                  "min-h-[120px] p-1 border border-gray-200 cursor-pointer transition-colors",
                  "hover:bg-gray-50",
                  !isCurrentMonth && "bg-gray-50 text-gray-400",
                  isSelected && "bg-blue-50 border-blue-300",
                  isTodayDate && "bg-blue-100 border-blue-400"
                )}
                onClick={() => handleDateClick(day)}
              >
                {/* Day Number */}
                <div className={cn(
                  "text-sm font-medium mb-1",
                  isTodayDate && "text-blue-600",
                  !isCurrentMonth && "text-gray-400"
                )}>
                  {format(day, 'd')}
                </div>

                {/* Events */}
                <div className="space-y-1">
                  {dayEvents.slice(0, 3).map((event, index) => (
                    <Popover key={event.id}>
                      <PopoverTrigger asChild>
                        <div
                          className={cn(
                            "text-xs p-1 rounded border cursor-pointer truncate",
                            "hover:shadow-sm transition-shadow",
                            getStatusColor(event.extendedProps.status)
                          )}
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                        >
                          <div className="font-medium truncate">
                            {event.extendedProps.customerName}
                          </div>
                          <div className="text-xs opacity-75 truncate">
                            {format(event.start, 'h:mm a')}
                          </div>
                        </div>
                      </PopoverTrigger>
                      <PopoverContent side="right" className="w-auto p-0">
                        <EventPopoverContent event={event} onEventClick={onEventClick} />
                      </PopoverContent>
                    </Popover>
                  ))}
                  
                  {dayEvents.length > 3 && (
                    <div className="text-xs text-gray-500 px-1">
                      +{dayEvents.length - 3} more
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Loading State */}
        {loading && (
          <div className="absolute inset-0 bg-white/50 flex items-center justify-center rounded-lg">
            <div className="flex items-center space-x-2 bg-white px-4 py-2 rounded-lg shadow-sm">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="text-sm text-gray-600">Loading calendar events...</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
});

EventPopoverContent.displayName = 'EventPopoverContent';
BookingCalendar.displayName = 'BookingCalendar';

export default BookingCalendar;