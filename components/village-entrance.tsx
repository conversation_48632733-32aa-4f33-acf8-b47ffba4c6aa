
'use client'
import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useT } from './language-provider';
import { ArrowRight, Map, Compass, Users } from 'lucide-react'
import { Button } from './ui/button'
import Image from 'next/image'

interface VillageEntranceProps {
  onEnterVillage: (mode: 'guided' | 'free') => void
}

export function VillageEntrance({ onEnterVillage }: VillageEntranceProps) {
  const t = useT();
  const [showGuide, setShowGuide] = useState(false)

  const handleEnterVillage = (mode: 'guided' | 'free') => {
    onEnterVillage(mode)
  }

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden pt-16">
      {/* Background Image with Parallax */}
      <div className="absolute inset-0">
        <div className="relative w-full h-full">
          <Image
            src="https://cdn.abacus.ai/images/ec04ca1d-c495-416e-ab42-8672705d22a4.png"
            alt="Netaj Village Entrance"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-black/20 to-black/40" />
        </div>
      </div>

      {/* Floating particles */}

      {/* Content */}
      <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2, delay: 0.2 }}
          viewport={{ once: true }}
          className="space-y-8"
        >
          {/* Welcome Message */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.2, delay: 0.4 }}
            viewport={{ once: true }}
            className="space-y-4"
          >
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold text-white leading-tight">
              {t('village_welcome')}
            </h1>
            <p className="text-xl md:text-2xl text-amber-100 max-w-3xl mx-auto">
              {t('village_entrance_subtitle')}
            </p>
          </motion.div>

          {/* Village Guide Introduction */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2, delay: 0.6 }}
            viewport={{ once: true }}
            className="max-w-4xl mx-auto"
          >
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <div className="flex items-center justify-center space-x-4 mb-4">
                <div className="relative">
                  <Image
                    src="https://cdn.abacus.ai/images/7b4046da-2880-4890-b9e5-41ce73062874.png"
                    alt="Village Guide"
                    width={80}
                    height={80}
                    className="rounded-full border-4 border-amber-300/50"
                  />
                  <motion.div
                    className="absolute -top-1 -right-1 w-6 h-6 bg-green-400 rounded-full border-2 border-white"
                    animate={{ scale: [1, 1.2, 1] }}
                    // transition removed: was decorative pulse
                  />
                </div>
                <div className="text-left">
                  <h3 className="text-lg font-semibold text-white">Village Guide</h3>
                  <p className="text-amber-200 text-sm">Your exploration companion</p>
                </div>
              </div>
              <p className="text-white/90 leading-relaxed">
                {t('village_guide_welcome')}
              </p>
            </div>
          </motion.div>

          {/* Exploration Options */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2, delay: 0.8 }}
            viewport={{ once: true }}
            className="grid md:grid-cols-2 gap-6 max-w-4xl mx-auto"
          >
            {/* Guided Tour */}
            <motion.div
              whileHover={{ scale: 1.02, y: -5 }}
              whileTap={{ scale: 0.98 }}
              className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 cursor-pointer group"
              onClick={() => handleEnterVillage('guided')}
            >
              <div className="flex items-center justify-center w-16 h-16 bg-amber-600/80 rounded-full mx-auto mb-4 group-hover:bg-amber-500/80 transition-colors">
                <Users className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">{t('exploration_mode_guided')}</h3>
              <p className="text-white/80 text-sm mb-4">
                Let our guide show you the best spots and share village stories
              </p>
              <Button
                className="w-full bg-amber-600 hover:bg-amber-700 text-white"
                onClick={() => handleEnterVillage('guided')}
              >
                Start Guided Tour
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </motion.div>

            {/* Free Exploration */}
            <motion.div
              whileHover={{ scale: 1.02, y: -5 }}
              whileTap={{ scale: 0.98 }}
              className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 cursor-pointer group"
              onClick={() => handleEnterVillage('free')}
            >
              <div className="flex items-center justify-center w-16 h-16 bg-emerald-600/80 rounded-full mx-auto mb-4 group-hover:bg-emerald-500/80 transition-colors">
                <Compass className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">{t('exploration_mode_free')}</h3>
              <p className="text-white/80 text-sm mb-4">
                Wander freely and discover the village at your own pace
              </p>
              <Button
                variant="outline"
                className="w-full border-white/30 text-primary hover:bg-white/10"
                onClick={() => handleEnterVillage('free')}
              >
                Explore Freely
                <Map className="ml-2 h-4 w-4" />
              </Button>
            </motion.div>
          </motion.div>

          {/* Village Statistics */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2, delay: 1.0 }}
            viewport={{ once: true }}
            className="grid grid-cols-3 gap-4 max-w-md mx-auto"
          >
            <div className="text-center">
              <div className="text-2xl font-bold text-amber-300">9</div>
              <div className="text-white/70 text-sm">Districts</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-amber-300">∞</div>
              <div className="text-white/70 text-sm">Possibilities</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-amber-300">1</div>
              <div className="text-white/70 text-sm">Community</div>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll Indicator removed for performance */}
    </section>
  )
}
