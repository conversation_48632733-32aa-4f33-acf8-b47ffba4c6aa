
'use client'
import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useLanguageStore } from './language-provider';
import { Globe, Menu, X, MapPin, Compass } from 'lucide-react'
import { Button } from './ui/button'
import type { ExplorationMode } from '@/app/page'
import Link from 'next/link'
import { useT } from './language-provider';

interface HeaderProps {
  onViewMap: () => void
  currentMode: ExplorationMode
  discoveredCount: number
}

export function Header({ onViewMap, currentMode, discoveredCount }: HeaderProps) {
  const t = useT();
  const language = useLanguageStore(state => state.language);
  const setLanguage = useLanguageStore(state => state.setLanguage);
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const toggleLanguage = () => {
    setLanguage(language === 'ar' ? 'en' : 'ar')
  }

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    element?.scrollIntoView({ behavior: 'smooth' })
    setIsMenuOpen(false)
  }

  return (
    <motion.header
      dir={language === 'ar' ? 'rtl' : 'ltr'}
      className={`fixed top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-sm border-b border-amber-200 rtl:flex-row-reverse`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.2 }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className={`flex items-center justify-between h-16`}>
          {/* Logo */}
          <Link href="/" className={`flex items-center card-scale-effect`}>
            <div className={`w-10 h-10 bg-gradient-to-br from-amber-600 to-orange-600 rounded-lg flex items-center justify-center`}>
              <span className="text-white font-bold text-lg">N</span>
            </div>
            <div className={`flex flex-col min-w-0`}>
              <span className="font-bold text-lg text-gray-900 whitespace-nowrap">Netaj</span>
              <span className={`text-xs text-amber-600 leading-tight`}>{t('village_entrance_subtitle')}</span>
            </div>
          </Link>

          {/* Village Status (visible when exploring) */}
          {currentMode !== 'entrance' && (
            <motion.div
              className="hidden md:flex items-center space-x-4 bg-amber-100 px-4 py-2 rounded-full"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.2 }}
            >
              <div className="flex items-center space-x-2">
                <Compass className="h-4 w-4 text-amber-600" />
                <span className="text-sm font-medium text-amber-800">
                  {t('districts_discovered')}: {discoveredCount}/9
                </span>
              </div>
              <div className="w-20 bg-amber-200 rounded-full h-2">
                <div 
                  className="bg-amber-600 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${(discoveredCount / 9) * 100}%` }}
                />
              </div>
            </motion.div>
          )}

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8 space-x-reverse">
            <Link href="/map" className="flex items-center space-x-2 text-amber-700 hover:text-amber-800 font-medium mx-4">
              <MapPin className="h-4 w-4" />
              <span>{t('nav_map')}</span>
            </Link>
            <Link href="/business" className="flex items-center space-x-2 text-amber-700 hover:text-amber-800 font-medium mx-4">
              <Compass className="h-4 w-4" />
              <span>{t('nav_businesses') || 'Businesses'}</span>
            </Link>

            <Button
              variant="ghost"
              size="sm"
              onClick={toggleLanguage}
              className="flex items-center space-x-1"
            >
              <Globe className="h-4 w-4" />
              <span>{language === 'ar' ? 'EN' : 'عربي'}</span>
            </Button>
          </nav>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center space-x-2 space-x-reverse">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleLanguage}
              className="flex items-center space-x-1"
            >
              <Globe className="h-4 w-4" />
              <span>{language === 'ar' ? 'EN' : 'عربي'}</span>
            </Button>

            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700 hover:text-amber-600 transition-colors duration-200"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <motion.div
            className="md:hidden"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="px-2 pt-2 pb-3 space-y-1 bg-white border-t border-amber-200">
              <Link href="/map" className="flex items-center space-x-2 text-amber-700 hover:text-amber-800 font-medium w-full py-2">
                <MapPin className="h-4 w-4" />
                <span>{t('nav_map')}</span>
              </Link>
              <Link href="/business" className="flex items-center space-x-2 text-amber-700 hover:text-amber-800 font-medium w-full py-2">
                <Compass className="h-4 w-4" />
                <span>{t('nav_businesses') || 'Businesses'}</span>
              </Link>

              {/* Mobile Village Status */}
              {currentMode !== 'entrance' && (
                <div className="px-3 py-2 bg-amber-50 rounded-md">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-amber-800">{t('districts_discovered')}</span>
                    <span className="font-bold text-amber-600">{discoveredCount}/9</span>
                  </div>
                  <div className="mt-2 w-full bg-amber-200 rounded-full h-2">
                    <div 
                      className="bg-amber-600 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${(discoveredCount / 9) * 100}%` }}
                    />
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </div>
    </motion.header>
  )
}
