
'use client'
import React from 'react'
import { motion } from 'framer-motion'
import { useLanguage } from './language-provider'
import { ArrowDown } from 'lucide-react'
import Image from 'next/image'

export function HeroSection() {
  const { t } = useLanguage()

  const scrollToServices = () => {
    const element = document.getElementById('services')
    element?.scrollIntoView({ behavior: 'smooth' })
  }

  return (
    <section id="hero" className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image with Parallax */}
      <div className="absolute inset-0 bg-slate-800">
        <div className="relative w-full h-full">
          <Image
            src="/hero-background.jpg"
            alt="Nataj Business Village"
            fill
            placeholder="blur"
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-slate-900/40" />
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2, delay: 0.2 }}
          viewport={{ once: true }}
          className="space-y-8"
        >
          <motion.h1
            className="text-4xl md:text-6xl lg:text-7xl font-bold text-white leading-tight"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2, delay: 0.4 }}
            viewport={{ once: true }}
          >
            {t('hero_title')}
          </motion.h1>

          <motion.p
            className="text-xl md:text-2xl text-gray-200 max-w-4xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2, delay: 0.6 }}
            viewport={{ once: true }}
          >
            {t('hero_subtitle')}
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2, delay: 0.8 }}
            viewport={{ once: true }}
          >
            <div
              className="inline-flex items-center px-8 py-4 bg-amber-600 text-white font-semibold rounded-lg hover:bg-amber-700 transition-colors duration-300 shadow-lg cta-scale-effect hero-cta-shadow"
              onClick={scrollToServices}
            >
              {t('hero_cta')}
              <ArrowDown className="ml-2 h-5 w-5" />
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll Indicator removed for performance */}
    </section>
  )
}
