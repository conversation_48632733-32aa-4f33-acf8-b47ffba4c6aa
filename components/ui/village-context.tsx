"use client"
import { create } from 'zustand';
import type { DistrictType, BusinessType } from '@/app/page';

interface VillageState {
  discoveredDistricts: Set<DistrictType>;
  setDiscoveredDistricts: (districts: Set<DistrictType> | ((prev: Set<DistrictType>) => Set<DistrictType>)) => void;
  discoveredBusinesses: Set<BusinessType>;
  setDiscoveredBusinesses: (businesses: Set<BusinessType> | ((prev: Set<BusinessType>) => Set<BusinessType>)) => void;
}

export const useVillageStore = create<VillageState>((set) => ({
  discoveredDistricts: new Set(),
  setDiscoveredDistricts: (districts) =>
    set(prev => {
      const value = typeof districts === 'function' ? districts(prev.discoveredDistricts) : districts;
      return { discoveredDistricts: value instanceof Set ? value : new Set(value) };
    }),
  discoveredBusinesses: new Set(),
  setDiscoveredBusinesses: (businesses) =>
    set(prev => {
      const value = typeof businesses === 'function' ? businesses(prev.discoveredBusinesses) : businesses;
      return { discoveredBusinesses: value instanceof Set ? value : new Set(value) };
    }),
})); 