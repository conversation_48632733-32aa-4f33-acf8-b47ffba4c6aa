"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  Loader2, 
  CheckCircle2, 
  AlertCircle, 
  Clock, 
  Save,
  Upload,
  Send,
  RefreshCw
} from "lucide-react";

interface FormLoadingButtonProps {
  loading: boolean;
  disabled?: boolean;
  children: React.ReactNode;
  loadingText?: string;
  icon?: 'save' | 'upload' | 'send' | 'refresh';
  className?: string;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

export function FormLoadingButton({
  loading,
  disabled = false,
  children,
  loadingText,
  icon = 'save',
  className,
  variant = 'default',
  size = 'default',
  onClick,
  type = 'button'
}: FormLoadingButtonProps) {
  const getIcon = () => {
    switch (icon) {
      case 'save':
        return <Save className="mr-2 h-4 w-4" />;
      case 'upload':
        return <Upload className="mr-2 h-4 w-4" />;
      case 'send':
        return <Send className="mr-2 h-4 w-4" />;
      case 'refresh':
        return <RefreshCw className="mr-2 h-4 w-4" />;
      default:
        return <Save className="mr-2 h-4 w-4" />;
    }
  };

  return (
    <Button
      type={type}
      variant={variant}
      size={size}
      disabled={loading || disabled}
      onClick={onClick}
      className={className}
    >
      {loading ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          {loadingText || 'Processing...'}
        </>
      ) : (
        <>
          {!loading && getIcon()}
          {children}
        </>
      )}
    </Button>
  );
}

interface FormLoadingOverlayProps {
  loading: boolean;
  message?: string;
  progress?: number;
  showProgress?: boolean;
}

export function FormLoadingOverlay({
  loading,
  message = "Processing form...",
  progress,
  showProgress = false
}: FormLoadingOverlayProps) {
  if (!loading) return null;

  return (
    <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
      <Card className="w-auto">
        <CardContent className="p-6">
          <div className="flex items-center space-x-3">
            <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
            <div>
              <p className="font-medium text-sm">{message}</p>
              {showProgress && typeof progress === 'number' && (
                <div className="mt-2">
                  <div className="w-48 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
                    />
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {Math.round(progress)}% complete
                  </p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

interface FormSkeletonProps {
  fields?: number;
  showButtons?: boolean;
  showTitle?: boolean;
}

export function FormSkeleton({
  fields = 5,
  showButtons = true,
  showTitle = true
}: FormSkeletonProps) {
  return (
    <div className="space-y-6">
      {showTitle && (
        <div className="space-y-2">
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-96" />
        </div>
      )}
      
      <div className="space-y-4">
        {Array.from({ length: fields }).map((_, index) => (
          <div key={index} className="space-y-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-10 w-full" />
          </div>
        ))}
      </div>

      {showButtons && (
        <div className="flex space-x-2 pt-4">
          <Skeleton className="h-10 w-20" />
          <Skeleton className="h-10 w-32" />
        </div>
      )}
    </div>
  );
}

interface FormStatusIndicatorProps {
  status: 'idle' | 'validating' | 'valid' | 'invalid' | 'submitting' | 'success' | 'error';
  message?: string;
  errorCount?: number;
  showIcon?: boolean;
}

export function FormStatusIndicator({
  status,
  message,
  errorCount = 0,
  showIcon = true
}: FormStatusIndicatorProps) {
  const getStatusConfig = () => {
    switch (status) {
      case 'validating':
        return {
          icon: <Loader2 className="h-4 w-4 animate-spin" />,
          text: message || 'Validating form...',
          className: 'text-blue-600'
        };
      case 'valid':
        return {
          icon: <CheckCircle2 className="h-4 w-4" />,
          text: message || 'Form is valid',
          className: 'text-green-600'
        };
      case 'invalid':
        return {
          icon: <AlertCircle className="h-4 w-4" />,
          text: message || `${errorCount} error${errorCount !== 1 ? 's' : ''}`,
          className: 'text-red-600'
        };
      case 'submitting':
        return {
          icon: <Loader2 className="h-4 w-4 animate-spin" />,
          text: message || 'Submitting form...',
          className: 'text-blue-600'
        };
      case 'success':
        return {
          icon: <CheckCircle2 className="h-4 w-4" />,
          text: message || 'Form submitted successfully',
          className: 'text-green-600'
        };
      case 'error':
        return {
          icon: <AlertCircle className="h-4 w-4" />,
          text: message || 'Submission failed',
          className: 'text-red-600'
        };
      default:
        return null;
    }
  };

  const config = getStatusConfig();
  if (!config) return null;

  return (
    <div className={`flex items-center space-x-2 text-sm ${config.className}`}>
      {showIcon && config.icon}
      <span>{config.text}</span>
    </div>
  );
}

interface InlineLoadingIndicatorProps {
  loading: boolean;
  text?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'subtle';
}

export function InlineLoadingIndicator({
  loading,
  text = "Loading...",
  size = 'sm',
  variant = 'default'
}: InlineLoadingIndicatorProps) {
  if (!loading) return null;

  const sizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  const containerClasses = variant === 'subtle' 
    ? 'text-muted-foreground' 
    : 'text-blue-600';

  return (
    <div className={`flex items-center space-x-2 ${containerClasses}`}>
      <Loader2 className={`${sizeClasses[size]} animate-spin`} />
      <span className={textSizeClasses[size]}>{text}</span>
    </div>
  );
}

interface SubmissionProgressProps {
  steps: string[];
  currentStep: number;
  loading: boolean;
  error?: string;
}

export function SubmissionProgress({
  steps,
  currentStep,
  loading,
  error
}: SubmissionProgressProps) {
  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium">
          {loading ? 'Processing...' : error ? 'Failed' : 'Complete'}
        </span>
        <Badge variant={error ? 'destructive' : loading ? 'secondary' : 'default'}>
          Step {Math.min(currentStep + 1, steps.length)} of {steps.length}
        </Badge>
      </div>
      
      <div className="space-y-2">
        {steps.map((step, index) => {
          const isActive = index === currentStep;
          const isComplete = index < currentStep;
          const hasError = error && index === currentStep;
          
          return (
            <div key={index} className="flex items-center space-x-3">
              <div className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                hasError 
                  ? 'bg-red-100 text-red-600' 
                  : isComplete 
                    ? 'bg-green-100 text-green-600' 
                    : isActive 
                      ? 'bg-blue-100 text-blue-600' 
                      : 'bg-gray-100 text-gray-400'
              }`}>
                {hasError ? (
                  <AlertCircle className="h-3 w-3" />
                ) : isComplete ? (
                  <CheckCircle2 className="h-3 w-3" />
                ) : isActive && loading ? (
                  <Loader2 className="h-3 w-3 animate-spin" />
                ) : (
                  index + 1
                )}
              </div>
              <span className={`text-sm ${
                hasError 
                  ? 'text-red-600' 
                  : isComplete 
                    ? 'text-green-600' 
                    : isActive 
                      ? 'text-blue-600' 
                      : 'text-gray-400'
              }`}>
                {step}
              </span>
            </div>
          );
        })}
      </div>
      
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  );
}