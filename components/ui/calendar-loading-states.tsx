"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Loader2, 
  Calendar, 
  Clock, 
  Wifi, 
  WifiOff, 
  AlertCircle,
  RefreshCw,
  CheckCircle2
} from "lucide-react";

interface CalendarLoadingOverlayProps {
  loading: boolean;
  message?: string;
  showProgress?: boolean;
  progress?: number;
  elapsedTime?: number;
}

export function CalendarLoadingOverlay({
  loading,
  message = "Loading calendar events...",
  showProgress = false,
  progress = 0,
  elapsedTime = 0
}: CalendarLoadingOverlayProps) {
  const [dots, setDots] = useState("");

  useEffect(() => {
    if (!loading) return;

    const interval = setInterval(() => {
      setDots(prev => {
        if (prev === "...") return "";
        return prev + ".";
      });
    }, 500);

    return () => clearInterval(interval);
  }, [loading]);

  if (!loading) return null;

  return (
    <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center rounded-lg z-10">
      <Card className="w-auto min-w-[300px]">
        <CardContent className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full">
              <Loader2 className="w-5 h-5 text-blue-600 animate-spin" />
            </div>
            <div className="flex-1">
              <p className="font-medium text-sm">{message}{dots}</p>
              <p className="text-xs text-muted-foreground">
                Fetching booking data from server
              </p>
            </div>
          </div>

          {showProgress && (
            <div className="space-y-2">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
                />
              </div>
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{Math.round(progress)}% complete</span>
                {elapsedTime > 0 && (
                  <span className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {elapsedTime}s
                  </span>
                )}
              </div>
            </div>
          )}

          {elapsedTime > 5 && (
            <Alert className="mt-3">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-xs">
                Taking longer than expected. Please check your connection.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

interface CalendarLoadingStateProps {
  loading: boolean;
  error?: string;
  onRetry?: () => void;
  showNetworkStatus?: boolean;
}

export function CalendarLoadingState({
  loading,
  error,
  onRetry,
  showNetworkStatus = true
}: CalendarLoadingStateProps) {
  const [elapsedTime, setElapsedTime] = useState(0);

  useEffect(() => {
    if (!loading) {
      setElapsedTime(0);
      return;
    }

    const startTime = Date.now();
    const interval = setInterval(() => {
      setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(interval);
  }, [loading]);

  if (error) {
    return (
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
              <AlertCircle className="w-6 h-6 text-red-600" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Failed to Load Calendar</h3>
              <p className="text-sm text-gray-600 mb-4">{error}</p>
              {onRetry && (
                <button
                  onClick={onRetry}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Try Again
                </button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!loading) return null;

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
              <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />
            </div>
            <div>
              <CardTitle className="text-lg flex items-center">
                <Calendar className="mr-2 h-5 w-5" />
                Loading Calendar
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Fetching booking events and availability
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {elapsedTime}s
            </Badge>
            <Badge variant="secondary">
              Loading
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Calendar Header Skeleton */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Skeleton className="h-8 w-40" />
            <Skeleton className="h-8 w-16" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-28" />
            <div className="flex items-center gap-1">
              <Skeleton className="h-8 w-8" />
              <Skeleton className="h-8 w-8" />
            </div>
          </div>
        </div>

        {/* Calendar Grid Skeleton */}
        <div className="grid grid-cols-7 gap-1">
          {/* Weekday Headers */}
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <div
              key={day}
              className="h-10 flex items-center justify-center text-sm font-medium text-gray-500 border-b"
            >
              {day}
            </div>
          ))}

          {/* Calendar Days Skeleton */}
          {Array.from({ length: 35 }).map((_, index) => (
            <div
              key={index}
              className="min-h-[100px] p-1 border border-gray-200"
            >
              <div className="mb-1">
                <Skeleton className="h-4 w-6" />
              </div>
              <div className="space-y-1">
                {index % 3 === 0 && <Skeleton className="h-6 w-full rounded" />}
                {index % 4 === 1 && <Skeleton className="h-6 w-full rounded" />}
                {index % 5 === 2 && (
                  <>
                    <Skeleton className="h-6 w-full rounded" />
                    <Skeleton className="h-3 w-16" />
                  </>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Network Status */}
        {showNetworkStatus && (
          <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
            {typeof window !== 'undefined' && navigator.onLine ? (
              <>
                <Wifi className="h-4 w-4 text-green-600" />
                <span>Network Connected</span>
              </>
            ) : typeof window !== 'undefined' ? (
              <>
                <WifiOff className="h-4 w-4 text-red-600" />
                <span>Network Disconnected</span>
              </>
            ) : (
              <>
                <Wifi className="h-4 w-4 text-gray-400" />
                <span>Checking Connection...</span>
              </>
            )}
          </div>
        )}

        {/* Long loading warning */}
        {elapsedTime > 10 && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Calendar is taking longer than expected to load. This might be due to a slow connection or server issues.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}

interface EventLoadingIndicatorProps {
  loading: boolean;
  count?: number;
  type?: 'events' | 'bookings' | 'appointments';
}

export function EventLoadingIndicator({
  loading,
  count = 0,
  type = 'events'
}: EventLoadingIndicatorProps) {
  if (!loading) return null;

  return (
    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
      <Loader2 className="h-3 w-3 animate-spin" />
      <span>
        Loading {type}...
        {count > 0 && ` (${count} found)`}
      </span>
    </div>
  );
}

interface CalendarRefreshIndicatorProps {
  refreshing: boolean;
  lastUpdated?: Date;
  onRefresh?: () => void;
}

export function CalendarRefreshIndicator({
  refreshing,
  lastUpdated,
  onRefresh
}: CalendarRefreshIndicatorProps) {
  return (
    <div className="flex items-center justify-between text-xs text-muted-foreground">
      <div className="flex items-center space-x-2">
        {refreshing ? (
          <>
            <Loader2 className="h-3 w-3 animate-spin" />
            <span>Refreshing calendar...</span>
          </>
        ) : (
          <>
            <CheckCircle2 className="h-3 w-3 text-green-600" />
            <span>
              {lastUpdated 
                ? `Updated ${lastUpdated.toLocaleTimeString()}`
                : 'Calendar up to date'
              }
            </span>
          </>
        )}
      </div>
      {onRefresh && (
        <button
          onClick={onRefresh}
          disabled={refreshing}
          className="flex items-center space-x-1 hover:text-blue-600 transition-colors"
        >
          <RefreshCw className={`h-3 w-3 ${refreshing ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      )}
    </div>
  );
}