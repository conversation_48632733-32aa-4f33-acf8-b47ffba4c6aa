"use client"

import { memo, useCallback, useMemo } from "react"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react"

interface PaginationControlsProps {
  currentPage: number
  totalPages: number
  pageSize: number
  totalItems: number
  onPageChange: (page: number) => void
  onPageSizeChange: (pageSize: number) => void
  pageSizeOptions?: number[]
  className?: string
}

export const PaginationControls = memo(({
  currentPage,
  totalPages,
  pageSize,
  totalItems,
  onPageChange,
  onPageSizeChange,
  pageSizeOptions = [10, 20, 50, 100],
  className = "",
}: PaginationControlsProps) => {
  const startItem = useMemo(() => 
    totalItems === 0 ? 0 : (currentPage - 1) * pageSize + 1, 
    [totalItems, currentPage, pageSize]
  )
  
  const endItem = useMemo(() => 
    Math.min(currentPage * pageSize, totalItems), 
    [currentPage, pageSize, totalItems]
  )

  const canGoPrevious = useMemo(() => currentPage > 1, [currentPage])
  const canGoNext = useMemo(() => currentPage < totalPages, [currentPage, totalPages])

  const handleFirstPage = useCallback(() => {
    if (canGoPrevious) {
      onPageChange(1)
    }
  }, [canGoPrevious, onPageChange])

  const handlePreviousPage = useCallback(() => {
    if (canGoPrevious) {
      onPageChange(currentPage - 1)
    }
  }, [canGoPrevious, onPageChange, currentPage])

  const handleNextPage = useCallback(() => {
    if (canGoNext) {
      onPageChange(currentPage + 1)
    }
  }, [canGoNext, onPageChange, currentPage])

  const handleLastPage = useCallback(() => {
    if (canGoNext) {
      onPageChange(totalPages)
    }
  }, [canGoNext, onPageChange, totalPages])

  // Generate page numbers to show - memoized for performance
  const pageNumbers = useMemo(() => {
    const pages: (number | string)[] = []
    const maxVisiblePages = 5

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is small
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      // Show first page
      pages.push(1)

      if (currentPage > 3) {
        pages.push("...")
      }

      // Show pages around current page
      const start = Math.max(2, currentPage - 1)
      const end = Math.min(totalPages - 1, currentPage + 1)

      for (let i = start; i <= end; i++) {
        pages.push(i)
      }

      if (currentPage < totalPages - 2) {
        pages.push("...")
      }

      // Show last page
      if (totalPages > 1) {
        pages.push(totalPages)
      }
    }

    return pages
  }, [currentPage, totalPages])

  if (totalItems === 0) {
    return null
  }

  return (
    <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 ${className}`}>
      <div className="flex items-center justify-center sm:justify-start">
        <p className="text-sm text-muted-foreground">
          Showing {startItem} to {endItem} of {totalItems} results
        </p>
      </div>

      <div className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6">
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium">Rows per page</p>
          <Select
            value={pageSize.toString()}
            onValueChange={(value) => onPageSizeChange(Number(value))}
          >
            <SelectTrigger 
              className="h-10 w-[70px] focus-enhanced"
              aria-label="Select page size"
            >
              <SelectValue placeholder={pageSize} />
            </SelectTrigger>
            <SelectContent side="top">
              {pageSizeOptions.map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-2">
          <p className="text-sm font-medium">
            Page {currentPage} of {totalPages}
          </p>
          <div className="flex items-center space-x-1">
            <Button
              variant="outline"
              className="hidden h-10 w-10 p-0 lg:flex focus-enhanced"
              onClick={handleFirstPage}
              disabled={!canGoPrevious}
              aria-label="Go to first page"
            >
              <span className="sr-only">Go to first page</span>
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-10 w-10 p-0 focus-enhanced"
              onClick={handlePreviousPage}
              disabled={!canGoPrevious}
              aria-label="Go to previous page"
            >
              <span className="sr-only">Go to previous page</span>
              <ChevronLeft className="h-4 w-4" />
            </Button>

            {/* Page number buttons - hide on very small screens */}
            <div className="hidden xs:flex items-center space-x-1">
              {pageNumbers.map((page, index) => (
                <div key={index}>
                  {page === "..." ? (
                    <span className="flex h-9 w-9 items-center justify-center text-sm">
                      ...
                    </span>
                  ) : (
                    <Button
                      variant={currentPage === page ? "default" : "outline"}
                      className="h-10 w-10 p-0 focus-enhanced"
                      onClick={() => onPageChange(page as number)}
                      aria-label={`Go to page ${page}`}
                      aria-current={currentPage === page ? "page" : undefined}
                    >
                      {page}
                    </Button>
                  )}
                </div>
              ))}
            </div>

            <Button
              variant="outline"
              className="h-10 w-10 p-0 focus-enhanced"
              onClick={handleNextPage}
              disabled={!canGoNext}
              aria-label="Go to next page"
            >
              <span className="sr-only">Go to next page</span>
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="hidden h-10 w-10 p-0 lg:flex focus-enhanced"
              onClick={handleLastPage}
              disabled={!canGoNext}
              aria-label="Go to last page"
            >
              <span className="sr-only">Go to last page</span>
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
})

PaginationControls.displayName = "PaginationControls"