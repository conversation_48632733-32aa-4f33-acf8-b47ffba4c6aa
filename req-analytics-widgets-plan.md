# Analytics Widgets Plan

Based on the analysis of `prd.md` and `requirements.md`, here is a recommended list of analytics widgets for the analytics section/page:

## 1. Revenue Overview
- **Description:** Displays total revenue over selectable time periods (monthly, quarterly, yearly), with trends and comparisons.
- **Rationale:** Helps admins track financial performance and identify growth or decline patterns.

## 2. Resource Utilization Rate
- **Description:** Shows the percentage of time each resource (event halls, rooms, desks, etc.) is booked versus available, with breakdowns by resource type.
- **Rationale:** Identifies underutilized or overbooked resources to inform pricing and marketing strategies.

## 3. Booking Volume Trends
- **Description:** Line or bar chart showing the number of bookings over time, with filters for resource type, customer segment, or time period.
- **Rationale:** Reveals seasonality, growth, and booking patterns to support operational planning.

## 4. Customer Activity & Segmentation
- **Description:** Visualizes customer sign-ups, repeat bookings, and segmentation by industry, specialization, or company size.
- **Rationale:** Supports targeted outreach and helps understand the customer base.

## 5. Invoice Payment Status Breakdown
- **Description:** Pie or bar chart showing the proportion of paid, partially paid, and unpaid invoices.
- **Rationale:** Aids in monitoring cash flow and identifying payment issues.

## 6. Catering Services Analytics
- **Description:** Tracks the number and value of catering orders, most popular offers, and vendor/first party share breakdowns.
- **Rationale:** Informs catering strategy and vendor management.

## 7. Booking Conflict & Error Log
- **Description:** Summarizes instances of booking conflicts, double-bookings prevented, or manual corrections made.
- **Rationale:** Measures system reliability and highlights areas for process improvement.

## 8. Top Customers & Companies
- **Description:** Lists customers or companies with the highest booking volume or revenue contribution.
- **Rationale:** Identifies key accounts for relationship management and rewards.

## 9. Resource Demand Forecast
- **Description:** Predictive widget estimating future demand for each resource based on historical data.
- **Rationale:** Supports proactive resource allocation and marketing.

---

**Note:** As the system evolves, additional analytics widgets can be added for advanced reporting, customer portal usage, or meeting room-specific analytics. 