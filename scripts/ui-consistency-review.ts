#!/usr/bin/env tsx

/**
 * UI/UX Consistency Review Script
 * 
 * This script performs automated checks for:
 * - Design pattern consistency across components
 * - Responsive design implementation
 * - Accessibility compliance
 * - Component structure consistency
 */

import { readFileSync, readdirSync, statSync } from 'fs';
import { join } from 'path';

interface ReviewResult {
  category: string;
  check: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  file?: string;
}

class UIConsistencyReviewer {
  private results: ReviewResult[] = [];
  private readonly projectRoot: string;

  constructor() {
    this.projectRoot = process.cwd();
  }

  private addResult(category: string, check: string, status: 'PASS' | 'FAIL' | 'WARNING', message: string, file?: string) {
    this.results.push({ category, check, status, message, file });
  }

  private getFiles(dir: string, extension: string): string[] {
    const files: string[] = [];
    
    const traverse = (currentDir: string) => {
      const items = readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = join(currentDir, item);
        const stat = statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          traverse(fullPath);
        } else if (stat.isFile() && item.endsWith(extension)) {
          files.push(fullPath);
        }
      }
    };
    
    traverse(dir);
    return files;
  }

  private readFile(filePath: string): string {
    try {
      return readFileSync(filePath, 'utf-8');
    } catch (error) {
      return '';
    }
  }

  // Check 1: Design Pattern Consistency
  private checkDesignPatterns() {
    console.log('🎨 Checking design pattern consistency...');
    
    const componentFiles = this.getFiles(join(this.projectRoot, 'app/dashboard'), '.tsx');
    const managementPages = componentFiles.filter(file => 
      file.includes('/page.tsx') && 
      (file.includes('customers/page.tsx') || file.includes('bookings/page.tsx') || 
       file.includes('invoices/page.tsx') || file.includes('amenities/page.tsx') || 
       file.includes('resources/page.tsx') || file.includes('catering/page.tsx') || 
       file.includes('users/page.tsx'))
    );

    // Check for consistent page structure
    const requiredPatterns = [
      { pattern: /h-full bg-gray-50/, name: 'Page container styling' },
      { pattern: /text-(2xl|3xl).*font-bold text-gray-900/, name: 'Page title styling' },
      { pattern: /text-gray-600/, name: 'Page description styling' }
    ];

    const cardPatterns = [
      { pattern: /<Card/, name: 'Card component usage' },
      { pattern: /<CardHeader/, name: 'Card header usage' },
      { pattern: /<CardContent/, name: 'Card content usage' }
    ];

    for (const file of managementPages) {
      const content = this.readFile(file);
      const fileName = file.split('/').pop() || file;
      
      // Check basic patterns for all pages
      for (const { pattern, name } of requiredPatterns) {
        if (pattern.test(content)) {
          this.addResult('Design Patterns', name, 'PASS', `Consistent pattern found in ${fileName}`, file);
        } else {
          this.addResult('Design Patterns', name, 'FAIL', `Missing or inconsistent pattern in ${fileName} (${file})`, file);
        }
      }

      // Check card patterns only for non-calendar pages
      if (!file.includes('bookings')) {
        for (const { pattern, name } of cardPatterns) {
          if (pattern.test(content)) {
            this.addResult('Design Patterns', name, 'PASS', `Consistent pattern found in ${fileName}`, file);
          } else {
            this.addResult('Design Patterns', name, 'WARNING', `Card pattern not found in ${fileName} - may use different layout`, file);
          }
        }
      }
    }

    // Check for consistent button patterns
    const buttonPatterns = [
      { pattern: /variant="outline"/, name: 'Outline button variant' },
      { pattern: /className="min-h-\[44px\] sm:min-h-auto"/, name: 'Mobile-friendly button height' },
      { pattern: /<Plus className="mr-2 h-4 w-4" \/>/, name: 'Consistent icon usage in buttons' }
    ];

    for (const file of managementPages) {
      const content = this.readFile(file);
      const fileName = file.split('/').pop() || file;
      
      for (const { pattern, name } of buttonPatterns) {
        if (pattern.test(content)) {
          this.addResult('Design Patterns', name, 'PASS', `Button pattern found in ${fileName}`, file);
        } else {
          this.addResult('Design Patterns', name, 'WARNING', `Button pattern not found in ${fileName} (may not be needed)`, file);
        }
      }
    }
  }

  // Check 2: Responsive Design
  private checkResponsiveDesign() {
    console.log('📱 Checking responsive design implementation...');
    
    const componentFiles = this.getFiles(join(this.projectRoot, 'app/dashboard'), '.tsx');
    const tableComponents = componentFiles.filter(file => 
      file.includes('table.tsx') && 
      (file.includes('customers') || file.includes('bookings') || file.includes('invoices'))
    );

    const responsivePatterns = [
      { pattern: /sm:/, name: 'Small screen breakpoints' },
      { pattern: /md:/, name: 'Medium screen breakpoints' },
      { pattern: /lg:/, name: 'Large screen breakpoints' },
      { pattern: /hidden sm:table-cell/, name: 'Mobile-hidden table columns' },
      { pattern: /sm:flex-row/, name: 'Responsive flex direction' },
      { pattern: /grid-cols-1.*md:grid-cols-2/, name: 'Responsive grid layouts' }
    ];

    for (const file of tableComponents) {
      const content = this.readFile(file);
      const fileName = file.split('/').pop() || file;
      
      for (const { pattern, name } of responsivePatterns) {
        if (pattern.test(content)) {
          this.addResult('Responsive Design', name, 'PASS', `Responsive pattern found in ${fileName}`, file);
        } else {
          this.addResult('Responsive Design', name, 'WARNING', `Responsive pattern not found in ${fileName}`, file);
        }
      }
    }

    // Check for mobile card layouts
    const mobileCardPattern = /block.*md:hidden|md:hidden.*block/;
    for (const file of tableComponents) {
      const content = this.readFile(file);
      const fileName = file.split('/').pop() || file;
      
      if (mobileCardPattern.test(content)) {
        this.addResult('Responsive Design', 'Mobile card layout', 'PASS', `Mobile card layout found in ${fileName}`, file);
      } else {
        this.addResult('Responsive Design', 'Mobile card layout', 'FAIL', `Mobile card layout missing in ${fileName}`, file);
      }
    }
  }

  // Check 3: Accessibility Compliance
  private checkAccessibility() {
    console.log('♿ Checking accessibility compliance...');
    
    const componentFiles = this.getFiles(join(this.projectRoot, 'app/dashboard'), '.tsx');
    const allComponents = componentFiles.filter(file => file.endsWith('.tsx'));

    const accessibilityPatterns = [
      { pattern: /aria-label=/, name: 'ARIA labels' },
      { pattern: /aria-describedby=/, name: 'ARIA descriptions' },
      { pattern: /role=/, name: 'ARIA roles' },
      { pattern: /alt=/, name: 'Image alt text' },
      { pattern: /htmlFor=/, name: 'Form label associations' },
      { pattern: /tabIndex=/, name: 'Keyboard navigation' }
    ];

    let accessibilityScore = 0;
    let totalChecks = 0;

    for (const file of allComponents) {
      const content = this.readFile(file);
      const fileName = file.split('/').pop() || file;
      
      // Skip if file doesn't contain interactive elements
      if (!content.includes('<button') && !content.includes('<input') && !content.includes('<select')) {
        continue;
      }

      for (const { pattern, name } of accessibilityPatterns) {
        totalChecks++;
        if (pattern.test(content)) {
          accessibilityScore++;
          this.addResult('Accessibility', name, 'PASS', `Accessibility feature found in ${fileName}`, file);
        } else {
          this.addResult('Accessibility', name, 'WARNING', `Consider adding ${name.toLowerCase()} in ${fileName}`, file);
        }
      }
    }

    // Check for semantic HTML
    const semanticPatterns = [
      { pattern: /<main/, name: 'Main landmark' },
      { pattern: /<nav/, name: 'Navigation landmark' },
      { pattern: /<section/, name: 'Section elements' },
      { pattern: /<header/, name: 'Header elements' }
    ];

    for (const file of allComponents) {
      const content = this.readFile(file);
      const fileName = file.split('/').pop() || file;
      
      for (const { pattern, name } of semanticPatterns) {
        if (pattern.test(content)) {
          this.addResult('Accessibility', name, 'PASS', `Semantic HTML found in ${fileName}`, file);
        }
      }
    }

    // Overall accessibility score - be more lenient as many patterns are optional
    const accessibilityPercentage = totalChecks > 0 ? Math.round((accessibilityScore / totalChecks) * 100) : 100;
    this.addResult('Accessibility', 'Overall Score', 
      accessibilityPercentage >= 30 ? 'PASS' : accessibilityPercentage >= 15 ? 'WARNING' : 'FAIL',
      `Accessibility compliance: ${accessibilityPercentage}% (${accessibilityScore}/${totalChecks}) - Many accessibility features are implemented through UI library components`
    );
  }

  // Check 4: Component Structure Consistency
  private checkComponentStructure() {
    console.log('🏗️ Checking component structure consistency...');
    
    const componentFiles = this.getFiles(join(this.projectRoot, 'app/dashboard'), '.tsx');
    const formComponents = componentFiles.filter(file => 
      file.includes('form') && file.endsWith('.tsx')
    );

    // Check for consistent form structure
    const formPatterns = [
      { pattern: /useForm/, name: 'React Hook Form usage' },
      { pattern: /zodResolver/, name: 'Zod validation integration' },
      { pattern: /onSubmit=/, name: 'Form submission handling' },
      { pattern: /loading/, name: 'Loading state management' },
      { pattern: /disabled=\{loading\}/, name: 'Loading state UI feedback' }
    ];

    for (const file of formComponents) {
      const content = this.readFile(file);
      const fileName = file.split('/').pop() || file;
      
      for (const { pattern, name } of formPatterns) {
        if (pattern.test(content)) {
          this.addResult('Component Structure', name, 'PASS', `Form pattern found in ${fileName}`, file);
        } else {
          this.addResult('Component Structure', name, 'WARNING', `Form pattern not found in ${fileName}`, file);
        }
      }
    }

    // Check for consistent error handling
    const errorPatterns = [
      { pattern: /ErrorBoundary/, name: 'Error boundary usage' },
      { pattern: /try.*catch/, name: 'Error handling in async operations' },
      { pattern: /toast.*error|error.*toast/, name: 'User-friendly error notifications' }
    ];

    const allComponents = componentFiles.filter(file => file.endsWith('.tsx'));
    for (const file of allComponents) {
      const content = this.readFile(file);
      const fileName = file.split('/').pop() || file;
      
      // Only check files that likely need error handling
      if (!content.includes('async') && !content.includes('fetch') && !content.includes('api')) {
        continue;
      }

      for (const { pattern, name } of errorPatterns) {
        if (pattern.test(content)) {
          this.addResult('Component Structure', name, 'PASS', `Error handling found in ${fileName}`, file);
        } else {
          this.addResult('Component Structure', name, 'WARNING', `Consider adding ${name.toLowerCase()} in ${fileName}`, file);
        }
      }
    }
  }

  // Check 5: Loading States and Skeletons
  private checkLoadingStates() {
    console.log('⏳ Checking loading states and skeleton implementations...');
    
    const componentFiles = this.getFiles(join(this.projectRoot, 'app/dashboard'), '.tsx');
    const pageComponents = componentFiles.filter(file => 
      file.includes('/page.tsx') && 
      (file.includes('customers') || file.includes('bookings') || file.includes('invoices'))
    );

    const loadingPatterns = [
      { pattern: /Skeleton/, name: 'Skeleton loading components' },
      { pattern: /loading.*\?/, name: 'Conditional loading states' },
      { pattern: /disabled=\{.*loading.*\}/, name: 'Disabled states during loading' },
      { pattern: /animate-spin/, name: 'Loading animations' }
    ];

    for (const file of pageComponents) {
      const content = this.readFile(file);
      const fileName = file.split('/').pop() || file;
      
      for (const { pattern, name } of loadingPatterns) {
        if (pattern.test(content)) {
          this.addResult('Loading States', name, 'PASS', `Loading pattern found in ${fileName}`, file);
        } else {
          this.addResult('Loading States', name, 'WARNING', `Loading pattern not found in ${fileName}`, file);
        }
      }
    }
  }

  // Run all checks
  public async runReview(): Promise<void> {
    console.log('🔍 Starting UI/UX Consistency Review...\n');
    
    this.checkDesignPatterns();
    this.checkResponsiveDesign();
    this.checkAccessibility();
    this.checkComponentStructure();
    this.checkLoadingStates();
    
    this.generateReport();
  }

  // Generate comprehensive report
  private generateReport(): void {
    console.log('\n📊 UI/UX Consistency Review Report');
    console.log('=====================================\n');

    const categories = [...new Set(this.results.map(r => r.category))];
    
    let totalPassed = 0;
    let totalFailed = 0;
    let totalWarnings = 0;

    for (const category of categories) {
      const categoryResults = this.results.filter(r => r.category === category);
      const passed = categoryResults.filter(r => r.status === 'PASS').length;
      const failed = categoryResults.filter(r => r.status === 'FAIL').length;
      const warnings = categoryResults.filter(r => r.status === 'WARNING').length;

      totalPassed += passed;
      totalFailed += failed;
      totalWarnings += warnings;

      console.log(`📂 ${category}`);
      console.log(`   ✅ Passed: ${passed}`);
      console.log(`   ❌ Failed: ${failed}`);
      console.log(`   ⚠️  Warnings: ${warnings}`);
      console.log('');

      // Show failed checks
      const failedChecks = categoryResults.filter(r => r.status === 'FAIL');
      if (failedChecks.length > 0) {
        console.log(`   Failed Checks:`);
        failedChecks.forEach(check => {
          console.log(`   - ${check.check}: ${check.message}`);
        });
        console.log('');
      }
    }

    console.log('📈 Overall Summary');
    console.log('==================');
    console.log(`✅ Total Passed: ${totalPassed}`);
    console.log(`❌ Total Failed: ${totalFailed}`);
    console.log(`⚠️  Total Warnings: ${totalWarnings}`);
    console.log(`📊 Total Checks: ${this.results.length}`);

    const successRate = Math.round((totalPassed / this.results.length) * 100);
    console.log(`🎯 Success Rate: ${successRate}%`);

    if (totalFailed === 0) {
      console.log('\n🎉 All critical UI/UX consistency checks passed!');
    } else {
      console.log(`\n⚠️  ${totalFailed} critical issues need attention.`);
    }

    if (totalWarnings > 0) {
      console.log(`💡 ${totalWarnings} improvements suggested for better UX.`);
    }

    console.log('\n✨ Review completed successfully!');
  }
}

// Run the review
const reviewer = new UIConsistencyReviewer();
reviewer.runReview().catch(console.error);