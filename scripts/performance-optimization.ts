#!/usr/bin/env tsx

/**
 * Performance Optimization Script
 * 
 * This script performs automated checks for:
 * - Database query optimization
 * - API response time analysis
 * - Calendar performance with large datasets
 * - Caching strategy implementation
 */

import { readFileSync, readdirSync, statSync } from 'fs';
import { join } from 'path';

interface PerformanceResult {
  category: string;
  check: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  file?: string;
  metric?: number;
}

class PerformanceOptimizer {
  private results: PerformanceResult[] = [];
  private readonly projectRoot: string;

  constructor() {
    this.projectRoot = process.cwd();
  }

  private addResult(category: string, check: string, status: 'PASS' | 'FAIL' | 'WARNING', message: string, file?: string, metric?: number) {
    this.results.push({ category, check, status, message, file, metric });
  }

  private getFiles(dir: string, extension: string): string[] {
    const files: string[] = [];
    
    const traverse = (currentDir: string) => {
      try {
        const items = readdirSync(currentDir);
        
        for (const item of items) {
          const fullPath = join(currentDir, item);
          const stat = statSync(fullPath);
          
          if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
            traverse(fullPath);
          } else if (stat.isFile() && item.endsWith(extension)) {
            files.push(fullPath);
          }
        }
      } catch (error) {
        // Skip directories we can't read
      }
    };
    
    traverse(dir);
    return files;
  }

  private readFile(filePath: string): string {
    try {
      return readFileSync(filePath, 'utf-8');
    } catch (error) {
      return '';
    }
  }

  // Check 1: Database Query Optimization
  private checkDatabaseQueries() {
    console.log('🗄️ Checking database query optimization...');
    
    const apiFiles = this.getFiles(join(this.projectRoot, 'app/api'), '.ts');
    
    // Check for N+1 query patterns
    const n1QueryPatterns = [
      { pattern: /prisma\.\w+\.findMany\(\).*\.map.*prisma\.\w+\.find/, name: 'N+1 Query Pattern' },
      { pattern: /for.*prisma\.\w+\.find(?!Many)/, name: 'Loop with individual queries' },
      { pattern: /forEach.*prisma\.\w+\.find(?!Many)/, name: 'ForEach with individual queries' }
    ];

    // Check for missing includes/select optimizations
    const optimizationPatterns = [
      { pattern: /include:\s*{/, name: 'Using includes for relations' },
      { pattern: /select:\s*{/, name: 'Using select for specific fields' },
      { pattern: /take:\s*\d+/, name: 'Using pagination (take)' },
      { pattern: /skip:\s*\d+/, name: 'Using pagination (skip)' },
      { pattern: /orderBy:\s*{/, name: 'Using database-level sorting' }
    ];

    let totalQueries = 0;
    let optimizedQueries = 0;

    for (const file of apiFiles) {
      const content = this.readFile(file);
      const fileName = file.split('/').pop() || file;
      
      // Skip if no Prisma queries
      if (!content.includes('prisma.')) continue;

      totalQueries++;

      // Check for N+1 patterns
      for (const { pattern, name } of n1QueryPatterns) {
        if (pattern.test(content)) {
          this.addResult('Database Queries', name, 'FAIL', `Potential N+1 query detected in ${fileName}`, file);
        }
      }

      // Check for optimizations
      let hasOptimizations = false;
      for (const { pattern, name } of optimizationPatterns) {
        if (pattern.test(content)) {
          hasOptimizations = true;
          this.addResult('Database Queries', name, 'PASS', `Query optimization found in ${fileName}`, file);
        }
      }

      if (hasOptimizations) {
        optimizedQueries++;
      } else {
        this.addResult('Database Queries', 'Query Optimization', 'WARNING', `Consider adding includes/select optimization in ${fileName}`, file);
      }
    }

    // Overall database optimization score
    const optimizationPercentage = totalQueries > 0 ? Math.round((optimizedQueries / totalQueries) * 100) : 100;
    this.addResult('Database Queries', 'Overall Optimization', 
      optimizationPercentage >= 70 ? 'PASS' : optimizationPercentage >= 50 ? 'WARNING' : 'FAIL',
      `Database query optimization: ${optimizationPercentage}% (${optimizedQueries}/${totalQueries} files optimized)`,
      undefined, optimizationPercentage
    );
  }

  // Check 2: API Response Time Analysis
  private async checkAPIResponseTimes() {
    console.log('⚡ Checking API response times...');
    
    const apiEndpoints = [
      { path: '/api/customers', method: 'GET', name: 'Customers List' },
      { path: '/api/bookings', method: 'GET', name: 'Bookings List' },
      { path: '/api/invoices', method: 'GET', name: 'Invoices List' },
      { path: '/api/resources', method: 'GET', name: 'Resources List' },
      { path: '/api/catering', method: 'GET', name: 'Catering List' }
    ];

    for (const endpoint of apiEndpoints) {
      try {
        const startTime = Date.now();
        const response = await fetch(`http://localhost:3000${endpoint.path}`, {
          method: endpoint.method,
          headers: { 'Content-Type': 'application/json' }
        });
        const endTime = Date.now();
        const responseTime = endTime - startTime;

        if (response.ok) {
          if (responseTime < 500) {
            this.addResult('API Performance', endpoint.name, 'PASS', `Response time: ${responseTime}ms (excellent)`, undefined, responseTime);
          } else if (responseTime < 1000) {
            this.addResult('API Performance', endpoint.name, 'WARNING', `Response time: ${responseTime}ms (acceptable)`, undefined, responseTime);
          } else {
            this.addResult('API Performance', endpoint.name, 'FAIL', `Response time: ${responseTime}ms (slow)`, undefined, responseTime);
          }
        } else {
          this.addResult('API Performance', endpoint.name, 'WARNING', `API returned ${response.status} status`, undefined, response.status);
        }
      } catch (error) {
        this.addResult('API Performance', endpoint.name, 'FAIL', `API request failed: ${error}`, undefined);
      }
    }
  }

  // Check 3: Calendar Performance
  private checkCalendarPerformance() {
    console.log('📅 Checking calendar performance optimization...');
    
    const calendarFiles = this.getFiles(join(this.projectRoot, 'components'), '.tsx')
      .concat(this.getFiles(join(this.projectRoot, 'app/dashboard/bookings'), '.tsx'));

    const performancePatterns = [
      { pattern: /useMemo/, name: 'Memoization for expensive calculations' },
      { pattern: /useCallback/, name: 'Callback memoization' },
      { pattern: /React\.memo/, name: 'Component memoization' },
      { pattern: /\.memo\(/, name: 'Component memoization (alternative syntax)' },
      { pattern: /lazy\(/, name: 'Lazy loading' },
      { pattern: /Suspense/, name: 'Suspense for loading states' }
    ];

    const calendarOptimizations = [
      { pattern: /events.*\.slice\(/, name: 'Event pagination/slicing' },
      { pattern: /events.*\.filter\(.*date/, name: 'Date-based event filtering' },
      { pattern: /virtualiz/, name: 'Virtualization for large datasets' },
      { pattern: /debounce/, name: 'Debounced operations' }
    ];

    let calendarFilesFound = 0;
    let optimizedCalendarFiles = 0;

    for (const file of calendarFiles) {
      const content = this.readFile(file);
      const fileName = file.split('/').pop() || file;
      
      // Skip if not calendar-related
      if (!content.includes('calendar') && !content.includes('Calendar') && !content.includes('booking') && !content.includes('event')) {
        continue;
      }

      calendarFilesFound++;
      let hasOptimizations = false;

      // Check for general performance patterns
      for (const { pattern, name } of performancePatterns) {
        if (pattern.test(content)) {
          hasOptimizations = true;
          this.addResult('Calendar Performance', name, 'PASS', `Performance optimization found in ${fileName}`, file);
        }
      }

      // Check for calendar-specific optimizations
      for (const { pattern, name } of calendarOptimizations) {
        if (pattern.test(content)) {
          hasOptimizations = true;
          this.addResult('Calendar Performance', name, 'PASS', `Calendar optimization found in ${fileName}`, file);
        }
      }

      if (hasOptimizations) {
        optimizedCalendarFiles++;
      } else {
        this.addResult('Calendar Performance', 'Performance Optimization', 'WARNING', `Consider adding performance optimizations to ${fileName}`, file);
      }
    }

    // Overall calendar performance score
    const calendarOptimizationPercentage = calendarFilesFound > 0 ? Math.round((optimizedCalendarFiles / calendarFilesFound) * 100) : 100;
    this.addResult('Calendar Performance', 'Overall Optimization', 
      calendarOptimizationPercentage >= 70 ? 'PASS' : calendarOptimizationPercentage >= 50 ? 'WARNING' : 'FAIL',
      `Calendar performance optimization: ${calendarOptimizationPercentage}% (${optimizedCalendarFiles}/${calendarFilesFound} files optimized)`,
      undefined, calendarOptimizationPercentage
    );
  }

  // Check 4: Caching Strategy
  private checkCachingStrategy() {
    console.log('🗂️ Checking caching strategy implementation...');
    
    const hookFiles = this.getFiles(join(this.projectRoot, 'hooks'), '.ts');
    const apiFiles = this.getFiles(join(this.projectRoot, 'app/api'), '.ts');

    const cachingPatterns = [
      { pattern: /useSWR/, name: 'SWR caching' },
      { pattern: /useQuery/, name: 'React Query caching' },
      { pattern: /staleTime/, name: 'Stale time configuration' },
      { pattern: /cacheTime/, name: 'Cache time configuration' },
      { pattern: /revalidate/, name: 'Revalidation strategy' },
      { pattern: /Cache-Control/, name: 'HTTP cache headers' },
      { pattern: /maxAge/, name: 'Max age caching' }
    ];

    let filesWithCaching = 0;
    let totalRelevantFiles = 0;

    // Check hooks for caching
    for (const file of hookFiles) {
      const content = this.readFile(file);
      const fileName = file.split('/').pop() || file;
      
      // Skip if not data-fetching hook
      if (!content.includes('fetch') && !content.includes('api') && !content.includes('useState')) {
        continue;
      }

      totalRelevantFiles++;
      let hasCaching = false;

      for (const { pattern, name } of cachingPatterns) {
        if (pattern.test(content)) {
          hasCaching = true;
          this.addResult('Caching Strategy', name, 'PASS', `Caching strategy found in ${fileName}`, file);
        }
      }

      if (hasCaching) {
        filesWithCaching++;
      } else {
        this.addResult('Caching Strategy', 'Data Caching', 'WARNING', `Consider implementing caching in ${fileName}`, file);
      }
    }

    // Check API routes for cache headers
    for (const file of apiFiles) {
      const content = this.readFile(file);
      const fileName = file.split('/').pop() || file;
      
      if (content.includes('NextResponse') && content.includes('GET')) {
        totalRelevantFiles++;
        
        if (content.includes('Cache-Control') || content.includes('maxAge')) {
          filesWithCaching++;
          this.addResult('Caching Strategy', 'HTTP Cache Headers', 'PASS', `Cache headers found in ${fileName}`, file);
        } else {
          this.addResult('Caching Strategy', 'HTTP Cache Headers', 'WARNING', `Consider adding cache headers to ${fileName}`, file);
        }
      }
    }

    // Overall caching score
    const cachingPercentage = totalRelevantFiles > 0 ? Math.round((filesWithCaching / totalRelevantFiles) * 100) : 100;
    this.addResult('Caching Strategy', 'Overall Implementation', 
      cachingPercentage >= 50 ? 'PASS' : cachingPercentage >= 25 ? 'WARNING' : 'FAIL',
      `Caching implementation: ${cachingPercentage}% (${filesWithCaching}/${totalRelevantFiles} files with caching)`,
      undefined, cachingPercentage
    );
  }

  // Check 5: Bundle Size and Code Splitting
  private checkBundleOptimization() {
    console.log('📦 Checking bundle optimization...');
    
    const componentFiles = this.getFiles(join(this.projectRoot, 'app'), '.tsx')
      .concat(this.getFiles(join(this.projectRoot, 'components'), '.tsx'));

    const bundleOptimizations = [
      { pattern: /dynamic\(/, name: 'Dynamic imports' },
      { pattern: /lazy\(/, name: 'Lazy loading' },
      { pattern: /import\(.*\)/, name: 'Code splitting' },
      { pattern: /"use client"/, name: 'Client component optimization' },
      { pattern: /Suspense/, name: 'Suspense boundaries' }
    ];

    let optimizedFiles = 0;
    let totalFiles = componentFiles.length;

    for (const file of componentFiles) {
      const content = this.readFile(file);
      const fileName = file.split('/').pop() || file;
      
      let hasOptimizations = false;

      for (const { pattern, name } of bundleOptimizations) {
        if (pattern.test(content)) {
          hasOptimizations = true;
          this.addResult('Bundle Optimization', name, 'PASS', `Bundle optimization found in ${fileName}`, file);
        }
      }

      if (hasOptimizations) {
        optimizedFiles++;
      }
    }

    // Check for large components that might benefit from splitting
    for (const file of componentFiles) {
      const content = this.readFile(file);
      const fileName = file.split('/').pop() || file;
      const lineCount = content.split('\n').length;
      
      if (lineCount > 500) {
        this.addResult('Bundle Optimization', 'Large Component', 'WARNING', `${fileName} has ${lineCount} lines - consider splitting`, file, lineCount);
      }
    }

    // Overall bundle optimization score
    const bundleOptimizationPercentage = Math.round((optimizedFiles / totalFiles) * 100);
    this.addResult('Bundle Optimization', 'Overall Optimization', 
      bundleOptimizationPercentage >= 30 ? 'PASS' : bundleOptimizationPercentage >= 15 ? 'WARNING' : 'FAIL',
      `Bundle optimization: ${bundleOptimizationPercentage}% (${optimizedFiles}/${totalFiles} files with optimizations)`,
      undefined, bundleOptimizationPercentage
    );
  }

  // Run all performance checks
  public async runOptimization(): Promise<void> {
    console.log('🚀 Starting Performance Optimization Analysis...\n');
    
    this.checkDatabaseQueries();
    await this.checkAPIResponseTimes();
    this.checkCalendarPerformance();
    this.checkCachingStrategy();
    this.checkBundleOptimization();
    
    this.generateReport();
  }

  // Generate comprehensive report
  private generateReport(): void {
    console.log('\n📊 Performance Optimization Report');
    console.log('===================================\n');

    const categories = [...new Set(this.results.map(r => r.category))];
    
    let totalPassed = 0;
    let totalFailed = 0;
    let totalWarnings = 0;

    for (const category of categories) {
      const categoryResults = this.results.filter(r => r.category === category);
      const passed = categoryResults.filter(r => r.status === 'PASS').length;
      const failed = categoryResults.filter(r => r.status === 'FAIL').length;
      const warnings = categoryResults.filter(r => r.status === 'WARNING').length;

      totalPassed += passed;
      totalFailed += failed;
      totalWarnings += warnings;

      console.log(`📂 ${category}`);
      console.log(`   ✅ Passed: ${passed}`);
      console.log(`   ❌ Failed: ${failed}`);
      console.log(`   ⚠️  Warnings: ${warnings}`);

      // Show metrics for key categories
      const metricsResults = categoryResults.filter(r => r.metric !== undefined);
      if (metricsResults.length > 0) {
        console.log(`   📈 Key Metrics:`);
        metricsResults.forEach(result => {
          const unit = category === 'API Performance' ? 'ms' : '%';
          console.log(`      - ${result.check}: ${result.metric}${unit}`);
        });
      }
      console.log('');

      // Show critical issues
      const criticalIssues = categoryResults.filter(r => r.status === 'FAIL');
      if (criticalIssues.length > 0) {
        console.log(`   Critical Issues:`);
        criticalIssues.forEach(issue => {
          console.log(`   - ${issue.check}: ${issue.message}`);
        });
        console.log('');
      }
    }

    console.log('📈 Overall Performance Summary');
    console.log('==============================');
    console.log(`✅ Total Passed: ${totalPassed}`);
    console.log(`❌ Total Failed: ${totalFailed}`);
    console.log(`⚠️  Total Warnings: ${totalWarnings}`);
    console.log(`📊 Total Checks: ${this.results.length}`);

    const performanceScore = Math.round(((totalPassed + (totalWarnings * 0.5)) / this.results.length) * 100);
    console.log(`🎯 Performance Score: ${performanceScore}%`);

    // Performance recommendations
    console.log('\n💡 Performance Recommendations');
    console.log('===============================');
    
    if (totalFailed > 0) {
      console.log('🔴 Critical Issues to Address:');
      const criticalIssues = this.results.filter(r => r.status === 'FAIL');
      criticalIssues.forEach(issue => {
        console.log(`   - ${issue.category}: ${issue.message}`);
      });
      console.log('');
    }

    console.log('🟡 Optimization Opportunities:');
    const optimizations = this.results.filter(r => r.status === 'WARNING');
    const topOptimizations = optimizations.slice(0, 5); // Show top 5
    topOptimizations.forEach(opt => {
      console.log(`   - ${opt.category}: ${opt.message}`);
    });

    if (performanceScore >= 80) {
      console.log('\n🎉 Excellent performance! Your application is well-optimized.');
    } else if (performanceScore >= 60) {
      console.log('\n👍 Good performance with room for improvement.');
    } else {
      console.log('\n⚠️  Performance needs attention. Consider implementing the recommendations above.');
    }

    console.log('\n✨ Performance analysis completed!');
  }
}

// Run the optimization analysis
const optimizer = new PerformanceOptimizer();
optimizer.runOptimization().catch(console.error);