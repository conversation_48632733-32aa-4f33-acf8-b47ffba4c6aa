import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcryptjs';
import * as readline from 'readline';

const prisma = new PrismaClient();

function prompt(query: string, hideInput = false): Promise<string> {
  return new Promise((resolve) => {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
    if (hideInput) {
      // Type assertion to allow custom properties
      const rlAny = rl as any;
      rlAny.stdoutMuted = true;
      rlAny._writeToOutput = function _writeToOutput(stringToWrite: any) {
        if (rlAny.stdoutMuted) rlAny.output.write("*\x1B[2K\x1B[200D" + query + Array(rlAny.line.length + 1).join("*"));
        else rlAny.output.write(stringToWrite);
      };
    }
    rl.question(query, (answer) => {
      rl.close();
      resolve(answer);
    });
    if (hideInput) (rl as any).stdoutMuted = false;
  });
}

async function main() {
  const username = await prompt('Username: ');
  const password = await prompt('Password: ', true);
  console.log(); // for newline after password

  if (!username || !password) {
    console.error('Username and password are required.');
    process.exit(1);
  }

  const hashedPassword = await bcrypt.hash(password, 10);
  const firstName = 'Super';
  const lastName = 'User';

  const existing = await prisma.user.findUnique({ where: { username } });
  if (existing) {
    console.error('A user with this username already exists.');
    process.exit(1);
  }

  const user = await prisma.user.create({
    data: {
      firstName,
      lastName,
      username,
      password: hashedPassword,
      // @ts-ignore - Role enum works at runtime despite TypeScript error
      role: 'ADMIN',
      isSuperUser: true,
    },
  });

  console.log('Superuser created:', user);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 