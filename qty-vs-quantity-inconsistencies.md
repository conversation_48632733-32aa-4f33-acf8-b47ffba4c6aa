# qty vs quantity Inconsistencies Analysis

## Overview

This document analyzes the inconsistencies in naming conventions for quantity fields across different layers of the application. The inconsistent use of "qty" versus "quantity" creates technical debt, increases maintenance burden, and introduces potential bugs throughout the codebase.

## Identified Inconsistencies

### 1. Database Schema Level

#### Invoice Schema (`prisma/schema/invoice.prisma`)
- **File**: [`prisma/schema/invoice.prisma`](prisma/schema/invoice.prisma:25)
- **Line**: 25
- **Issue**: The `LineItem` model uses `qty` as the field name
```prisma
model LineItem {
  id          Int       @id @default(autoincrement())
  invoiceId   Int
  description String
  amount      Decimal
  isCatering  <PERSON>olean   @default(false)
  qty         Int       @default(1)  // ← Uses "qty"
  cateringId  Int?
  // ... other fields
}
```

#### Catering Schema (`prisma/schema/catering.prisma`)
- **File**: [`prisma/schema/catering.prisma`](prisma/schema/catering.prisma:21)
- **Line**: 21
- **Issue**: The `CateringOnBooking` model uses `quantity` as the field name
```prisma
model CateringOnBooking {
  id          Int      @id @default(autoincrement())
  bookingId   Int
  cateringId  Int
  quantity    Int      // ← Uses "quantity"
  // ... other fields
}
```

### 2. TypeScript Interface Level

#### Types Definition (`lib/types.ts`)
- **File**: [`lib/types.ts`](lib/types.ts:375)
- **Line**: 375
- **Issue**: The `LineItem` interface uses `quantity` as the field name, which is inconsistent with the database schema
```typescript
export interface LineItem {
  id: number;
  invoiceId: number;
  description: string;
  amount: number;
  quantity: number;  // ← Uses "quantity" but database uses "qty"
  isCatering: boolean;
  catering?: Catering | null;
  cateringId?: number | null;
  // ... other fields
}
```

- **File**: [`lib/types.ts`](lib/types.ts:340)
- **Line**: 340
- **Issue**: The `CateringOnBooking` interface uses `quantity` as the field name, which is consistent with the database schema
```typescript
export interface CateringOnBooking {
  id: number;
  bookingId: number;
  cateringId: number;
  quantity: number;  // ← Uses "quantity" (consistent with database)
  // ... other fields
}
```

### 3. Validation Schema Level

#### Invoice Validation (`lib/validations/invoice.ts`)
- **File**: [`lib/validations/invoice.ts`](lib/validations/invoice.ts:35)
- **Line**: 35
- **Issue**: The validation schema uses `quantity` as the field name
```typescript
const quantitySchema = z.number()
  .int("Quantity must be a whole number")
  .min(1, "Quantity must be at least 1")
  .max(1000, "Quantity cannot exceed 1000")
```

- **File**: [`lib/validations/invoice.ts`](lib/validations/invoice.ts:51)
- **Line**: 51
- **Issue**: The line item validation schema uses `quantity` as the field name
```typescript
const lineItemSchema = z.object({
  description: descriptionSchema,
  amount: amountSchema,
  quantity: quantitySchema,  // ← Uses "quantity"
  isCatering: z.boolean().default(false),
  cateringId: z.number().int().positive().optional()
})
```

#### Booking Validation (`lib/validations/booking.ts`)
- **File**: [`lib/validations/booking.ts`](lib/validations/booking.ts:30)
- **Line**: 30
- **Issue**: The catering item validation schema uses `quantity` as the field name
```typescript
const cateringItemSchema = z.object({
  cateringId: z.number().int().positive("Invalid catering ID"),
  quantity: z.number()
    .int("Quantity must be a whole number")
    .min(1, "Quantity must be at least 1")
    .max(1000, "Quantity cannot exceed 1000")  // ← Uses "quantity"
})
```

### 4. API Route Level

#### Invoice API Route (`app/api/invoices/route.ts`)
- **File**: [`app/api/invoices/route.ts`](app/api/invoices/route.ts:334)
- **Line**: 334
- **Issue**: The API maps `item.quantity` to `qty: item.quantity` when saving to database
```typescript
const lineItemsData = validatedData.lineItems.map(item => ({
  invoiceId: newInvoice.id,
  description: item.description,
  amount: new Prisma.Decimal(item.amount),
  qty: item.quantity,  // ← Maps "quantity" to "qty"
  isCatering: item.isCatering,
  cateringId: item.cateringId || null,
  // TODO: Add createdById from authenticated user session
}));
```

#### Invoice API Route (`app/api/invoices/[id]/route.ts`)
- **File**: [`app/api/invoices/[id]/route.ts`](app/api/invoices/%5Bid%5D/route.ts:235)
- **Line**: 235
- **Issue**: The API maps `item.quantity` to `qty: item.quantity` when updating an invoice.
```typescript
  qty: item.quantity, // Use 'qty' to match database schema
```
- **File**: [`app/api/invoices/[id]/route.ts`](app/api/invoices/%5Bid%5D/route.ts:74)
- **Line**: 74
- **Issue**: Prisma select uses `qty: true` to fetch `qty` from the database.
```typescript
          id: true,
          description: true,
          amount: true,
          qty: true, // ← Uses "qty"
          isCatering: true,
          cateringId: true,
```
- **File**: [`app/api/invoices/[id]/route.ts`](app/api/invoices/%5Bid%5D/route.ts:56)
- **Line**: 56
- **Issue**: Prisma select uses `quantity: true` to fetch `quantity` from the database for catering bookings, even though `LineItem` uses `qty`.
```typescript
              catering: {
                select: {
                  id: true,
                  quantity: true, // ← Uses "quantity" for catering
                  catering: true,
                }
              }
```
- **File**: [`app/api/invoices/[id]/route.ts`](app/api/invoices/%5Bid%5D/route.ts:202)
- **Line**: 202
- **Issue**: Calculation uses `item.quantity` for line items, even though the database stores `qty`.
```typescript
    .reduce((sum, item) => sum + (item.amount * item.quantity), 0); // ← Uses "quantity"
```

#### Booking API Route (`app/api/bookings/[id]/invoice/route.ts`)
- **File**: [`app/api/bookings/[id]/invoice/route.ts`](app/api/bookings/%5Bid%5D/invoice/route.ts:140)
- **Line**: 140
- **Issue**: The API maps `item.quantity` to `qty: item.quantity` when generating an invoice from a booking.
```typescript
      qty: item.quantity, // Use 'qty' to match database schema
```
- **File**: [`app/api/bookings/[id]/route.ts`](app/api/bookings/%5Bid%5D/route.ts:74)
- **Line**: 74
- **Issue**: Prisma select uses `qty: true` to fetch `qty` for invoice line items.
```typescript
        invoice: {
          select: {
            lineItems: {
              select: {
                qty: true, // ← Uses "qty"
                amount: true,
```
- **File**: [`app/api/bookings/[id]/route.ts`](app/api/bookings/%5Bid%5D/route.ts:83)
- **Line**: 83
- **Issue**: Prisma select uses `quantity: true` to fetch `quantity` for catering on booking.
```typescript
          catering: {
            select: {
              quantity: true, // ← Uses "quantity"
              catering: {
                select: {
```

#### Booking API Route (`app/api/bookings/route.ts`)
- **File**: [`app/api/bookings/route.ts`](app/api/bookings/route.ts:380)
- **Line**: 380
- **Issue**: `catering.quantity` is directly used when creating a catering on booking.
```typescript
          cateringId: catering.cateringId,
          quantity: catering.quantity // ← Uses "quantity"
        }))
```

#### Invoice Recalculation API Route (`app/api/invoices/[id]/recalculate/route.ts`)
- **File**: [`app/api/invoices/[id]/recalculate/route.ts`](app/api/invoices/%5Bid%5D/recalculate/route.ts:115)
- **Line**: 115
- **Issue**: Prisma select uses `quantity: true` for catering bookings within invoice recalculation.
```typescript
            catering: {
              select: {
                quantity: true, // ← Uses "quantity"
              }
            }
```

### 5. Test Level

#### Invoice API Route Tests (`app/api/invoices/__tests__/route.test.ts`)
- **File**: [`app/api/invoices/__tests__/route.test.ts`](app/api/invoices/__tests__/route.test.ts:93)
- **Line**: 93
- **Issue**: Test data for line items uses `qty`.
```typescript
        lineItems: [{
          description: 'Test Line Item 1',
          amount: 100,
          qty: 1, // ← Uses "qty"
          isCatering: false,
        }],
```
- **File**: [`app/api/invoices/__tests__/route.test.ts`](app/api/invoices/__tests__/route.test.ts:233)
- **Line**: 233
- **Issue**: Test data for line items uses `quantity`.
```typescript
            description: 'Test Catering Item',
            amount: 50,
            quantity: 1, // ← Uses "quantity"
            isCatering: true,
            cateringId: 1,
```
- **File**: [`app/api/invoices/__tests__/route.test.ts`](app/api/invoices/__tests__/route.test.ts:217)
- **Line**: 217
- **Issue**: Test expectation checks for `qty`.
```typescript
      expect(createdInvoice.lineItems[0]).toHaveProperty('qty', 1); // ← Uses "qty"
```

#### Invoice API Route ID Tests (`app/api/invoices/[id]/__tests__/route.test.ts`)
- **File**: [`app/api/invoices/[id]/__tests__/route.test.ts`](app/api/invoices/%5Bid%5D/__tests__/route.test.ts:80)
- **Line**: 80
- **Issue**: Test data for line items uses `qty`.
```typescript
        lineItems: [{
          description: 'Existing Line Item',
          amount: 50,
          qty: 1, // ← Uses "qty"
          isCatering: false,
        }],
```
- **File**: [`app/api/invoices/[id]/__tests__/route.test.ts`](app/api/invoices/%5Bid%5D/__tests__/route.test.ts:65)
- **Line**: 65
- **Issue**: Test data for line items uses `quantity`.
```typescript
            description: 'Catering Item',
            amount: 10,
            quantity: 10, // ← Uses "quantity"
            isCatering: true,
            cateringId: catering.id
```
- **File**: [`app/api/invoices/[id]/__tests__/route.test.ts`](app/api/invoices/%5Bid%5D/__tests__/route.test.ts:220)
- **Line**: 220
- **Issue**: Test expectation checks for `qty`.
```typescript
      expect(updatedInvoice.lineItems[0].qty).toBe(2); // ← Uses "qty"
```

#### Invoice Integration Tests (`app/api/invoices/__tests__/integration.test.ts`)
- **File**: [`app/api/invoices/__tests__/integration.test.ts`](app/api/invoices/__tests__/integration.test.ts:146)
- **Line**: 146
- **Issue**: Test data for line items uses `qty`.
```typescript
        lineItems: [{
          description: 'Regular Item',
          amount: 100,
          qty: 1, // ← Uses "qty"
          isCatering: false,
        }],
```
- **File**: [`app/api/invoices/__tests__/integration.test.ts`](app/api/invoices/__tests__/integration.test.ts:100)
- **Line**: 100
- **Issue**: Test data for catering items uses `quantity`.
```typescript
        cateringItems: [{
          cateringId: 1,
          quantity: 1, // ← Uses "quantity"
        }],
```
- **File**: [`app/api/invoices/__tests__/integration.test.ts`](app/api/invoices/__tests__/integration.test.ts:240)
- **Line**: 240
- **Issue**: Test expectation checks for `qty`.
```typescript
      expect(invoice.lineItems[0]).toHaveProperty('qty', 1); // ← Uses "qty"
```

### 6. UI Component Level

#### Booking Invoice Tab (`app/dashboard/bookings/components/booking-invoice-tab.tsx`)
- **File**: [`app/dashboard/bookings/components/booking-invoice-tab.tsx`](app/dashboard/bookings/components/booking-invoice-tab.tsx:317)
- **Line**: 317
- **Issue**: Displays `item.qty` from line items.
```typescript
          <span className="text-gray-500 ml-1">x{item.qty}</span> // ← Uses "qty"
          <span className="font-medium">{(Number(item.amount) * Number(item.qty)).toLocaleString('en-US')} IQD</span>
```

#### Booking Information Form (`app/dashboard/bookings/components/booking-information-form.tsx`)
- **File**: [`app/dashboard/bookings/components/booking-information-form.tsx`](app/dashboard/bookings/components/booking-information-form.tsx:62)
- **Line**: 62
- **Issue**: Interface for catering selection uses `quantity`.
```typescript
interface CateringSelection extends Omit<CateringOnBooking, 'catering' | 'booking'> {
  catering: Catering;
  quantity: number; // ← Uses "quantity"
}
```
- **File**: [`app/dashboard/bookings/components/booking-information-form.tsx`](app/dashboard/bookings/components/booking-information-form.tsx:666)
- **Line**: 666
- **Issue**: Label uses "Quantity".
```typescript
<FormLabel>Quantity (People) *</FormLabel>
```
- **File**: [`app/dashboard/bookings/components/booking-information-form.tsx`](app/dashboard/bookings/components/booking-information-form.tsx:672)
- **Line**: 672
- **Issue**: Input value bound to `selection.quantity`.
```typescript
            <Input
              type="number"
              value={selection.quantity} // ← Uses "quantity"
              onChange={(e) => {
                const newQuantity = parseInt(e.target.value) || 1;
                if (newQuantity >= 1) {
                  updateCateringQuantity(index, newQuantity);
                }
              }}
```
- **File**: [`app/dashboard/bookings/components/booking-information-form.tsx`](app/dashboard/bookings/components/booking-information-form.tsx:700)
- **Line**: 700
- **Issue**: Calculation uses `selection.quantity`.
```typescript
                IQD {(selection.catering.pricePerPerson * selection.quantity).toFixed(2)}
```

#### Invoice Form Dialog (`app/dashboard/invoices/components/invoice-form-dialog.tsx`)
- **File**: [`app/dashboard/invoices/components/invoice-form-dialog.tsx`](app/dashboard/invoices/components/invoice-form-dialog.tsx:87)
- **Line**: 87
- **Issue**: Initial values for line items use `quantity`.
```typescript
      lineItems: [
        {
          description: '',
          amount: 0,
          quantity: 1, // ← Uses "quantity"
          isCatering: false,
        },
      ],
```
- **File**: [`app/dashboard/invoices/components/invoice-form-dialog.tsx`](app/dashboard/invoices/components/invoice-form-dialog.tsx:163)
- **Line**: 163
- **Issue**: Maps `cateringBooking.quantity` to line item quantity.
```typescript
            amount: cateringBooking.catering.pricePerPerson,
            quantity: cateringBooking.quantity, // ← Uses "quantity"
            isCatering: true,
            cateringId: cateringBooking.cateringId,
```

#### Catering Revenue Breakdown (`app/dashboard/invoices/components/catering-revenue-breakdown.tsx`)
- **File**: [`app/dashboard/invoices/components/catering-revenue-breakdown.tsx`](app/dashboard/invoices/components/catering-revenue-breakdown.tsx:59)
- **Line**: 59
- **Issue**: Uses `lineItem.quantity` for display and calculation.
```typescript
      const totalFirstPartyRevenue = lineItem.catering.firstPartyShare * lineItem.quantity; // ← Uses "quantity"
      const totalVendorShare = lineItem.catering.vendorShare * lineItem.quantity; // ← Uses "quantity"
      const totalRevenue = lineItem.amount * lineItem.quantity; // ← Uses "quantity"
```
- **File**: [`app/dashboard/invoices/components/catering-revenue-breakdown.tsx`](app/dashboard/invoices/components/catering-revenue-breakdown.tsx:169)
- **Line**: 169
- **Issue**: Displays `item.lineItem.quantity`.
```typescript
        {item.lineItem.quantity} people × {formatCurrency(item.catering.pricePerPerson)} per person
```

#### Line Item Manager (`app/dashboard/invoices/components/line-item-manager.tsx`)
- **File**: [`app/dashboard/invoices/components/line-item-manager.tsx`](app/dashboard/invoices/components/line-item-manager.tsx:66)
- **Line**: 66
- **Issue**: Initial value for quantity is 1.
```typescript
        description: '',
        amount: 0,
        quantity: 1, // ← Uses "quantity"
        isCatering: false,
```
- **File**: [`app/dashboard/invoices/components/line-item-manager.tsx`](app/dashboard/invoices/components/line-item-manager.tsx:85)
- **Line**: 85
- **Issue**: Calculation uses `item.quantity`.
```typescript
    // - Regular items: quantity × price per unit
    const quantity = Number(item.quantity) || 1; // ← Uses "quantity"
    const total = amount * quantity; // quantity × unit price
```
- **File**: [`app/dashboard/invoices/components/line-item-manager.tsx`](app/dashboard/invoices/components/line-item-manager.tsx:287)
- **Line**: 287
- **Issue**: Input name is `lineItems.${index}.quantity`.
```typescript
            <FormField
              control={form.control}
              name={`lineItems.${index}.quantity`} // ← Uses "quantity"
              render={({ field }) => (
                <FormItem className="col-span-12 md:col-span-2">
```
- **File**: [`app/dashboard/invoices/components/line-item-manager.tsx`](app/dashboard/invoices/components/line-item-manager.tsx:290)
- **Line**: 290
- **Issue**: Form label for quantity.
```typescript
                  <FormLabel>Quantity *</FormLabel>
```
- **File**: [`app/dashboard/invoices/components/line-item-manager.tsx`](app/dashboard/invoices/components/line-item-manager.tsx:363)
- **Line**: 363
- **Issue**: Formula display uses `item?.quantity`.
```typescript
          <p className="text-sm text-gray-500">
            Formula: {item?.quantity || 1} × {formatCurrency(item?.amount || 0)} = {formatCurrency(lineTotal)}
          </p>
```

#### Invoice Information Form (`app/dashboard/invoices/components/invoice-information-form.tsx`)
- **File**: [`app/dashboard/invoices/components/invoice-information-form.tsx`](app/dashboard/invoices/components/invoice-information-form.tsx:78)
- **Line**: 78
- **Issue**: Initial values for line items use `quantity`.
```typescript
      lineItems: [
        {
          description: '',
          amount: 0,
          quantity: 1, // ← Uses "quantity"
          isCatering: false,
        },
      ],
```
- **File**: [`app/dashboard/invoices/components/invoice-information-form.tsx`](app/dashboard/invoices/components/invoice-information-form.tsx:164)
- **Line**: 164
- **Issue**: Maps `cateringBooking.quantity` to line item quantity.
```typescript
            amount: cateringBooking.catering.pricePerPerson,
            quantity: cateringBooking.quantity, // Number of people from booking // ← Uses "quantity"
            isCatering: true,
            cateringId: cateringBooking.cateringId,
```
- **File**: [`app/dashboard/invoices/components/invoice-information-form.tsx`](app/dashboard/invoices/components/invoice-information-form.tsx:205)
- **Line**: 205
- **Issue**: Checks if `item.quantity` is different from `originalQuantity`.
```typescript
      // For catering items: amount should be pricePerPerson (unit price), quantity should be numberOfPeople from booking
      const isQuantityWrong = item.quantity !== originalQuantity; // ← Uses "quantity"
```

## Impact of Inconsistencies

### 1. Code Complexity
- **Mapping Required**: The API layer must explicitly map between `quantity` and `qty` fields
- **Type Mismatch**: TypeScript interfaces don\'t match the actual database schema
- **Validation Confusion**: Validation schemas use different naming than the database

### 2. Potential Bugs
- **Field Mismatch**: If the mapping in the API layer is missed, data could be saved incorrectly
- **Query Issues**: Direct database queries might use the wrong field name
- **Serialization Problems**: JSON serialization might not work as expected

### 3. Maintenance Burden
- **Cognitive Load**: Developers must remember which naming convention to use in each layer
- **Documentation Challenges**: API documentation becomes confusing with inconsistent field names
- **Onboarding Difficulty**: New developers face a steeper learning curve

### 4. Development Challenges
- **Type Safety Compromised**: TypeScript interfaces don\'t accurately represent the database schema
- **Refactoring Risk**: Changes to field names require updates across multiple layers
- **Testing Complexity**: Tests must account for the field name transformations

## Recommendations for Standardization

### Option 1: Standardize to "quantity" (Recommended)
1. **Database Schema**: Update `prisma/schema/invoice.prisma` to change `qty` to `quantity`
2. **Migration**: Create a database migration to rename the column
3. **TypeScript Interfaces**: Keep `lib/types.ts` as-is (already uses "quantity")
4. **Validation Schemas**: Keep validation schemas as-is (already use "quantity")
5. **API Layer**: Remove the mapping logic in `app/api/invoices/route.ts`

### Option 2: Standardize to "qty"
1. **Database Schema**: Keep `prisma/schema/invoice.prisma` as-is (already uses "qty")
2. **Database Schema**: Update `prisma/schema/catering.prisma` to change `quantity` to `qty`
3. **TypeScript Interfaces**: Update `lib/types.ts` to use `qty` instead of `quantity`
4. **Validation Schemas**: Update all validation schemas to use `qty`
5. **API Layer**: Update the mapping logic to be consistent

### Implementation Priority
1. **High Priority**: Database schema standardization (requires migration)
2. **Medium Priority**: TypeScript interface alignment
3. **Medium Priority**: Validation schema updates
4. **Low Priority**: API layer mapping removal/update

## Conclusion

The inconsistent use of "qty" versus "quantity" across different layers of the application creates significant technical debt. Standardizing to a single naming convention (preferably "quantity" for better readability) would improve code maintainability, reduce bugs, and make the codebase more approachable for new developers.

The most critical issue is the mismatch between the database schema (`qty`) and the TypeScript interfaces (`quantity`), which requires explicit mapping in the API layer and creates confusion throughout the application.