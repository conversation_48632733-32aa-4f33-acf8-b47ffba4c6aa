/**
 * Error monitoring and logging utilities for invoice management
 */

export interface InvoiceErrorContext {
  component: string;
  action: string;
  invoiceId?: number;
  bookingId?: number;
  customerId?: number;
  paymentId?: number;
  userId?: number;
  timestamp: string;
  userAgent: string;
  url: string;
  sessionId?: string;
}

export interface InvoiceErrorDetails {
  message: string;
  stack?: string;
  code?: string;
  statusCode?: number;
  category: 'validation' | 'network' | 'server' | 'authorization' | 'conflict' | 'not_found' | 'payment' | 'calculation' | 'unknown';
  severity: 'low' | 'medium' | 'high' | 'critical';
  retryable: boolean;
}

export interface InvoiceErrorReport {
  id: string;
  error: InvoiceErrorDetails;
  context: InvoiceErrorContext;
  metadata: Record<string, any>;
}

/**
 * Enhanced error monitoring service for invoice management
 */
export class InvoiceErrorMonitor {
  private static instance: InvoiceErrorMonitor;
  private errorQueue: InvoiceErrorReport[] = [];
  private maxQueueSize = 100;
  private flushInterval = 30000; // 30 seconds
  private flushTimer?: NodeJS.Timeout;

  private constructor() {
    this.startFlushTimer();
  }

  public static getInstance(): InvoiceErrorMonitor {
    if (!InvoiceErrorMonitor.instance) {
      InvoiceErrorMonitor.instance = new InvoiceErrorMonitor();
    }
    return InvoiceErrorMonitor.instance;
  }

  /**
   * Capture and categorize an error
   */
  public captureError(
    error: Error | unknown,
    context: Partial<InvoiceErrorContext>,
    metadata: Record<string, any> = {}
  ): string {
    const errorId = this.generateErrorId();
    const errorDetails = this.categorizeError(error);
    
    const errorReport: InvoiceErrorReport = {
      id: errorId,
      error: errorDetails,
      context: {
        component: 'invoice-management',
        action: 'unknown',
        timestamp: new Date().toISOString(),
        userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server',
        url: typeof window !== 'undefined' ? window.location.href : 'server',
        ...context
      },
      metadata: {
        ...metadata,
        errorId,
        buildVersion: process.env.NEXT_PUBLIC_BUILD_VERSION || 'unknown',
        environment: process.env.NODE_ENV || 'unknown'
      }
    };

    // Add to queue
    this.errorQueue.push(errorReport);
    
    // Trim queue if too large
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue = this.errorQueue.slice(-this.maxQueueSize);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Invoice Management Error:', errorReport);
    }

    // Immediate flush for critical errors
    if (errorDetails.severity === 'critical') {
      this.flushErrors();
    }

    return errorId;
  }

  /**
   * Categorize error based on type and message
   */
  private categorizeError(error: Error | unknown): InvoiceErrorDetails {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const stack = error instanceof Error ? error.stack : undefined;
    const lowerMessage = errorMessage.toLowerCase();

    // Extract status code if present
    const statusMatch = errorMessage.match(/(\d{3})/);
    const statusCode = statusMatch ? parseInt(statusMatch[1]) : undefined;

    // Invoice-specific validation errors
    if (lowerMessage.includes('validation') || lowerMessage.includes('invalid amount') || 
        lowerMessage.includes('required') || lowerMessage.includes('line item') ||
        lowerMessage.includes('zod') || lowerMessage.includes('invoice total')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'validation',
        severity: 'medium',
        retryable: false,
        code: 'INVOICE_VALIDATION_ERROR'
      };
    }

    // Payment-related errors
    if (lowerMessage.includes('payment') || lowerMessage.includes('overpayment') || 
        lowerMessage.includes('payment amount') || lowerMessage.includes('payment method') ||
        lowerMessage.includes('payment status') || lowerMessage.includes('balance')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'payment',
        severity: 'high',
        retryable: false,
        code: 'INVOICE_PAYMENT_ERROR'
      };
    }

    // Calculation errors
    if (lowerMessage.includes('calculation') || lowerMessage.includes('total') || 
        lowerMessage.includes('amount') || lowerMessage.includes('math') ||
        lowerMessage.includes('precision') || lowerMessage.includes('currency')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'calculation',
        severity: 'high',
        retryable: false,
        code: 'INVOICE_CALCULATION_ERROR'
      };
    }

    // Invoice relationship errors
    if (lowerMessage.includes('has payments') || lowerMessage.includes('cannot delete invoice') ||
        lowerMessage.includes('booking already has invoice') || lowerMessage.includes('referential integrity')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'conflict',
        severity: 'medium',
        retryable: false,
        code: 'INVOICE_RELATIONSHIP_ERROR'
      };
    }

    // Network errors
    if (lowerMessage.includes('network') || lowerMessage.includes('fetch') || 
        lowerMessage.includes('connection') || lowerMessage.includes('timeout')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'network',
        severity: 'high',
        retryable: true,
        code: 'INVOICE_NETWORK_ERROR'
      };
    }

    // Server errors (5xx)
    if ((statusCode && statusCode >= 500) || lowerMessage.includes('server error') || 
        lowerMessage.includes('database connection')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'server',
        severity: 'high',
        retryable: true,
        code: 'INVOICE_SERVER_ERROR'
      };
    }

    // Authorization errors
    if ((statusCode && (statusCode === 401 || statusCode === 403)) || 
        lowerMessage.includes('unauthorized') || lowerMessage.includes('forbidden')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'authorization',
        severity: 'critical',
        retryable: false,
        code: 'INVOICE_AUTHORIZATION_ERROR'
      };
    }

    // Duplicate invoice errors
    if ((statusCode && statusCode === 409) || lowerMessage.includes('already exists') || 
        lowerMessage.includes('unique') || lowerMessage.includes('duplicate invoice')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'conflict',
        severity: 'medium',
        retryable: false,
        code: 'INVOICE_DUPLICATE_ERROR'
      };
    }

    // Invoice not found errors
    if ((statusCode && statusCode === 404) || lowerMessage.includes('invoice not found') ||
        lowerMessage.includes('booking not found') || lowerMessage.includes('not found')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'not_found',
        severity: 'medium',
        retryable: false,
        code: 'INVOICE_NOT_FOUND_ERROR'
      };
    }

    // Unknown errors
    return {
      message: errorMessage,
      stack,
      statusCode,
      category: 'unknown',
      severity: 'high',
      retryable: true,
      code: 'INVOICE_UNKNOWN_ERROR'
    };
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `inv_err_${timestamp}_${random}`;
  }

  /**
   * Start the flush timer
   */
  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flushErrors();
    }, this.flushInterval);
  }

  /**
   * Flush errors to monitoring service
   */
  private async flushErrors(): Promise<void> {
    if (this.errorQueue.length === 0) return;

    const errorsToFlush = [...this.errorQueue];
    this.errorQueue = [];

    try {
      // In a real application, you would send these to your monitoring service
      // For now, we'll just log them
      if (process.env.NODE_ENV === 'development') {
        console.log('Flushing invoice errors to monitoring service:', errorsToFlush);
      }

      // Example: Send to monitoring service
      // await fetch('/api/monitoring/invoice-errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ errors: errorsToFlush })
      // });

    } catch (flushError) {
      console.error('Failed to flush invoice errors to monitoring service:', flushError);
      // Re-add errors to queue for retry
      this.errorQueue.unshift(...errorsToFlush);
    }
  }

  /**
   * Get error statistics
   */
  public getErrorStats(): {
    totalErrors: number;
    errorsByCategory: Record<string, number>;
    errorsBySeverity: Record<string, number>;
    recentErrors: InvoiceErrorReport[];
  } {
    const errorsByCategory: Record<string, number> = {};
    const errorsBySeverity: Record<string, number> = {};

    this.errorQueue.forEach(report => {
      errorsByCategory[report.error.category] = (errorsByCategory[report.error.category] || 0) + 1;
      errorsBySeverity[report.error.severity] = (errorsBySeverity[report.error.severity] || 0) + 1;
    });

    return {
      totalErrors: this.errorQueue.length,
      errorsByCategory,
      errorsBySeverity,
      recentErrors: this.errorQueue.slice(-10) // Last 10 errors
    };
  }

  /**
   * Clear error queue
   */
  public clearErrors(): void {
    this.errorQueue = [];
  }

  /**
   * Destroy the monitor
   */
  public destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    this.flushErrors();
  }
}

/**
 * Convenience function to capture invoice management errors
 */
export function captureInvoiceError(
  error: Error | unknown,
  action: string,
  metadata: Record<string, any> = {}
): string {
  const monitor = InvoiceErrorMonitor.getInstance();
  return monitor.captureError(error, { action }, metadata);
}

/**
 * Hook for using invoice error monitoring in React components
 */
export function useInvoiceErrorMonitor() {
  const monitor = InvoiceErrorMonitor.getInstance();

  const captureError = (
    error: Error | unknown,
    action: string,
    metadata: Record<string, any> = {}
  ) => {
    return monitor.captureError(error, { action }, metadata);
  };

  const getStats = () => monitor.getErrorStats();
  const clearErrors = () => monitor.clearErrors();

  return {
    captureError,
    getStats,
    clearErrors
  };
}

/**
 * Error boundary hook with invoice monitoring
 */
export function useInvoiceErrorBoundaryWithMonitoring() {
  const { captureError } = useInvoiceErrorMonitor();

  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    captureError(error, 'component-error', {
      componentStack: errorInfo.componentStack,
      errorBoundary: true
    });
  };

  return { handleError };
}