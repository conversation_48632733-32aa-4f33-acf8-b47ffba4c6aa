import { Zod<PERSON>rror, ZodIssue } from "zod";
import { FieldErro<PERSON>, FieldPath, FieldValues } from "react-hook-form";

/**
 * Utility functions for handling form errors and validation
 */

export interface FormErrorDetails {
  field: string;
  message: string;
  code?: string;
}

export interface ServerErrorResponse {
  error: string;
  details?: Record<string, string[]>;
  code?: string;
}

/**
 * Convert Zod validation errors to a more usable format
 */
export function formatZodErrors(error: ZodError): FormErrorDetails[] {
  return error.issues.map((issue: ZodIssue) => ({
    field: issue.path.join('.'),
    message: issue.message,
    code: issue.code,
  }));
}

/**
 * Convert server validation errors to React Hook Form format
 */
export function mapServerErrorsToForm<T extends FieldValues>(
  serverError: ServerErrorResponse,
  setError: (name: FieldPath<T>, error: { type: string; message: string }) => void
): void {
  if (serverError.details) {
    Object.entries(serverError.details).forEach(([field, messages]) => {
      if (messages && messages.length > 0) {
        setError(field as FieldPath<T>, {
          type: 'server',
          message: messages[0], // Use the first error message
        });
      }
    });
  }
}

/**
 * Get user-friendly error messages for common API errors
 */
export function getApiErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    // Handle catering-specific error types
    if (error.message.includes('Catering offer name already exists') || error.message.includes('catering') && error.message.includes('UNIQUE constraint failed')) {
      return 'This catering offer name is already taken. Please choose a different name.';
    }
    
    if (error.message.includes('catering offer has existing bookings') || error.message.includes('catering') && error.message.includes('FOREIGN KEY constraint failed')) {
      return 'Cannot delete this catering offer because it has existing bookings. Please cancel or complete these bookings first.';
    }
    
    if (error.message.includes('Catering offer not found')) {
      return 'The requested catering offer was not found. It may have been deleted by another user.';
    }
    
    // Handle catering revenue sharing validation errors
    if (error.message.includes('revenue sharing') || error.message.includes('share') && error.message.includes('100')) {
      return 'First party share and vendor share must add up to exactly 100% of the price per person.';
    }
    
    if (error.message.includes('price per person') || error.message.includes('pricing')) {
      return 'Invalid pricing information. Please ensure the price per person is a valid amount.';
    }
    
    if (error.message.includes('offer name') && error.message.includes('validation')) {
      return 'Invalid catering offer name. Please use only letters, numbers, spaces, and basic punctuation.';
    }
    
    // Handle resource-specific error types
    if (error.message.includes('Resource name already exists') || error.message.includes('UNIQUE constraint failed')) {
      return 'This resource name is already taken. Please choose a different one.';
    }
    
    if (error.message.includes('resource has existing bookings') || error.message.includes('FOREIGN KEY constraint failed')) {
      return 'Cannot delete this resource because it has existing bookings. Please cancel or complete these bookings first.';
    }
    
    if (error.message.includes('Invalid reference') || error.message.includes('amenities do not exist')) {
      return 'One or more selected amenities are no longer available. Please refresh and try again.';
    }
    
    if (error.message.includes('Resource not found')) {
      return 'The requested resource was not found. It may have been deleted by another user.';
    }
    
    // Handle resource type validation errors
    if (error.message.includes('resource type') || error.message.includes('type-specific')) {
      return 'Invalid resource type configuration. Please check the required fields for this resource type.';
    }
    
    if (error.message.includes('seating style') || error.message.includes('stage styles')) {
      return 'Invalid configuration for this resource type. Please check the seating style and stage options.';
    }
    
    if (error.message.includes('Number of chairs should be at least equal to number of desks')) {
      return 'For private offices, the number of chairs must be at least equal to the number of desks.';
    }
    
    // Handle network and connection errors
    if (error.message.includes('Network') || error.message.includes('fetch') || error.message.includes('connection')) {
      return 'Network connection error. Please check your internet connection and try again.';
    }
    
    if (error.message.includes('timeout') || error.message.includes('AbortError')) {
      return 'Request timed out. Please try again.';
    }
    
    // Handle authentication and authorization errors
    if (error.message.includes('401')) {
      return 'You are not authorized to perform this action. Please log in again.';
    }
    
    if (error.message.includes('403')) {
      return 'You do not have permission to manage resources.';
    }
    
    if (error.message.includes('404')) {
      return 'The requested resource was not found.';
    }
    
    if (error.message.includes('429')) {
      return 'Too many requests. Please wait a moment and try again.';
    }
    
    // Handle server errors
    if (error.message.includes('500') || error.message.includes('502') || error.message.includes('503')) {
      return 'Server error. Our team has been notified. Please try again later.';
    }
    
    if (error.message.includes('Database connection error')) {
      return 'Unable to connect to the database. Please try again in a moment.';
    }
    
    // Handle validation errors
    if (error.message.includes('validation') || error.message.includes('invalid')) {
      return 'Please check the resource information and try again.';
    }
    
    // Return the original message if it's user-friendly
    if (error.message.length < 100 && !error.message.includes('fetch') && !error.message.includes('stack')) {
      return error.message;
    }
  }
  
  return 'An unexpected error occurred. Please try again.';
}

/**
 * Validate field in real-time with debouncing
 */
export function createFieldValidator<T>(
  schema: any,
  field: keyof T,
  debounceMs: number = 300
) {
  let timeoutId: NodeJS.Timeout;
  
  return (value: any): Promise<string | undefined> => {
    return new Promise((resolve) => {
      clearTimeout(timeoutId);
      
      timeoutId = setTimeout(() => {
        try {
          schema.shape[field].parse(value);
          resolve(undefined);
        } catch (error) {
          if (error instanceof ZodError) {
            resolve(error.issues[0]?.message);
          } else {
            resolve('Invalid value');
          }
        }
      }, debounceMs);
    });
  };
}

/**
 * Check if form has any errors
 */
export function hasFormErrors<T extends FieldValues>(errors: FieldErrors<T>): boolean {
  return Object.keys(errors).length > 0;
}

/**
 * Get all error messages from form errors
 */
export function getFormErrorMessages<T extends FieldValues>(errors: FieldErrors<T>): string[] {
  return Object.values(errors)
    .map(error => error?.message)
    .filter((message): message is string => typeof message === 'string');
}

/**
 * Format validation error for display
 */
export function formatValidationError(error: unknown): string {
  if (error instanceof ZodError) {
    return error.issues.map(issue => issue.message).join(', ');
  }
  
  if (error instanceof Error) {
    return error.message;
  }
  
  return 'Validation failed';
}

/**
 * Create a comprehensive error summary for forms
 */
export function createErrorSummary<T extends FieldValues>(
  errors: FieldErrors<T>,
  serverError?: string
): {
  hasErrors: boolean;
  errorCount: number;
  messages: string[];
  summary: string;
} {
  const fieldErrors = getFormErrorMessages(errors);
  const allMessages = serverError ? [serverError, ...fieldErrors] : fieldErrors;
  
  return {
    hasErrors: allMessages.length > 0,
    errorCount: allMessages.length,
    messages: allMessages,
    summary: allMessages.length === 1 
      ? allMessages[0] 
      : `${allMessages.length} errors need to be fixed`,
  };
}

/**
 * Enhanced error categorization for resource management
 */
export interface ResourceErrorCategory {
  type: 'validation' | 'network' | 'server' | 'authorization' | 'conflict' | 'not_found' | 'unknown';
  severity: 'low' | 'medium' | 'high' | 'critical';
  retryable: boolean;
  userMessage: string;
  technicalMessage: string;
}

export function categorizeResourceError(error: unknown): ResourceErrorCategory {
  const errorMessage = error instanceof Error ? error.message : String(error);
  const lowerMessage = errorMessage.toLowerCase();

  // Catering-specific validation errors
  if (lowerMessage.includes('catering') && (lowerMessage.includes('validation') || lowerMessage.includes('invalid'))) {
    return {
      type: 'validation',
      severity: 'medium',
      retryable: false,
      userMessage: 'Please check the catering offer information and correct any errors.',
      technicalMessage: errorMessage
    };
  }

  // Revenue sharing validation errors
  if (lowerMessage.includes('revenue sharing') || lowerMessage.includes('share') && lowerMessage.includes('100')) {
    return {
      type: 'validation',
      severity: 'medium',
      retryable: false,
      userMessage: 'Revenue sharing percentages must add up to exactly 100%. Please adjust the values.',
      technicalMessage: errorMessage
    };
  }

  // Catering pricing validation errors
  if (lowerMessage.includes('price per person') || (lowerMessage.includes('catering') && lowerMessage.includes('pricing'))) {
    return {
      type: 'validation',
      severity: 'medium',
      retryable: false,
      userMessage: 'Invalid pricing information. Please ensure all prices are valid amounts.',
      technicalMessage: errorMessage
    };
  }

  // General validation errors
  if (lowerMessage.includes('validation') || lowerMessage.includes('invalid') || lowerMessage.includes('required')) {
    return {
      type: 'validation',
      severity: 'medium',
      retryable: false,
      userMessage: 'Please check the information and correct any errors.',
      technicalMessage: errorMessage
    };
  }

  // Resource type specific validation errors
  if (lowerMessage.includes('resource type') || lowerMessage.includes('seating style') || lowerMessage.includes('stage styles')) {
    return {
      type: 'validation',
      severity: 'medium',
      retryable: false,
      userMessage: 'Invalid configuration for this resource type. Please check the required fields.',
      technicalMessage: errorMessage
    };
  }

  // Network errors
  if (lowerMessage.includes('network') || lowerMessage.includes('fetch') || lowerMessage.includes('connection')) {
    return {
      type: 'network',
      severity: 'high',
      retryable: true,
      userMessage: 'Network connection issue. Please check your internet connection and try again.',
      technicalMessage: errorMessage
    };
  }

  // Server errors
  if (lowerMessage.includes('500') || lowerMessage.includes('502') || lowerMessage.includes('503') || lowerMessage.includes('server error')) {
    return {
      type: 'server',
      severity: 'high',
      retryable: true,
      userMessage: 'Server error. Our team has been notified. Please try again in a few moments.',
      technicalMessage: errorMessage
    };
  }

  // Authorization errors
  if (lowerMessage.includes('401') || lowerMessage.includes('403') || lowerMessage.includes('unauthorized')) {
    return {
      type: 'authorization',
      severity: 'critical',
      retryable: false,
      userMessage: 'You do not have permission to perform this action. Please contact your administrator.',
      technicalMessage: errorMessage
    };
  }

  // Conflict errors (duplicate names, foreign key constraints)
  if (lowerMessage.includes('already exists') || lowerMessage.includes('unique') || lowerMessage.includes('existing bookings')) {
    return {
      type: 'conflict',
      severity: 'medium',
      retryable: false,
      userMessage: lowerMessage.includes('existing bookings') 
        ? 'Cannot delete resource because it has existing bookings.'
        : 'This resource name is already taken. Please choose a different name.',
      technicalMessage: errorMessage
    };
  }

  // Not found errors
  if (lowerMessage.includes('not found') || lowerMessage.includes('404')) {
    return {
      type: 'not_found',
      severity: 'medium',
      retryable: false,
      userMessage: 'The requested resource was not found. It may have been deleted by another user.',
      technicalMessage: errorMessage
    };
  }

  // Unknown errors
  return {
    type: 'unknown',
    severity: 'high',
    retryable: true,
    userMessage: 'An unexpected error occurred. Please try again.',
    technicalMessage: errorMessage
  };
}

/**
 * Get retry strategy based on error category
 */
export function getRetryStrategy(errorCategory: ResourceErrorCategory): {
  shouldRetry: boolean;
  maxAttempts: number;
  baseDelay: number;
  backoffFactor: number;
} {
  if (!errorCategory.retryable) {
    return {
      shouldRetry: false,
      maxAttempts: 1,
      baseDelay: 0,
      backoffFactor: 1
    };
  }

  switch (errorCategory.type) {
    case 'network':
      return {
        shouldRetry: true,
        maxAttempts: 3,
        baseDelay: 1000,
        backoffFactor: 2
      };
    case 'server':
      return {
        shouldRetry: true,
        maxAttempts: 2,
        baseDelay: 2000,
        backoffFactor: 1.5
      };
    default:
      return {
        shouldRetry: true,
        maxAttempts: 2,
        baseDelay: 1000,
        backoffFactor: 1.5
      };
  }
}