/**
 * Error monitoring and logging utilities for customer management
 */

export interface CustomerErrorContext {
  component: string;
  action: string;
  customerId?: number;
  customerName?: string;
  userId?: number;
  timestamp: string;
  userAgent: string;
  url: string;
  sessionId?: string;
}

export interface CustomerErrorDetails {
  message: string;
  stack?: string;
  code?: string;
  statusCode?: number;
  category: 'validation' | 'network' | 'server' | 'authorization' | 'conflict' | 'not_found' | 'unknown';
  severity: 'low' | 'medium' | 'high' | 'critical';
  retryable: boolean;
}

export interface CustomerErrorReport {
  id: string;
  error: CustomerErrorDetails;
  context: CustomerErrorContext;
  metadata: Record<string, any>;
}

/**
 * Enhanced error monitoring service for customer management
 */
export class CustomerErrorMonitor {
  private static instance: CustomerErrorMonitor;
  private errorQueue: CustomerErrorReport[] = [];
  private maxQueueSize = 100;
  private flushInterval = 30000; // 30 seconds
  private flushTimer?: NodeJS.Timeout;

  private constructor() {
    this.startFlushTimer();
  }

  public static getInstance(): CustomerErrorMonitor {
    if (!CustomerErrorMonitor.instance) {
      CustomerErrorMonitor.instance = new CustomerErrorMonitor();
    }
    return CustomerErrorMonitor.instance;
  }

  /**
   * Capture and categorize an error
   */
  public captureError(
    error: Error | unknown,
    context: Partial<CustomerErrorContext>,
    metadata: Record<string, any> = {}
  ): string {
    const errorId = this.generateErrorId();
    const errorDetails = this.categorizeError(error);
    
    const errorReport: CustomerErrorReport = {
      id: errorId,
      error: errorDetails,
      context: {
        component: 'customer-management',
        action: 'unknown',
        timestamp: new Date().toISOString(),
        userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server',
        url: typeof window !== 'undefined' ? window.location.href : 'server',
        ...context
      },
      metadata: {
        ...metadata,
        errorId,
        buildVersion: process.env.NEXT_PUBLIC_BUILD_VERSION || 'unknown',
        environment: process.env.NODE_ENV || 'unknown'
      }
    };

    // Add to queue
    this.errorQueue.push(errorReport);
    
    // Trim queue if too large
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue = this.errorQueue.slice(-this.maxQueueSize);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Customer Management Error:', errorReport);
    }

    // Immediate flush for critical errors
    if (errorDetails.severity === 'critical') {
      this.flushErrors();
    }

    return errorId;
  }

  /**
   * Categorize error based on type and message
   */
  private categorizeError(error: Error | unknown): CustomerErrorDetails {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const stack = error instanceof Error ? error.stack : undefined;
    const lowerMessage = errorMessage.toLowerCase();

    // Extract status code if present
    const statusMatch = errorMessage.match(/(\d{3})/);
    const statusCode = statusMatch ? parseInt(statusMatch[1]) : undefined;

    // Customer-specific validation errors
    if (lowerMessage.includes('validation') || lowerMessage.includes('invalid email') || 
        lowerMessage.includes('required') || lowerMessage.includes('customer name') ||
        lowerMessage.includes('zod')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'validation',
        severity: 'medium',
        retryable: false,
        code: 'CUSTOMER_VALIDATION_ERROR'
      };
    }

    // Customer relationship errors
    if (lowerMessage.includes('has existing bookings') || lowerMessage.includes('cannot delete customer') ||
        lowerMessage.includes('referential integrity')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'conflict',
        severity: 'medium',
        retryable: false,
        code: 'CUSTOMER_RELATIONSHIP_ERROR'
      };
    }

    // Network errors
    if (lowerMessage.includes('network') || lowerMessage.includes('fetch') || 
        lowerMessage.includes('connection') || lowerMessage.includes('timeout')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'network',
        severity: 'high',
        retryable: true,
        code: 'CUSTOMER_NETWORK_ERROR'
      };
    }

    // Server errors (5xx)
    if ((statusCode && statusCode >= 500) || lowerMessage.includes('server error') || 
        lowerMessage.includes('database connection')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'server',
        severity: 'high',
        retryable: true,
        code: 'CUSTOMER_SERVER_ERROR'
      };
    }

    // Authorization errors
    if ((statusCode && (statusCode === 401 || statusCode === 403)) || 
        lowerMessage.includes('unauthorized') || lowerMessage.includes('forbidden')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'authorization',
        severity: 'critical',
        retryable: false,
        code: 'CUSTOMER_AUTHORIZATION_ERROR'
      };
    }

    // Duplicate customer errors
    if ((statusCode && statusCode === 409) || lowerMessage.includes('already exists') || 
        lowerMessage.includes('unique') || lowerMessage.includes('duplicate email')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'conflict',
        severity: 'medium',
        retryable: false,
        code: 'CUSTOMER_DUPLICATE_ERROR'
      };
    }

    // Customer not found errors
    if ((statusCode && statusCode === 404) || lowerMessage.includes('customer not found') ||
        lowerMessage.includes('not found')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'not_found',
        severity: 'medium',
        retryable: false,
        code: 'CUSTOMER_NOT_FOUND_ERROR'
      };
    }

    // Unknown errors
    return {
      message: errorMessage,
      stack,
      statusCode,
      category: 'unknown',
      severity: 'high',
      retryable: true,
      code: 'CUSTOMER_UNKNOWN_ERROR'
    };
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `cust_err_${timestamp}_${random}`;
  }

  /**
   * Start the flush timer
   */
  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flushErrors();
    }, this.flushInterval);
  }

  /**
   * Flush errors to monitoring service
   */
  private async flushErrors(): Promise<void> {
    if (this.errorQueue.length === 0) return;

    const errorsToFlush = [...this.errorQueue];
    this.errorQueue = [];

    try {
      // In a real application, you would send these to your monitoring service
      // For now, we'll just log them
      if (process.env.NODE_ENV === 'development') {
        console.log('Flushing customer errors to monitoring service:', errorsToFlush);
      }

      // Example: Send to monitoring service
      // await fetch('/api/monitoring/customer-errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ errors: errorsToFlush })
      // });

    } catch (flushError) {
      console.error('Failed to flush customer errors to monitoring service:', flushError);
      // Re-add errors to queue for retry
      this.errorQueue.unshift(...errorsToFlush);
    }
  }

  /**
   * Get error statistics
   */
  public getErrorStats(): {
    totalErrors: number;
    errorsByCategory: Record<string, number>;
    errorsBySeverity: Record<string, number>;
    recentErrors: CustomerErrorReport[];
  } {
    const errorsByCategory: Record<string, number> = {};
    const errorsBySeverity: Record<string, number> = {};

    this.errorQueue.forEach(report => {
      errorsByCategory[report.error.category] = (errorsByCategory[report.error.category] || 0) + 1;
      errorsBySeverity[report.error.severity] = (errorsBySeverity[report.error.severity] || 0) + 1;
    });

    return {
      totalErrors: this.errorQueue.length,
      errorsByCategory,
      errorsBySeverity,
      recentErrors: this.errorQueue.slice(-10) // Last 10 errors
    };
  }

  /**
   * Clear error queue
   */
  public clearErrors(): void {
    this.errorQueue = [];
  }

  /**
   * Destroy the monitor
   */
  public destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    this.flushErrors();
  }
}

/**
 * Convenience function to capture customer management errors
 */
export function captureCustomerError(
  error: Error | unknown,
  action: string,
  metadata: Record<string, any> = {}
): string {
  const monitor = CustomerErrorMonitor.getInstance();
  return monitor.captureError(error, { action }, metadata);
}

/**
 * Hook for using customer error monitoring in React components
 */
export function useCustomerErrorMonitor() {
  const monitor = CustomerErrorMonitor.getInstance();

  const captureError = (
    error: Error | unknown,
    action: string,
    metadata: Record<string, any> = {}
  ) => {
    return monitor.captureError(error, { action }, metadata);
  };

  const getStats = () => monitor.getErrorStats();
  const clearErrors = () => monitor.clearErrors();

  return {
    captureError,
    getStats,
    clearErrors
  };
}

/**
 * Error boundary hook with customer monitoring
 */
export function useCustomerErrorBoundaryWithMonitoring() {
  const { captureError } = useCustomerErrorMonitor();

  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    captureError(error, 'component-error', {
      componentStack: errorInfo.componentStack,
      errorBoundary: true
    });
  };

  return { handleError };
}