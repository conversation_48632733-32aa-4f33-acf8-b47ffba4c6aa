import {
  Projector,
  Presentation,
  Monitor,
  Table,
  Wifi,
  Wind,
  Tv,
  Mic,
  Volume2,
  Theater,
  Coffee,
  Droplets,
  UtensilsCrossed,
  Shield,
  Car,
  type LucideIcon
} from "lucide-react"
import { AmenityIcon } from "@/lib/types"

// Mapping between AmenityIcon enum values and Lucide React icons
export const AMENITY_ICON_MAP: Record<AmenityIcon, LucideIcon> = {
  PROJECTOR: Projector,
  WHITEBOARD: Presentation,
  SMARTBOARD: Monitor,
  TABLE: Table,
  WIFI: Wifi,
  AIR_CONDITIONER: Wind,
  TV: Tv,
  MICROPHONE: Mic,
  SPEAKER: Volume2,
  STAGE: Theater,
  COFFEE_MACHINE: Coffee,
  WATER_DISPENSER: Droplets,
  CATERING: UtensilsCrossed,
  SECURITY: Shield,
  PARKING: Car
}

// Helper function to get the Lucide icon component for an amenity icon
export const getAmenityIcon = (iconType: AmenityIcon): LucideIcon => {
  return AMENITY_ICON_MAP[iconType]
}

// Helper function to get all available amenity icons with their display names
export const getAmenityIconOptions = (): Array<{ value: AmenityIcon; label: string; icon: LucideIcon }> => {
  return Object.entries(AMENITY_ICON_MAP).map(([key, IconComponent]) => ({
    value: key as AmenityIcon,
    label: key.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
    icon: IconComponent
  }))
}

// Helper function to validate if an icon type is valid
export const isValidAmenityIcon = (icon: string): icon is AmenityIcon => {
  return Object.keys(AMENITY_ICON_MAP).includes(icon as AmenityIcon)
}