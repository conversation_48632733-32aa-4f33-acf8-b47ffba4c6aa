/**
 * Error monitoring and logging utilities for resource management
 */

export interface ErrorContext {
  component: string;
  action: string;
  resourceId?: number;
  resourceType?: string;
  userId?: number;
  timestamp: string;
  userAgent: string;
  url: string;
  sessionId?: string;
}

export interface ErrorDetails {
  message: string;
  stack?: string;
  code?: string;
  statusCode?: number;
  category: 'validation' | 'network' | 'server' | 'authorization' | 'conflict' | 'not_found' | 'unknown';
  severity: 'low' | 'medium' | 'high' | 'critical';
  retryable: boolean;
}

export interface ErrorReport {
  id: string;
  error: ErrorDetails;
  context: ErrorContext;
  metadata: Record<string, any>;
}

/**
 * Enhanced error monitoring service for resource management
 */
export class ResourceErrorMonitor {
  private static instance: ResourceErrorMonitor;
  private errorQueue: ErrorReport[] = [];
  private maxQueueSize = 100;
  private flushInterval = 30000; // 30 seconds
  private flushTimer?: NodeJS.Timeout;

  private constructor() {
    this.startFlushTimer();
  }

  public static getInstance(): ResourceErrorMonitor {
    if (!ResourceErrorMonitor.instance) {
      ResourceErrorMonitor.instance = new ResourceErrorMonitor();
    }
    return ResourceErrorMonitor.instance;
  }

  /**
   * Capture and categorize an error
   */
  public captureError(
    error: Error | unknown,
    context: Partial<ErrorContext>,
    metadata: Record<string, any> = {}
  ): string {
    const errorId = this.generateErrorId();
    const errorDetails = this.categorizeError(error);
    
    const errorReport: ErrorReport = {
      id: errorId,
      error: errorDetails,
      context: {
        component: 'resource-management',
        action: 'unknown',
        timestamp: new Date().toISOString(),
        userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server',
        url: typeof window !== 'undefined' ? window.location.href : 'server',
        ...context
      },
      metadata: {
        ...metadata,
        errorId,
        buildVersion: process.env.NEXT_PUBLIC_BUILD_VERSION || 'unknown',
        environment: process.env.NODE_ENV || 'unknown'
      }
    };

    // Add to queue
    this.errorQueue.push(errorReport);
    
    // Trim queue if too large
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue = this.errorQueue.slice(-this.maxQueueSize);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Resource Management Error:', errorReport);
    }

    // Immediate flush for critical errors
    if (errorDetails.severity === 'critical') {
      this.flushErrors();
    }

    return errorId;
  }

  /**
   * Categorize error based on type and message
   */
  private categorizeError(error: Error | unknown): ErrorDetails {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const stack = error instanceof Error ? error.stack : undefined;
    const lowerMessage = errorMessage.toLowerCase();

    // Extract status code if present
    const statusMatch = errorMessage.match(/(\d{3})/);
    const statusCode = statusMatch ? parseInt(statusMatch[1]) : undefined;

    // Validation errors
    if (lowerMessage.includes('validation') || lowerMessage.includes('invalid') || 
        lowerMessage.includes('required') || lowerMessage.includes('zod')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'validation',
        severity: 'medium',
        retryable: false,
        code: 'VALIDATION_ERROR'
      };
    }

    // Network errors
    if (lowerMessage.includes('network') || lowerMessage.includes('fetch') || 
        lowerMessage.includes('connection') || lowerMessage.includes('timeout')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'network',
        severity: 'high',
        retryable: true,
        code: 'NETWORK_ERROR'
      };
    }

    // Server errors (5xx)
    if ((statusCode && statusCode >= 500) || lowerMessage.includes('server error') || 
        lowerMessage.includes('database connection')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'server',
        severity: 'high',
        retryable: true,
        code: 'SERVER_ERROR'
      };
    }

    // Authorization errors
    if ((statusCode && (statusCode === 401 || statusCode === 403)) || 
        lowerMessage.includes('unauthorized') || lowerMessage.includes('forbidden')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'authorization',
        severity: 'critical',
        retryable: false,
        code: 'AUTHORIZATION_ERROR'
      };
    }

    // Conflict errors
    if ((statusCode && statusCode === 409) || lowerMessage.includes('already exists') || 
        lowerMessage.includes('unique') || lowerMessage.includes('constraint')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'conflict',
        severity: 'medium',
        retryable: false,
        code: 'CONFLICT_ERROR'
      };
    }

    // Not found errors
    if ((statusCode && statusCode === 404) || lowerMessage.includes('not found')) {
      return {
        message: errorMessage,
        stack,
        statusCode,
        category: 'not_found',
        severity: 'medium',
        retryable: false,
        code: 'NOT_FOUND_ERROR'
      };
    }

    // Unknown errors
    return {
      message: errorMessage,
      stack,
      statusCode,
      category: 'unknown',
      severity: 'high',
      retryable: true,
      code: 'UNKNOWN_ERROR'
    };
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `err_${timestamp}_${random}`;
  }

  /**
   * Start the flush timer
   */
  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flushErrors();
    }, this.flushInterval);
  }

  /**
   * Flush errors to monitoring service
   */
  private async flushErrors(): Promise<void> {
    if (this.errorQueue.length === 0) return;

    const errorsToFlush = [...this.errorQueue];
    this.errorQueue = [];

    try {
      // In a real application, you would send these to your monitoring service
      // For now, we'll just log them
      if (process.env.NODE_ENV === 'development') {
        console.log('Flushing errors to monitoring service:', errorsToFlush);
      }

      // Example: Send to monitoring service
      // await fetch('/api/monitoring/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ errors: errorsToFlush })
      // });

    } catch (flushError) {
      console.error('Failed to flush errors to monitoring service:', flushError);
      // Re-add errors to queue for retry
      this.errorQueue.unshift(...errorsToFlush);
    }
  }

  /**
   * Get error statistics
   */
  public getErrorStats(): {
    totalErrors: number;
    errorsByCategory: Record<string, number>;
    errorsBySeverity: Record<string, number>;
    recentErrors: ErrorReport[];
  } {
    const errorsByCategory: Record<string, number> = {};
    const errorsBySeverity: Record<string, number> = {};

    this.errorQueue.forEach(report => {
      errorsByCategory[report.error.category] = (errorsByCategory[report.error.category] || 0) + 1;
      errorsBySeverity[report.error.severity] = (errorsBySeverity[report.error.severity] || 0) + 1;
    });

    return {
      totalErrors: this.errorQueue.length,
      errorsByCategory,
      errorsBySeverity,
      recentErrors: this.errorQueue.slice(-10) // Last 10 errors
    };
  }

  /**
   * Clear error queue
   */
  public clearErrors(): void {
    this.errorQueue = [];
  }

  /**
   * Destroy the monitor
   */
  public destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    this.flushErrors();
  }
}

/**
 * Convenience function to capture resource management errors
 */
export function captureResourceError(
  error: Error | unknown,
  action: string,
  metadata: Record<string, any> = {}
): string {
  const monitor = ResourceErrorMonitor.getInstance();
  return monitor.captureError(error, { action }, metadata);
}

/**
 * Hook for using error monitoring in React components
 */
export function useResourceErrorMonitor() {
  const monitor = ResourceErrorMonitor.getInstance();

  const captureError = (
    error: Error | unknown,
    action: string,
    metadata: Record<string, any> = {}
  ) => {
    return monitor.captureError(error, { action }, metadata);
  };

  const getStats = () => monitor.getErrorStats();
  const clearErrors = () => monitor.clearErrors();

  return {
    captureError,
    getStats,
    clearErrors
  };
}

/**
 * Error boundary hook with monitoring
 */
export function useErrorBoundaryWithMonitoring() {
  const { captureError } = useResourceErrorMonitor();

  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    captureError(error, 'component-error', {
      componentStack: errorInfo.componentStack,
      errorBoundary: true
    });
  };

  return { handleError };
}