import { RevenueSplit } from "../types";

// Currency configuration for IQD (Iraqi Dinar)
export const CURRENCY_CONFIG = {
  symbol: "IQD",
  decimalPlaces: 0, // IQD typically doesn't use decimal places
  thousandsSeparator: ",",
  decimalSeparator: ".",
  locale: "en-IQ",
  currency: "IQD",
} as const;

/**
 * Format a number as currency with proper formatting
 * @param amount - The amount to format
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number): string => {
  if (!Number.isFinite(amount)) {
    return "0 IQD";
  }

  // Round to whole number for IQD and use same pattern as formatResourcePrice
  const roundedAmount = Math.round(amount);
  return `${roundedAmount.toLocaleString()} IQD`;
};

/**
 * Format a percentage with proper formatting
 * @param percentage - The percentage to format (0-100)
 * @returns Formatted percentage string
 */
export const formatPercentage = (percentage: number): string => {
  if (!Number.isFinite(percentage)) {
    return "0%";
  }

  return `${Math.round(percentage)}%`;
};

/**
 * Parse a currency string to a number
 * @param currencyString - The currency string to parse
 * @returns Parsed number or 0 if invalid
 */
export const parseCurrency = (currencyString: string): number => {
  if (!currencyString || typeof currencyString !== "string") {
    return 0;
  }

  // Remove IQD, commas, and spaces
  const cleanString = currencyString
    .replace(/IQD|,|\s/g, "")
    .replace(/[^\d]/g, "");

  const parsed = parseInt(cleanString, 10);
  return Number.isFinite(parsed) ? parsed : 0;
};

/**
 * Calculate revenue split based on quantity and fixed share amounts per person
 * @param quantity - Number of people
 * @param firstPartySharePerPerson - First party share amount per person in IQD
 * @param vendorSharePerPerson - Vendor share amount per person in IQD
 * @returns Revenue split breakdown
 */
export const calculateRevenueSplit = (
  quantity: number,
  firstPartySharePerPerson: number,
  vendorSharePerPerson: number
): RevenueSplit => {
  if (!Number.isFinite(quantity) || quantity < 0) {
    quantity = 0;
  }

  if (!Number.isFinite(firstPartySharePerPerson) || firstPartySharePerPerson < 0) {
    firstPartySharePerPerson = 0;
  }

  if (!Number.isFinite(vendorSharePerPerson) || vendorSharePerPerson < 0) {
    vendorSharePerPerson = 0;
  }

  const firstPartyAmount = Math.round(firstPartySharePerPerson * quantity);
  const vendorAmount = Math.round(vendorSharePerPerson * quantity);
  const totalAmount = firstPartyAmount + vendorAmount;

  return {
    totalAmount,
    firstPartyAmount,
    vendorAmount,
    firstPartySharePerPerson,
    vendorSharePerPerson,
  };
};

/**
 * Calculate total catering cost for a given quantity
 * @param pricePerPerson - Price per person
 * @param quantity - Number of people
 * @returns Total cost
 */
export const calculateCateringCost = (
  pricePerPerson: number,
  quantity: number
): number => {
  if (!Number.isFinite(pricePerPerson) || pricePerPerson < 0) {
    pricePerPerson = 0;
  }

  if (!Number.isFinite(quantity) || quantity < 0) {
    quantity = 0;
  }

  return Math.round(pricePerPerson * quantity);
};

/**
 * Calculate catering cost with revenue split using fixed amounts per person
 * @param quantity - Number of people
 * @param firstPartySharePerPerson - First party share amount per person in IQD
 * @param vendorSharePerPerson - Vendor share amount per person in IQD
 * @returns Revenue split for the catering cost
 */
export const calculateCateringRevenueSplit = (
  quantity: number,
  firstPartySharePerPerson: number,
  vendorSharePerPerson: number
): RevenueSplit => {
  return calculateRevenueSplit(quantity, firstPartySharePerPerson, vendorSharePerPerson);
};

/**
 * Validate that revenue sharing amounts add up to the price per person
 * @param firstPartyShare - First party share amount per person
 * @param vendorShare - Vendor share amount per person
 * @param pricePerPerson - Total price per person
 * @returns True if valid, false otherwise
 */
export const validateRevenueSharing = (
  firstPartyShare: number,
  vendorShare: number,
  pricePerPerson: number
): boolean => {
  if (!Number.isFinite(firstPartyShare) || !Number.isFinite(vendorShare) || !Number.isFinite(pricePerPerson)) {
    return false;
  }

  return Math.abs((firstPartyShare + vendorShare) - pricePerPerson) < 0.01; // Allow for floating point precision
};

/**
 * Format monetary precision to ensure whole numbers for IQD
 * @param amount - The amount to format
 * @returns Amount as whole number
 */
export const formatMonetaryPrecision = (amount: number): number => {
  if (!Number.isFinite(amount)) {
    return 0;
  }

  return Math.round(amount);
};

/**
 * Validate monetary amount for catering pricing in IQD
 * @param amount - The amount to validate
 * @returns True if valid, false otherwise
 */
export const validateMonetaryAmount = (amount: number): boolean => {
  return Number.isFinite(amount) && amount >= 1 && amount <= 999999;
};

/**
 * Validate share amount for revenue sharing
 * @param shareAmount - The share amount to validate
 * @returns True if valid, false otherwise
 */
export const validateShareAmount = (shareAmount: number): boolean => {
  return Number.isFinite(shareAmount) && shareAmount >= 0 && shareAmount <= 999999;
};

/**
 * Calculate percentage breakdown from fixed amounts with error handling
 * @param firstPartyShare - First party share amount
 * @param vendorShare - Vendor share amount
 * @returns Percentage breakdown for display
 */
export const calculatePercentageBreakdown = (
  firstPartyShare: number,
  vendorShare: number
): { firstPartyPercentage: number; vendorPercentage: number } => {
  // Validate inputs
  if (!Number.isFinite(firstPartyShare) || !Number.isFinite(vendorShare)) {
    console.warn('Invalid share amounts provided to calculatePercentageBreakdown:', { firstPartyShare, vendorShare });
    return { firstPartyPercentage: 0, vendorPercentage: 0 };
  }

  if (firstPartyShare < 0 || vendorShare < 0) {
    console.warn('Negative share amounts provided to calculatePercentageBreakdown:', { firstPartyShare, vendorShare });
    return { firstPartyPercentage: 0, vendorPercentage: 0 };
  }

  const total = firstPartyShare + vendorShare;
  
  if (total === 0) {
    return { firstPartyPercentage: 0, vendorPercentage: 0 };
  }

  try {
    const firstPartyPercentage = Math.round((firstPartyShare / total) * 100);
    const vendorPercentage = Math.round((vendorShare / total) * 100);

    // Ensure percentages are valid
    if (!Number.isFinite(firstPartyPercentage) || !Number.isFinite(vendorPercentage)) {
      console.error('Invalid percentage calculation result:', { firstPartyPercentage, vendorPercentage, total });
      return { firstPartyPercentage: 0, vendorPercentage: 0 };
    }

    return { firstPartyPercentage, vendorPercentage };
  } catch (error) {
    console.error('Error calculating percentage breakdown:', error, { firstPartyShare, vendorShare, total });
    return { firstPartyPercentage: 0, vendorPercentage: 0 };
  }
};

/**
 * Calculate revenue split for booking system integration with detailed breakdown
 * @param cateringOffer - The catering offer details
 * @param quantity - Number of people for the booking
 * @returns Detailed revenue split for booking system
 */
export const calculateBookingRevenueSplit = (
  cateringOffer: {
    pricePerPerson: number;
    firstPartyShare: number;
    vendorShare: number;
  },
  quantity: number
): {
  totalCost: number;
  firstPartyRevenue: number;
  vendorRevenue: number;
  breakdown: {
    pricePerPerson: number;
    firstPartySharePerPerson: number;
    vendorSharePerPerson: number;
    quantity: number;
  };
  percentages: {
    firstPartyPercentage: number;
    vendorPercentage: number;
  };
} => {
  if (!Number.isFinite(quantity) || quantity < 0) {
    quantity = 0;
  }

  const { pricePerPerson, firstPartyShare, vendorShare } = cateringOffer;

  if (!Number.isFinite(pricePerPerson) || pricePerPerson < 0) {
    return {
      totalCost: 0,
      firstPartyRevenue: 0,
      vendorRevenue: 0,
      breakdown: { pricePerPerson: 0, firstPartySharePerPerson: 0, vendorSharePerPerson: 0, quantity },
      percentages: { firstPartyPercentage: 0, vendorPercentage: 0 }
    };
  }

  const totalCost = Math.round(pricePerPerson * quantity);
  const firstPartyRevenue = Math.round(firstPartyShare * quantity);
  const vendorRevenue = Math.round(vendorShare * quantity);
  const percentages = calculatePercentageBreakdown(firstPartyShare, vendorShare);

  return {
    totalCost,
    firstPartyRevenue,
    vendorRevenue,
    breakdown: {
      pricePerPerson,
      firstPartySharePerPerson: firstPartyShare,
      vendorSharePerPerson: vendorShare,
      quantity
    },
    percentages
  };
};

/**
 * Validate catering offer configuration for booking system compatibility
 * @param cateringOffer - The catering offer to validate
 * @returns Validation result with specific booking system requirements
 */
export const validateCateringOfferForBooking = (cateringOffer: {
  pricePerPerson: number;
  firstPartyShare: number;
  vendorShare: number;
}): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate price per person
  if (!validateMonetaryAmount(cateringOffer.pricePerPerson)) {
    errors.push('Price per person must be between 1 and 999,999 IQD');
  }

  // Validate share amounts
  if (!validateShareAmount(cateringOffer.firstPartyShare)) {
    errors.push('First party share must be between 0 and 999,999 IQD');
  }

  if (!validateShareAmount(cateringOffer.vendorShare)) {
    errors.push('Vendor share must be between 0 and 999,999 IQD');
  }

  // Validate revenue sharing total
  if (!validateRevenueSharing(cateringOffer.firstPartyShare, cateringOffer.vendorShare, cateringOffer.pricePerPerson)) {
    errors.push('First party share and vendor share must add up to exactly the price per person');
  }

  // Check for potential booking system issues
  if (cateringOffer.firstPartyShare === 0 && cateringOffer.vendorShare === 0) {
    errors.push('At least one party must receive revenue (cannot have 0-0 split)');
  }

  // Warnings for unusual configurations
  if (cateringOffer.firstPartyShare === 0) {
    warnings.push('First party receives no revenue from this catering offer');
  }

  if (cateringOffer.vendorShare === 0) {
    warnings.push('Vendor receives no revenue from this catering offer');
  }

  const percentages = calculatePercentageBreakdown(cateringOffer.firstPartyShare, cateringOffer.vendorShare);
  if (percentages.firstPartyPercentage > 90) {
    warnings.push('First party receives more than 90% of revenue');
  }

  if (percentages.vendorPercentage > 90) {
    warnings.push('Vendor receives more than 90% of revenue');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Calculate total revenue impact for multiple catering items in a booking
 * @param cateringItems - Array of catering items with quantities
 * @returns Aggregated revenue split for the entire booking
 */
export const calculateMultipleCateringRevenue = (
  cateringItems: Array<{
    cateringOffer: {
      pricePerPerson: number;
      firstPartyShare: number;
      vendorShare: number;
    };
    quantity: number;
  }>
): {
  totalCost: number;
  totalFirstPartyRevenue: number;
  totalVendorRevenue: number;
  itemBreakdown: Array<{
    itemCost: number;
    firstPartyRevenue: number;
    vendorRevenue: number;
    quantity: number;
  }>;
  overallPercentages: {
    firstPartyPercentage: number;
    vendorPercentage: number;
  };
} => {
  let totalCost = 0;
  let totalFirstPartyRevenue = 0;
  let totalVendorRevenue = 0;
  const itemBreakdown: Array<{
    itemCost: number;
    firstPartyRevenue: number;
    vendorRevenue: number;
    quantity: number;
  }> = [];

  for (const item of cateringItems) {
    const itemRevenue = calculateBookingRevenueSplit(item.cateringOffer, item.quantity);
    
    totalCost += itemRevenue.totalCost;
    totalFirstPartyRevenue += itemRevenue.firstPartyRevenue;
    totalVendorRevenue += itemRevenue.vendorRevenue;

    itemBreakdown.push({
      itemCost: itemRevenue.totalCost,
      firstPartyRevenue: itemRevenue.firstPartyRevenue,
      vendorRevenue: itemRevenue.vendorRevenue,
      quantity: item.quantity
    });
  }

  const overallPercentages = calculatePercentageBreakdown(totalFirstPartyRevenue, totalVendorRevenue);

  return {
    totalCost,
    totalFirstPartyRevenue,
    totalVendorRevenue,
    itemBreakdown,
    overallPercentages
  };
};

/**
 * Enhanced validation for catering form data with detailed error messages
 * @param data - The catering form data to validate
 * @returns Validation result with detailed error information
 */
export const validateCateringFormData = (data: {
  offerName: string;
  pricePerPerson: number;
  firstPartyShare: number;
  vendorShare: number;
}): {
  isValid: boolean;
  errors: Record<string, string[]>;
  summary: string;
} => {
  const errors: Record<string, string[]> = {};

  // Validate offer name
  if (!data.offerName || typeof data.offerName !== 'string') {
    errors.offerName = ['Offer name is required'];
  } else {
    const trimmedName = data.offerName.trim();
    if (trimmedName.length === 0) {
      errors.offerName = ['Offer name cannot be empty'];
    } else if (trimmedName.length > 100) {
      errors.offerName = ['Offer name must be less than 100 characters'];
    } else if (!/^[a-zA-Z0-9\s\-_&()]+$/.test(trimmedName)) {
      errors.offerName = ['Name can only contain letters, numbers, spaces, and basic punctuation'];
    } else if (trimmedName.toLowerCase().includes('test')) {
      errors.offerName = ['Name cannot contain "test"'];
    } else if (/^\d+$/.test(trimmedName)) {
      errors.offerName = ['Name cannot be only numbers'];
    }
  }

  // Validate price per person
  if (!Number.isFinite(data.pricePerPerson)) {
    errors.pricePerPerson = ['Price per person must be a valid number'];
  } else if (data.pricePerPerson < 1) {
    errors.pricePerPerson = ['Price per person must be at least 1 IQD'];
  } else if (data.pricePerPerson > 9999999) {
    errors.pricePerPerson = ['Price per person cannot exceed 999,999 IQD'];
  }

  // Validate first party share
  if (!Number.isFinite(data.firstPartyShare)) {
    errors.firstPartyShare = ['First party share must be a valid number'];
  } else if (data.firstPartyShare < 0) {
    errors.firstPartyShare = ['First party share must be at least 0 IQD'];
  } else if (data.firstPartyShare > 9999999) {
    errors.firstPartyShare = ['First party share cannot exceed 999,999 IQD'];
  }

  // Validate vendor share
  if (!Number.isFinite(data.vendorShare)) {
    errors.vendorShare = ['Vendor share must be a valid number'];
  } else if (data.vendorShare < 0) {
    errors.vendorShare = ['Vendor share must be at least 0 IQD'];
  } else if (data.vendorShare > 9999999) {
    errors.vendorShare = ['Vendor share cannot exceed 999,999 IQD'];
  }

  // Validate revenue sharing total
  if (Number.isFinite(data.pricePerPerson) && Number.isFinite(data.firstPartyShare) && Number.isFinite(data.vendorShare)) {
    const total = data.firstPartyShare + data.vendorShare;
    if (Math.abs(total - data.pricePerPerson) > 0.01) {
      errors.vendorShare = errors.vendorShare || [];
      errors.vendorShare.push('First party share and vendor share must add up to exactly the price per person');
    }
  }

  const errorCount = Object.keys(errors).length;
  const isValid = errorCount === 0;
  
  let summary = '';
  if (!isValid) {
    const totalErrors = Object.values(errors).flat().length;
    summary = totalErrors === 1 
      ? Object.values(errors).flat()[0] 
      : `${totalErrors} validation errors need to be fixed`;
  }

  return {
    isValid,
    errors,
    summary
  };
};
