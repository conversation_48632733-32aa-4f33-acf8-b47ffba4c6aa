/**
 * Enhanced error monitoring and feedback system specifically for catering management
 */

export interface CateringErrorContext {
  operation: 'create' | 'update' | 'delete' | 'fetch' | 'search';
  cateringId?: number;
  cateringName?: string;
  formData?: any;
  searchQuery?: string;
  userId?: number;
  timestamp: Date;
  userAgent: string;
  url: string;
}

export interface CateringErrorReport {
  id: string;
  error: string;
  stack?: string;
  context: CateringErrorContext;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'validation' | 'network' | 'server' | 'authorization' | 'conflict' | 'not_found' | 'unknown';
  retryable: boolean;
  userMessage: string;
  technicalMessage: string;
  metadata: Record<string, any>;
}

export interface CateringErrorStats {
  totalErrors: number;
  errorsByCategory: Record<string, number>;
  errorsByOperation: Record<string, number>;
  recentErrors: CateringErrorReport[];
  errorRate: number;
  lastErrorTime?: Date;
}

/**
 * Singleton class for monitoring catering-specific errors
 */
export class CateringErrorMonitor {
  private static instance: CateringErrorMonitor;
  private errorQueue: CateringErrorReport[] = [];
  private maxQueueSize = 100;
  private errorCallbacks: ((error: CateringErrorReport) => void)[] = [];

  public static getInstance(): CateringErrorMonitor {
    if (!CateringErrorMonitor.instance) {
      CateringErrorMonitor.instance = new CateringErrorMonitor();
    }
    return CateringErrorMonitor.instance;
  }

  /**
   * Capture and categorize a catering-related error
   */
  public captureError(
    error: Error,
    context: Partial<CateringErrorContext>,
    metadata: Record<string, any> = {}
  ): CateringErrorReport {
    const errorId = `catering_${context.operation || 'unknown'}_${Date.now().toString(36)}_${Math.random().toString(36).substr(2, 5)}`;
    
    const fullContext: CateringErrorContext = {
      operation: 'fetch',
      timestamp: new Date(),
      userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server',
      url: typeof window !== 'undefined' ? window.location.href : 'server',
      ...context
    };

    const errorReport: CateringErrorReport = {
      id: errorId,
      error: error.message,
      stack: error.stack,
      context: fullContext,
      ...this.categorizeError(error),
      metadata: {
        ...metadata,
        errorType: error.constructor.name,
        timestamp: fullContext.timestamp.toISOString(),
      }
    };

    // Add to queue
    this.errorQueue.push(errorReport);
    
    // Maintain queue size
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift();
    }

    // Notify callbacks
    this.errorCallbacks.forEach(callback => {
      try {
        callback(errorReport);
      } catch (callbackError) {
        console.error('Error in error callback:', callbackError);
      }
    });

    // Log in development
    if (process.env.NODE_ENV === 'development') {
      console.group(`🍽️ Catering Error [${errorReport.severity.toUpperCase()}]`);
      console.error('Error:', errorReport.error);
      console.log('Context:', errorReport.context);
      console.log('Category:', errorReport.category);
      console.log('Retryable:', errorReport.retryable);
      console.log('User Message:', errorReport.userMessage);
      if (errorReport.stack) {
        console.log('Stack:', errorReport.stack);
      }
      console.groupEnd();
    }

    return errorReport;
  }

  /**
   * Categorize catering errors with specific handling
   */
  private categorizeError(error: Error): Pick<CateringErrorReport, 'severity' | 'category' | 'retryable' | 'userMessage' | 'technicalMessage'> {
    const message = error.message.toLowerCase();

    // Catering-specific validation errors
    if (message.includes('catering') && (message.includes('validation') || message.includes('invalid'))) {
      return {
        severity: 'medium',
        category: 'validation',
        retryable: false,
        userMessage: 'Please check the catering offer information and correct any validation errors.',
        technicalMessage: error.message
      };
    }

    // Revenue sharing validation errors
    if (message.includes('revenue sharing') || message.includes('share') && message.includes('100')) {
      return {
        severity: 'medium',
        category: 'validation',
        retryable: false,
        userMessage: 'Revenue sharing percentages must add up to exactly 100%. Please adjust the first party and vendor share values.',
        technicalMessage: error.message
      };
    }

    // Catering pricing errors
    if (message.includes('price per person') || (message.includes('catering') && message.includes('pricing'))) {
      return {
        severity: 'medium',
        category: 'validation',
        retryable: false,
        userMessage: 'Invalid pricing information. Please ensure the price per person is a valid amount in IQD.',
        technicalMessage: error.message
      };
    }

    // Catering name validation errors
    if (message.includes('offer name') || (message.includes('catering') && message.includes('name'))) {
      return {
        severity: 'medium',
        category: 'validation',
        retryable: false,
        userMessage: 'Invalid catering offer name. Please use only letters, numbers, spaces, and basic punctuation.',
        technicalMessage: error.message
      };
    }

    // Duplicate catering offer errors
    if (message.includes('catering') && (message.includes('already exists') || message.includes('unique'))) {
      return {
        severity: 'medium',
        category: 'conflict',
        retryable: false,
        userMessage: 'A catering offer with this name already exists. Please choose a different name.',
        technicalMessage: error.message
      };
    }

    // Catering booking constraint errors
    if (message.includes('catering') && (message.includes('existing bookings') || message.includes('constraint'))) {
      return {
        severity: 'high',
        category: 'conflict',
        retryable: false,
        userMessage: 'Cannot delete this catering offer because it is associated with existing bookings. Please remove these associations first.',
        technicalMessage: error.message
      };
    }

    // Catering not found errors
    if (message.includes('catering') && message.includes('not found')) {
      return {
        severity: 'medium',
        category: 'not_found',
        retryable: false,
        userMessage: 'The requested catering offer was not found. It may have been deleted by another user.',
        technicalMessage: error.message
      };
    }

    // Network errors
    if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
      return {
        severity: 'high',
        category: 'network',
        retryable: true,
        userMessage: 'Network connection issue. Please check your internet connection and try again.',
        technicalMessage: error.message
      };
    }

    // Server errors
    if (message.includes('500') || message.includes('502') || message.includes('503') || message.includes('server error')) {
      return {
        severity: 'high',
        category: 'server',
        retryable: true,
        userMessage: 'Server error. Our team has been notified. Please try again in a few moments.',
        technicalMessage: error.message
      };
    }

    // Authorization errors
    if (message.includes('401') || message.includes('403') || message.includes('unauthorized')) {
      return {
        severity: 'critical',
        category: 'authorization',
        retryable: false,
        userMessage: 'You do not have permission to manage catering offers. Please contact your administrator.',
        technicalMessage: error.message
      };
    }

    // General validation errors
    if (message.includes('validation') || message.includes('invalid') || message.includes('required')) {
      return {
        severity: 'medium',
        category: 'validation',
        retryable: false,
        userMessage: 'Please check the information and correct any validation errors.',
        technicalMessage: error.message
      };
    }

    // Unknown errors
    return {
      severity: 'high',
      category: 'unknown',
      retryable: true,
      userMessage: 'An unexpected error occurred while managing catering offers. Please try again.',
      technicalMessage: error.message
    };
  }

  /**
   * Get comprehensive error statistics
   */
  public getStats(): CateringErrorStats {
    const errorsByCategory: Record<string, number> = {};
    const errorsByOperation: Record<string, number> = {};

    this.errorQueue.forEach(error => {
      errorsByCategory[error.category] = (errorsByCategory[error.category] || 0) + 1;
      errorsByOperation[error.context.operation] = (errorsByOperation[error.context.operation] || 0) + 1;
    });

    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);
    const recentErrorsCount = this.errorQueue.filter(error => 
      error.context.timestamp.getTime() > oneHourAgo
    ).length;

    return {
      totalErrors: this.errorQueue.length,
      errorsByCategory,
      errorsByOperation,
      recentErrors: this.errorQueue.slice(-10), // Last 10 errors
      errorRate: recentErrorsCount, // Errors in the last hour
      lastErrorTime: this.errorQueue.length > 0 ? this.errorQueue[this.errorQueue.length - 1].context.timestamp : undefined
    };
  }

  /**
   * Register a callback for error notifications
   */
  public onError(callback: (error: CateringErrorReport) => void): () => void {
    this.errorCallbacks.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.errorCallbacks.indexOf(callback);
      if (index > -1) {
        this.errorCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Clear error queue
   */
  public clearErrors(): void {
    this.errorQueue = [];
  }

  /**
   * Get retry strategy for a specific error
   */
  public getRetryStrategy(errorReport: CateringErrorReport): {
    shouldRetry: boolean;
    maxAttempts: number;
    baseDelay: number;
    backoffFactor: number;
  } {
    if (!errorReport.retryable) {
      return {
        shouldRetry: false,
        maxAttempts: 1,
        baseDelay: 0,
        backoffFactor: 1
      };
    }

    switch (errorReport.category) {
      case 'network':
        return {
          shouldRetry: true,
          maxAttempts: 3,
          baseDelay: 1000,
          backoffFactor: 2
        };
      case 'server':
        return {
          shouldRetry: true,
          maxAttempts: 2,
          baseDelay: 2000,
          backoffFactor: 1.5
        };
      default:
        return {
          shouldRetry: true,
          maxAttempts: 2,
          baseDelay: 1000,
          backoffFactor: 1.5
        };
    }
  }

  /**
   * Generate user-friendly error summary
   */
  public generateErrorSummary(errors: CateringErrorReport[]): string {
    if (errors.length === 0) {
      return 'No errors to report';
    }

    if (errors.length === 1) {
      return errors[0].userMessage;
    }

    const categories = [...new Set(errors.map(e => e.category))];
    const operations = [...new Set(errors.map(e => e.context.operation))];

    let summary = `${errors.length} errors occurred`;
    
    if (categories.length === 1) {
      summary += ` (all ${categories[0]} errors)`;
    } else {
      summary += ` across ${categories.length} categories`;
    }

    if (operations.length === 1) {
      summary += ` during ${operations[0]} operations`;
    }

    return summary;
  }
}

/**
 * Hook for using catering error monitoring in React components
 */
export function useCateringErrorMonitor() {
  const monitor = CateringErrorMonitor.getInstance();

  const captureError = (
    error: Error,
    context: Partial<CateringErrorContext>,
    metadata?: Record<string, any>
  ) => {
    return monitor.captureError(error, context, metadata);
  };

  const getStats = () => monitor.getStats();
  const clearErrors = () => monitor.clearErrors();
  const onError = (callback: (error: CateringErrorReport) => void) => monitor.onError(callback);

  return {
    captureError,
    getStats,
    clearErrors,
    onError,
    monitor
  };
}