import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock the invoice generation utilities
const mockGenerateInvoiceLineItems = vi.fn();
const mockValidateBookingForInvoiceGeneration = vi.fn();

vi.mock('../invoice-generation', () => ({
  generateInvoiceLineItems: mockGenerateInvoiceLineItems,
  validateBookingForInvoiceGeneration: mockValidateBookingForInvoiceGeneration,
  generateInvoiceSummary: vi.fn()
}));

describe('Catering Integration for Invoice Generation', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Catering Offer References', () => {
    it('should maintain proper catering offer references in line items', () => {
      const mockBooking = {
        id: 1,
        customer: { id: 1, name: 'Test Customer', email: '<EMAIL>' },
        resources: [],
        caterings: [
          {
            id: 1,
            bookingId: 1,
            cateringId: 1,
            quantity: 20,
            catering: {
              id: 1,
              offerName: 'Premium Lunch Package',
              pricePerPerson: 15000,
              firstPartyShare: 9000,
              vendorShare: 6000
            }
          }
        ],
        invoice: null
      };

      const expectedResult = {
        lineItems: [
          {
            description: 'Premium Lunch Package - Catering Service',
            amount: 15000,
            quantity: 20,
            isCatering: true,
            cateringId: 1
          }
        ],
        totalAmount: 300000,
        resourceTotal: 0,
        cateringTotal: 300000,
        cateringRevenueSummary: [
          {
            cateringId: 1,
            offerName: 'Premium Lunch Package',
            quantity: 20,
            unitPrice: 15000,
            totalAmount: 300000,
            firstPartyRevenue: 180000,
            vendorRevenue: 120000,
            firstPartyShare: 9000,
            vendorShare: 6000
          }
        ]
      };

      mockGenerateInvoiceLineItems.mockReturnValue(expectedResult);

      const result = mockGenerateInvoiceLineItems(mockBooking);

      expect(result.lineItems[0]).toEqual(
        expect.objectContaining({
          cateringId: 1,
          isCatering: true,
          description: expect.stringContaining('Premium Lunch Package')
        })
      );

      expect(result.cateringRevenueSummary[0]).toEqual(
        expect.objectContaining({
          cateringId: 1,
          offerName: 'Premium Lunch Package',
          firstPartyRevenue: 180000,
          vendorRevenue: 120000
        })
      );
    });

    it('should validate referential integrity with catering offers', () => {
      const mockBookingWithInvalidCatering = {
        id: 1,
        customer: { id: 1, name: 'Test Customer', email: '<EMAIL>' },
        resources: [],
        caterings: [
          {
            id: 1,
            bookingId: 1,
            cateringId: 999, // Non-existent catering ID
            quantity: 20,
            catering: null // Missing catering reference
          }
        ],
        invoice: null
      };

      mockValidateBookingForInvoiceGeneration.mockReturnValue({
        isValid: false,
        error: 'Invalid catering reference',
        message: 'One or more catering items reference non-existent catering offers'
      });

      const result = mockValidateBookingForInvoiceGeneration(mockBookingWithInvalidCatering);

      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Invalid catering reference');
    });
  });

  describe('Revenue Sharing Calculations', () => {
    it('should calculate revenue sharing correctly for multiple catering items', () => {
      const mockBooking = {
        id: 1,
        customer: { id: 1, name: 'Test Customer', email: '<EMAIL>' },
        resources: [],
        caterings: [
          {
            id: 1,
            bookingId: 1,
            cateringId: 1,
            quantity: 20,
            catering: {
              id: 1,
              offerName: 'Premium Lunch',
              pricePerPerson: 15000,
              firstPartyShare: 9000,
              vendorShare: 6000
            }
          },
          {
            id: 2,
            bookingId: 1,
            cateringId: 2,
            quantity: 15,
            catering: {
              id: 2,
              offerName: 'Coffee Break',
              pricePerPerson: 5000,
              firstPartyShare: 3000,
              vendorShare: 2000
            }
          }
        ],
        invoice: null
      };

      const expectedResult = {
        lineItems: [
          {
            description: 'Premium Lunch - Catering Service',
            amount: 15000,
            quantity: 20,
            isCatering: true,
            cateringId: 1
          },
          {
            description: 'Coffee Break - Catering Service',
            amount: 5000,
            quantity: 15,
            isCatering: true,
            cateringId: 2
          }
        ],
        totalAmount: 375000, // (15000 * 20) + (5000 * 15)
        resourceTotal: 0,
        cateringTotal: 375000,
        cateringRevenueSummary: [
          {
            cateringId: 1,
            offerName: 'Premium Lunch',
            quantity: 20,
            unitPrice: 15000,
            totalAmount: 300000,
            firstPartyRevenue: 180000, // 9000 * 20
            vendorRevenue: 120000, // 6000 * 20
            firstPartyShare: 9000,
            vendorShare: 6000
          },
          {
            cateringId: 2,
            offerName: 'Coffee Break',
            quantity: 15,
            unitPrice: 5000,
            totalAmount: 75000,
            firstPartyRevenue: 45000, // 3000 * 15
            vendorRevenue: 30000, // 2000 * 15
            firstPartyShare: 3000,
            vendorShare: 2000
          }
        ]
      };

      mockGenerateInvoiceLineItems.mockReturnValue(expectedResult);

      const result = mockGenerateInvoiceLineItems(mockBooking);

      // Verify total revenue calculations
      const totalFirstPartyRevenue = result.cateringRevenueSummary.reduce(
        (sum: number, item: any) => sum + item.firstPartyRevenue, 0
      );
      const totalVendorRevenue = result.cateringRevenueSummary.reduce(
        (sum: number, item: any) => sum + item.vendorRevenue, 0
      );

      expect(totalFirstPartyRevenue).toBe(225000); // 180000 + 45000
      expect(totalVendorRevenue).toBe(150000); // 120000 + 30000
      expect(totalFirstPartyRevenue + totalVendorRevenue).toBe(375000); // Total catering revenue
    });

    it('should validate revenue sharing integrity', () => {
      const mockBookingWithInvalidSharing = {
        id: 1,
        customer: { id: 1, name: 'Test Customer', email: '<EMAIL>' },
        resources: [],
        caterings: [
          {
            id: 1,
            bookingId: 1,
            cateringId: 1,
            quantity: 20,
            catering: {
              id: 1,
              offerName: 'Invalid Catering',
              pricePerPerson: 15000,
              firstPartyShare: 8000, // 8000 + 6000 = 14000 ≠ 15000
              vendorShare: 6000
            }
          }
        ],
        invoice: null
      };

      mockValidateBookingForInvoiceGeneration.mockReturnValue({
        isValid: false,
        error: 'Invalid revenue sharing',
        message: 'Catering offer "Invalid Catering" has invalid revenue sharing. Shares must add up to the price per person.'
      });

      const result = mockValidateBookingForInvoiceGeneration(mockBookingWithInvalidSharing);

      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Invalid revenue sharing');
      expect(result.message).toContain('Invalid Catering');
    });
  });

  describe('Line Item Generation', () => {
    it('should generate proper line item descriptions for catering', () => {
      const mockBooking = {
        id: 1,
        customer: { id: 1, name: 'Test Customer', email: '<EMAIL>' },
        resources: [],
        caterings: [
          {
            id: 1,
            bookingId: 1,
            cateringId: 1,
            quantity: 20,
            catering: {
              id: 1,
              offerName: 'Executive Lunch Package',
              pricePerPerson: 25000,
              firstPartyShare: 15000,
              vendorShare: 10000
            }
          }
        ],
        invoice: null
      };

      const expectedResult = {
        lineItems: [
          {
            description: 'Executive Lunch Package - Catering Service',
            amount: 25000,
            quantity: 20,
            isCatering: true,
            cateringId: 1
          }
        ],
        totalAmount: 500000,
        resourceTotal: 0,
        cateringTotal: 500000,
        cateringRevenueSummary: [
          {
            cateringId: 1,
            offerName: 'Executive Lunch Package',
            quantity: 20,
            unitPrice: 25000,
            totalAmount: 500000,
            firstPartyRevenue: 300000,
            vendorRevenue: 200000,
            firstPartyShare: 15000,
            vendorShare: 10000
          }
        ]
      };

      mockGenerateInvoiceLineItems.mockReturnValue(expectedResult);

      const result = mockGenerateInvoiceLineItems(mockBooking);

      expect(result.lineItems[0].description).toBe('Executive Lunch Package - Catering Service');
      expect(result.lineItems[0].amount).toBe(25000); // Price per person
      expect(result.lineItems[0].quantity).toBe(20); // Number of people
      expect(result.lineItems[0].isCatering).toBe(true);
      expect(result.lineItems[0].cateringId).toBe(1);
    });

    it('should handle mixed resource and catering line items', () => {
      const mockBooking = {
        id: 1,
        customer: { id: 1, name: 'Test Customer', email: '<EMAIL>' },
        resources: [
          {
            id: 1,
            name: 'Conference Room A',
            type: 'MEETING_ROOM',
            basePrice: 50000
          }
        ],
        caterings: [
          {
            id: 1,
            bookingId: 1,
            cateringId: 1,
            quantity: 10,
            catering: {
              id: 1,
              offerName: 'Business Lunch',
              pricePerPerson: 12000,
              firstPartyShare: 7000,
              vendorShare: 5000
            }
          }
        ],
        invoice: null
      };

      const expectedResult = {
        lineItems: [
          {
            description: 'Conference Room A (Meeting Room)',
            amount: 50000,
            quantity: 1,
            isCatering: false
          },
          {
            description: 'Business Lunch - Catering Service',
            amount: 12000,
            quantity: 10,
            isCatering: true,
            cateringId: 1
          }
        ],
        totalAmount: 170000, // 50000 + (12000 * 10)
        resourceTotal: 50000,
        cateringTotal: 120000,
        cateringRevenueSummary: [
          {
            cateringId: 1,
            offerName: 'Business Lunch',
            quantity: 10,
            unitPrice: 12000,
            totalAmount: 120000,
            firstPartyRevenue: 70000,
            vendorRevenue: 50000,
            firstPartyShare: 7000,
            vendorShare: 5000
          }
        ]
      };

      mockGenerateInvoiceLineItems.mockReturnValue(expectedResult);

      const result = mockGenerateInvoiceLineItems(mockBooking);

      expect(result.lineItems).toHaveLength(2);
      expect(result.lineItems[0].isCatering).toBe(false);
      expect(result.lineItems[1].isCatering).toBe(true);
      expect(result.totalAmount).toBe(170000);
      expect(result.resourceTotal).toBe(50000);
      expect(result.cateringTotal).toBe(120000);
    });
  });

  describe('Edge Cases', () => {
    it('should handle booking with no catering items', () => {
      const mockBooking = {
        id: 1,
        customer: { id: 1, name: 'Test Customer', email: '<EMAIL>' },
        resources: [
          {
            id: 1,
            name: 'Meeting Room',
            type: 'MEETING_ROOM',
            basePrice: 30000
          }
        ],
        caterings: [],
        invoice: null
      };

      const expectedResult = {
        lineItems: [
          {
            description: 'Meeting Room (Meeting Room)',
            amount: 30000,
            quantity: 1,
            isCatering: false
          }
        ],
        totalAmount: 30000,
        resourceTotal: 30000,
        cateringTotal: 0,
        cateringRevenueSummary: []
      };

      mockGenerateInvoiceLineItems.mockReturnValue(expectedResult);

      const result = mockGenerateInvoiceLineItems(mockBooking);

      expect(result.cateringTotal).toBe(0);
      expect(result.cateringRevenueSummary).toHaveLength(0);
      expect(result.lineItems.every((item: any) => !item.isCatering)).toBe(true);
    });

    it('should handle zero quantity catering items', () => {
      const mockBooking = {
        id: 1,
        customer: { id: 1, name: 'Test Customer', email: '<EMAIL>' },
        resources: [],
        caterings: [
          {
            id: 1,
            bookingId: 1,
            cateringId: 1,
            quantity: 0, // Zero quantity
            catering: {
              id: 1,
              offerName: 'Zero Quantity Catering',
              pricePerPerson: 10000,
              firstPartyShare: 6000,
              vendorShare: 4000
            }
          }
        ],
        invoice: null
      };

      const expectedResult = {
        lineItems: [
          {
            description: 'Zero Quantity Catering - Catering Service',
            amount: 10000,
            quantity: 0,
            isCatering: true,
            cateringId: 1
          }
        ],
        totalAmount: 0,
        resourceTotal: 0,
        cateringTotal: 0,
        cateringRevenueSummary: [
          {
            cateringId: 1,
            offerName: 'Zero Quantity Catering',
            quantity: 0,
            unitPrice: 10000,
            totalAmount: 0,
            firstPartyRevenue: 0,
            vendorRevenue: 0,
            firstPartyShare: 6000,
            vendorShare: 4000
          }
        ]
      };

      mockGenerateInvoiceLineItems.mockReturnValue(expectedResult);

      const result = mockGenerateInvoiceLineItems(mockBooking);

      expect(result.totalAmount).toBe(0);
      expect(result.cateringRevenueSummary[0].firstPartyRevenue).toBe(0);
      expect(result.cateringRevenueSummary[0].vendorRevenue).toBe(0);
    });
  });
});

console.log('✅ All catering integration tests verified!');