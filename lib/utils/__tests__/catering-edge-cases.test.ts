import { describe, it, expect } from 'vitest'
import {
  formatCurrency,
  calculateRevenueSplit,
  validateRevenueSharing,
  calculateBookingRevenueSplit,
  validateCateringOfferForBooking,
  calculateMultipleCateringRevenue,
  formatMonetaryPrecision,
  calculatePercentageBreakdown
} from '../catering'

describe('Catering Edge Cases and Stress Tests', () => {
  describe('Extreme Value Handling', () => {
    it('should handle maximum safe integer values', () => {
      const maxSafeInt = Number.MAX_SAFE_INTEGER
      const result = calculateRevenueSplit(1, maxSafeInt, 0)
      
      expect(Number.isFinite(result.totalAmount)).toBe(true)
      expect(Number.isFinite(result.firstPartyAmount)).toBe(true)
      expect(Number.isFinite(result.vendorAmount)).toBe(true)
    })

    it('should handle very small decimal values', () => {
      const result = calculateRevenueSplit(1000000, 0.001, 0.001)
      
      expect(result.firstPartyAmount).toBe(1000) // 0.001 * 1000000 rounded
      expect(result.vendorAmount).toBe(1000)
      expect(result.totalAmount).toBe(2000)
    })

    it('should handle currency formatting with very large numbers', () => {
      expect(formatCurrency(999999999)).toBe('999,999,999 IQD')
      expect(formatCurrency(1000000000)).toBe('1,000,000,000 IQD')
    })

    it('should handle precision issues with repeated calculations', () => {
      // Simulate multiple booking calculations that might accumulate precision errors
      let totalRevenue = 0
      const pricePerPerson = 333.33
      const firstPartyShare = 200
      const vendorShare = 133.33
      
      for (let i = 0; i < 1000; i++) {
        const result = calculateRevenueSplit(1, firstPartyShare, vendorShare)
        totalRevenue += result.totalAmount
      }
      
      expect(Number.isFinite(totalRevenue)).toBe(true)
      expect(totalRevenue).toBeGreaterThan(0)
    })
  })

  describe('Floating Point Precision Edge Cases', () => {
    it('should handle revenue sharing validation with floating point edge cases', () => {
      // Test cases that are known to cause floating point precision issues
      expect(validateRevenueSharing(0.1 + 0.2, 0.7, 1.0)).toBe(true) // 0.1 + 0.2 = 0.30000000000000004
      expect(validateRevenueSharing(1.0 - 0.9, 0.9, 1.0)).toBe(true) // 1.0 - 0.9 = 0.09999999999999998
    })

    it('should handle monetary precision with problematic decimal values', () => {
      expect(formatMonetaryPrecision(0.1 + 0.2)).toBe(0) // Should round 0.30000000000000004 to 0
      expect(formatMonetaryPrecision(1.0 - 0.9)).toBe(0) // Should round 0.09999999999999998 to 0
      expect(formatMonetaryPrecision(0.5)).toBe(1) // Should round 0.5 to 1
    })

    it('should handle percentage calculations with precision issues', () => {
      const result = calculatePercentageBreakdown(1/3, 2/3) // 0.333... and 0.666...
      
      expect(result.firstPartyPercentage).toBe(33)
      expect(result.vendorPercentage).toBe(67)
      expect(result.firstPartyPercentage + result.vendorPercentage).toBe(100)
    })
  })

  describe('Boundary Value Testing', () => {
    it('should handle minimum valid values', () => {
      const result = calculateBookingRevenueSplit({
        pricePerPerson: 1,
        firstPartyShare: 1,
        vendorShare: 0
      }, 1)
      
      expect(result.totalCost).toBe(1)
      expect(result.firstPartyRevenue).toBe(1)
      expect(result.vendorRevenue).toBe(0)
    })

    it('should handle maximum valid values within constraints', () => {
      const result = calculateBookingRevenueSplit({
        pricePerPerson: 999999,
        firstPartyShare: 500000,
        vendorShare: 499999
      }, 1)
      
      expect(result.totalCost).toBe(999999)
      expect(result.firstPartyRevenue).toBe(500000)
      expect(result.vendorRevenue).toBe(499999)
    })

    it('should validate boundary conditions for catering offers', () => {
      // Test minimum valid offer
      const minValidResult = validateCateringOfferForBooking({
        pricePerPerson: 1,
        firstPartyShare: 0,
        vendorShare: 1
      })
      expect(minValidResult.isValid).toBe(true)

      // Test maximum valid offer
      const maxValidResult = validateCateringOfferForBooking({
        pricePerPerson: 999999,
        firstPartyShare: 999999,
        vendorShare: 0
      })
      expect(maxValidResult.isValid).toBe(true)
    })
  })

  describe('Stress Testing with Large Datasets', () => {
    it('should handle large number of catering items efficiently', () => {
      const largeCateringItems = Array.from({ length: 1000 }, (_, index) => ({
        cateringOffer: {
          pricePerPerson: 1000 + index,
          firstPartyShare: 600 + index * 0.1,
          vendorShare: 400 - index * 0.1
        },
        quantity: 10 + (index % 50)
      }))

      const startTime = Date.now()
      const result = calculateMultipleCateringRevenue(largeCateringItems)
      const endTime = Date.now()

      expect(endTime - startTime).toBeLessThan(1000) // Should complete within 1 second
      expect(result.totalCost).toBeGreaterThan(0)
      expect(Number.isFinite(result.totalCost)).toBe(true)
      expect(result.itemBreakdown).toHaveLength(1000)
    })

    it('should handle repeated calculations without memory leaks', () => {
      const cateringOffer = {
        pricePerPerson: 1000,
        firstPartyShare: 600,
        vendorShare: 400
      }

      // Perform many calculations to test for memory issues
      for (let i = 0; i < 10000; i++) {
        const result = calculateBookingRevenueSplit(cateringOffer, i % 100 + 1)
        expect(Number.isFinite(result.totalCost)).toBe(true)
      }
    })
  })

  describe('Error Recovery and Resilience', () => {
    it('should gracefully handle mixed valid and invalid data', () => {
      const mixedCateringItems = [
        {
          cateringOffer: {
            pricePerPerson: 1000,
            firstPartyShare: 600,
            vendorShare: 400
          },
          quantity: 10
        },
        {
          cateringOffer: {
            pricePerPerson: NaN,
            firstPartyShare: 600,
            vendorShare: 400
          },
          quantity: 5
        },
        {
          cateringOffer: {
            pricePerPerson: 500,
            firstPartyShare: 300,
            vendorShare: 200
          },
          quantity: 8
        }
      ]

      const result = calculateMultipleCateringRevenue(mixedCateringItems)
      
      // Should only calculate for valid items
      expect(result.totalCost).toBe(14000) // (1000*10) + (500*8)
      expect(result.totalFirstPartyRevenue).toBe(8400) // (600*10) + (300*8)
      expect(result.totalVendorRevenue).toBe(5600) // (400*10) + (200*8)
    })

    it('should handle validation with corrupted data structures', () => {
      const corruptedOffer = {
        pricePerPerson: undefined as any,
        firstPartyShare: null as any,
        vendorShare: 'invalid' as any
      }

      const result = validateCateringOfferForBooking(corruptedOffer)
      
      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })
  })

  describe('Real-world Scenario Testing', () => {
    it('should handle typical wedding catering scenario', () => {
      const weddingCatering = [
        {
          cateringOffer: {
            pricePerPerson: 50000, // 50,000 IQD per person
            firstPartyShare: 30000, // 60%
            vendorShare: 20000 // 40%
          },
          quantity: 200 // 200 guests
        },
        {
          cateringOffer: {
            pricePerPerson: 15000, // Drinks package
            firstPartyShare: 9000, // 60%
            vendorShare: 6000 // 40%
          },
          quantity: 200
        }
      ]

      const result = calculateMultipleCateringRevenue(weddingCatering)
      
      expect(result.totalCost).toBe(13000000) // (50000*200) + (15000*200)
      expect(result.totalFirstPartyRevenue).toBe(7800000) // (30000*200) + (9000*200)
      expect(result.totalVendorRevenue).toBe(5200000) // (20000*200) + (6000*200)
      expect(result.overallPercentages.firstPartyPercentage).toBe(60)
      expect(result.overallPercentages.vendorPercentage).toBe(40)
    })

    it('should handle corporate meeting catering scenario', () => {
      const corporateCatering = [
        {
          cateringOffer: {
            pricePerPerson: 25000, // Business lunch
            firstPartyShare: 15000, // 60%
            vendorShare: 10000 // 40%
          },
          quantity: 50
        },
        {
          cateringOffer: {
            pricePerPerson: 5000, // Coffee break
            firstPartyShare: 2000, // 40%
            vendorShare: 3000 // 60%
          },
          quantity: 50
        }
      ]

      const result = calculateMultipleCateringRevenue(corporateCatering)
      
      expect(result.totalCost).toBe(1500000) // (25000*50) + (5000*50)
      expect(result.totalFirstPartyRevenue).toBe(850000) // (15000*50) + (2000*50)
      expect(result.totalVendorRevenue).toBe(650000) // (10000*50) + (3000*50)
    })

    it('should handle budget-conscious event scenario', () => {
      const budgetCatering = {
        cateringOffer: {
          pricePerPerson: 8000, // 8,000 IQD per person
          firstPartyShare: 3000, // 37.5%
          vendorShare: 5000 // 62.5%
        },
        quantity: 100
      }

      const result = calculateBookingRevenueSplit(budgetCatering.cateringOffer, budgetCatering.quantity)
      
      expect(result.totalCost).toBe(800000)
      expect(result.firstPartyRevenue).toBe(300000)
      expect(result.vendorRevenue).toBe(500000)
      expect(result.percentages.firstPartyPercentage).toBe(38) // Rounded from 37.5%
      expect(result.percentages.vendorPercentage).toBe(63) // Rounded from 62.5%
    })
  })

  describe('Currency and Formatting Edge Cases', () => {
    it('should handle currency formatting with edge cases', () => {
      expect(formatCurrency(0.4)).toBe('0 IQD') // Should round down
      expect(formatCurrency(0.5)).toBe('1 IQD') // Should round up
      expect(formatCurrency(0.6)).toBe('1 IQD') // Should round up
      expect(formatCurrency(-0.4)).toBe('-0 IQD') // Should round towards zero (JavaScript -0)
      expect(formatCurrency(-0.6)).toBe('-1 IQD') // Should round away from zero
    })

    it('should handle very large numbers in currency formatting', () => {
      const largeNumber = 999999999999
      const formatted = formatCurrency(largeNumber)
      
      expect(formatted).toContain('IQD')
      expect(formatted).toContain(',')
      expect(Number.isFinite(largeNumber)).toBe(true)
    })
  })

  describe('Performance and Memory Tests', () => {
    it('should maintain consistent performance with varying input sizes', () => {
      const sizes = [10, 100, 1000]
      const performanceResults: number[] = []

      sizes.forEach(size => {
        const items = Array.from({ length: size }, (_, i) => ({
          cateringOffer: {
            pricePerPerson: 1000 + i,
            firstPartyShare: 600,
            vendorShare: 400 + i
          },
          quantity: 10
        }))

        const startTime = performance.now()
        calculateMultipleCateringRevenue(items)
        const endTime = performance.now()
        
        performanceResults.push(endTime - startTime)
      })

      // Performance should scale reasonably (not exponentially)
      // Allow for some variance in timing measurements
      expect(performanceResults[2]).toBeLessThan(1000) // Should complete within 1 second
    })

    it('should not accumulate precision errors over many operations', () => {
      let runningTotal = 0
      const baseAmount = 1000.01

      for (let i = 0; i < 1000; i++) {
        const result = calculateRevenueSplit(1, baseAmount, baseAmount)
        runningTotal += result.totalAmount
      }

      // Should maintain reasonable precision
      expect(Math.abs(runningTotal - (2000 * 1000))).toBeLessThan(1000) // Within 1000 IQD tolerance
    })
  })
})