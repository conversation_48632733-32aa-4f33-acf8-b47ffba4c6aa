import { describe, it, expect, vi, beforeEach } from 'vitest'
import {
  formatCurrency,
  formatPercentage,
  parseCurrency,
  calculateRevenueSplit,
  calculateCateringCost,
  calculateCateringRevenueSplit,
  validateRevenueSharing,
  formatMonetaryPrecision,
  validateMonetaryAmount,
  validateShareAmount,
  calculatePercentageBreakdown,
  validateCateringFormData,
  calculateBookingRevenueSplit,
  validateCateringOfferForBooking,
  calculateMultipleCateringRevenue,
  CURRENCY_CONFIG
} from '../catering'

describe('Currency Formatting', () => {
  describe('formatCurrency', () => {
    it('should format positive numbers correctly', () => {
      expect(formatCurrency(1000)).toBe('1,000 IQD')
      expect(formatCurrency(1234567)).toBe('1,234,567 IQD')
      expect(formatCurrency(100)).toBe('100 IQD')
      expect(formatCurrency(1)).toBe('1 IQD')
    })

    it('should handle decimal numbers by rounding', () => {
      expect(formatCurrency(1000.4)).toBe('1,000 IQD')
      expect(formatCurrency(1000.5)).toBe('1,001 IQD')
      expect(formatCurrency(1000.9)).toBe('1,001 IQD')
    })

    it('should handle zero', () => {
      expect(formatCurrency(0)).toBe('0 IQD')
    })

    it('should handle negative numbers', () => {
      expect(formatCurrency(-1000)).toBe('-1,000 IQD')
      expect(formatCurrency(-1234567)).toBe('-1,234,567 IQD')
    })

    it('should handle invalid inputs', () => {
      expect(formatCurrency(NaN)).toBe('0 IQD')
      expect(formatCurrency(Infinity)).toBe('0 IQD')
      expect(formatCurrency(-Infinity)).toBe('0 IQD')
    })
  })

  describe('formatPercentage', () => {
    it('should format percentages correctly', () => {
      expect(formatPercentage(50)).toBe('50%')
      expect(formatPercentage(100)).toBe('100%')
      expect(formatPercentage(0)).toBe('0%')
      expect(formatPercentage(75.6)).toBe('76%')
      expect(formatPercentage(25.4)).toBe('25%')
    })

    it('should handle invalid inputs', () => {
      expect(formatPercentage(NaN)).toBe('0%')
      expect(formatPercentage(Infinity)).toBe('0%')
      expect(formatPercentage(-Infinity)).toBe('0%')
    })
  })

  describe('parseCurrency', () => {
    it('should parse currency strings correctly', () => {
      expect(parseCurrency('1,000 IQD')).toBe(1000)
      expect(parseCurrency('1,234,567 IQD')).toBe(1234567)
      expect(parseCurrency('100 IQD')).toBe(100)
      expect(parseCurrency('1 IQD')).toBe(1)
    })

    it('should handle strings without formatting', () => {
      expect(parseCurrency('1000')).toBe(1000)
      expect(parseCurrency('500')).toBe(500)
    })

    it('should handle invalid inputs', () => {
      expect(parseCurrency('')).toBe(0)
      expect(parseCurrency('invalid')).toBe(0)
      expect(parseCurrency('abc IQD')).toBe(0)
      expect(parseCurrency(null as any)).toBe(0)
      expect(parseCurrency(undefined as any)).toBe(0)
    })

    it('should handle mixed formats', () => {
      expect(parseCurrency('1,000')).toBe(1000)
      expect(parseCurrency('1000 IQD')).toBe(1000)
      expect(parseCurrency('1,000IQD')).toBe(1000)
    })
  })
})

describe('Revenue Split Calculations', () => {
  describe('calculateRevenueSplit', () => {
    it('should calculate revenue split correctly for valid inputs', () => {
      const result = calculateRevenueSplit(10, 500, 300)
      expect(result).toEqual({
        totalAmount: 8000,
        firstPartyAmount: 5000,
        vendorAmount: 3000,
        firstPartySharePerPerson: 500,
        vendorSharePerPerson: 300
      })
    })

    it('should handle zero quantity', () => {
      const result = calculateRevenueSplit(0, 500, 300)
      expect(result).toEqual({
        totalAmount: 0,
        firstPartyAmount: 0,
        vendorAmount: 0,
        firstPartySharePerPerson: 500,
        vendorSharePerPerson: 300
      })
    })

    it('should handle zero share amounts', () => {
      const result = calculateRevenueSplit(5, 0, 0)
      expect(result).toEqual({
        totalAmount: 0,
        firstPartyAmount: 0,
        vendorAmount: 0,
        firstPartySharePerPerson: 0,
        vendorSharePerPerson: 0
      })
    })

    it('should handle invalid inputs by defaulting to zero', () => {
      const result = calculateRevenueSplit(NaN, Infinity, -Infinity)
      expect(result).toEqual({
        totalAmount: 0,
        firstPartyAmount: 0,
        vendorAmount: 0,
        firstPartySharePerPerson: 0,
        vendorSharePerPerson: 0
      })
    })

    it('should handle negative inputs by defaulting to zero', () => {
      const result = calculateRevenueSplit(-5, -100, -200)
      expect(result).toEqual({
        totalAmount: 0,
        firstPartyAmount: 0,
        vendorAmount: 0,
        firstPartySharePerPerson: 0,
        vendorSharePerPerson: 0
      })
    })

    it('should round results to whole numbers', () => {
      const result = calculateRevenueSplit(3, 333.33, 166.67)
      expect(result.firstPartyAmount).toBe(1000) // 333.33 * 3 = 999.99, rounded to 1000
      expect(result.vendorAmount).toBe(500) // 166.67 * 3 = 500.01, rounded to 500
      expect(result.totalAmount).toBe(1500)
    })
  })

  describe('calculateCateringCost', () => {
    it('should calculate total cost correctly', () => {
      expect(calculateCateringCost(500, 10)).toBe(5000)
      expect(calculateCateringCost(1000, 5)).toBe(5000)
      expect(calculateCateringCost(250, 20)).toBe(5000)
    })

    it('should handle zero values', () => {
      expect(calculateCateringCost(0, 10)).toBe(0)
      expect(calculateCateringCost(500, 0)).toBe(0)
      expect(calculateCateringCost(0, 0)).toBe(0)
    })

    it('should handle invalid inputs', () => {
      expect(calculateCateringCost(NaN, 10)).toBe(0)
      expect(calculateCateringCost(500, NaN)).toBe(0)
      expect(calculateCateringCost(Infinity, 10)).toBe(0)
      expect(calculateCateringCost(500, -5)).toBe(0)
    })

    it('should round results to whole numbers', () => {
      expect(calculateCateringCost(333.33, 3)).toBe(1000) // 999.99 rounded to 1000
      expect(calculateCateringCost(166.67, 3)).toBe(500) // 500.01 rounded to 500
    })
  })

  describe('calculateCateringRevenueSplit', () => {
    it('should be an alias for calculateRevenueSplit', () => {
      const result1 = calculateCateringRevenueSplit(10, 500, 300)
      const result2 = calculateRevenueSplit(10, 500, 300)
      expect(result1).toEqual(result2)
    })
  })
})

describe('Revenue Sharing Validation', () => {
  describe('validateRevenueSharing', () => {
    it('should validate correct revenue sharing', () => {
      expect(validateRevenueSharing(500, 300, 800)).toBe(true)
      expect(validateRevenueSharing(600, 400, 1000)).toBe(true)
      expect(validateRevenueSharing(0, 1000, 1000)).toBe(true)
      expect(validateRevenueSharing(1000, 0, 1000)).toBe(true)
    })

    it('should reject incorrect revenue sharing', () => {
      expect(validateRevenueSharing(500, 300, 900)).toBe(false) // 800 != 900
      expect(validateRevenueSharing(600, 400, 900)).toBe(false) // 1000 != 900
      expect(validateRevenueSharing(500, 300, 700)).toBe(false) // 800 != 700
    })

    it('should handle floating point precision', () => {
      expect(validateRevenueSharing(333.33, 166.67, 500)).toBe(true) // Within 0.01 tolerance
      expect(validateRevenueSharing(333.35, 166.67, 500)).toBe(false) // Outside tolerance (333.35 + 166.67 = 500.02)
    })

    it('should handle invalid inputs', () => {
      expect(validateRevenueSharing(NaN, 300, 800)).toBe(false)
      expect(validateRevenueSharing(500, NaN, 800)).toBe(false)
      expect(validateRevenueSharing(500, 300, NaN)).toBe(false)
      expect(validateRevenueSharing(Infinity, 300, 800)).toBe(false)
    })
  })
})

describe('Monetary Precision and Validation', () => {
  describe('formatMonetaryPrecision', () => {
    it('should format to whole numbers', () => {
      expect(formatMonetaryPrecision(1000.5)).toBe(1001)
      expect(formatMonetaryPrecision(1000.4)).toBe(1000)
      expect(formatMonetaryPrecision(1000)).toBe(1000)
      expect(formatMonetaryPrecision(0)).toBe(0)
    })

    it('should handle invalid inputs', () => {
      expect(formatMonetaryPrecision(NaN)).toBe(0)
      expect(formatMonetaryPrecision(Infinity)).toBe(0)
      expect(formatMonetaryPrecision(-Infinity)).toBe(0)
    })
  })

  describe('validateMonetaryAmount', () => {
    it('should validate correct monetary amounts', () => {
      expect(validateMonetaryAmount(1)).toBe(true)
      expect(validateMonetaryAmount(1000)).toBe(true)
      expect(validateMonetaryAmount(999999)).toBe(true)
      expect(validateMonetaryAmount(500.5)).toBe(true)
    })

    it('should reject invalid monetary amounts', () => {
      expect(validateMonetaryAmount(0)).toBe(false)
      expect(validateMonetaryAmount(-1)).toBe(false)
      expect(validateMonetaryAmount(1000000)).toBe(false) // Above max
      expect(validateMonetaryAmount(NaN)).toBe(false)
      expect(validateMonetaryAmount(Infinity)).toBe(false)
    })
  })

  describe('validateShareAmount', () => {
    it('should validate correct share amounts', () => {
      expect(validateShareAmount(0)).toBe(true)
      expect(validateShareAmount(1)).toBe(true)
      expect(validateShareAmount(1000)).toBe(true)
      expect(validateShareAmount(999999)).toBe(true)
      expect(validateShareAmount(500.5)).toBe(true)
    })

    it('should reject invalid share amounts', () => {
      expect(validateShareAmount(-1)).toBe(false)
      expect(validateShareAmount(1000000)).toBe(false) // Above max
      expect(validateShareAmount(NaN)).toBe(false)
      expect(validateShareAmount(Infinity)).toBe(false)
    })
  })
})

describe('Percentage Calculations', () => {
  describe('calculatePercentageBreakdown', () => {
    beforeEach(() => {
      vi.clearAllMocks()
      // Mock console methods to avoid noise in tests
      vi.spyOn(console, 'warn').mockImplementation(() => {})
      vi.spyOn(console, 'error').mockImplementation(() => {})
    })

    it('should calculate percentages correctly', () => {
      const result = calculatePercentageBreakdown(600, 400)
      expect(result).toEqual({
        firstPartyPercentage: 60,
        vendorPercentage: 40
      })
    })

    it('should handle equal splits', () => {
      const result = calculatePercentageBreakdown(500, 500)
      expect(result).toEqual({
        firstPartyPercentage: 50,
        vendorPercentage: 50
      })
    })

    it('should handle zero total', () => {
      const result = calculatePercentageBreakdown(0, 0)
      expect(result).toEqual({
        firstPartyPercentage: 0,
        vendorPercentage: 0
      })
    })

    it('should handle one-sided splits', () => {
      const result1 = calculatePercentageBreakdown(1000, 0)
      expect(result1).toEqual({
        firstPartyPercentage: 100,
        vendorPercentage: 0
      })

      const result2 = calculatePercentageBreakdown(0, 1000)
      expect(result2).toEqual({
        firstPartyPercentage: 0,
        vendorPercentage: 100
      })
    })

    it('should handle invalid inputs with error logging', () => {
      const result = calculatePercentageBreakdown(NaN, 400)
      expect(result).toEqual({
        firstPartyPercentage: 0,
        vendorPercentage: 0
      })
      expect(console.warn).toHaveBeenCalledWith(
        'Invalid share amounts provided to calculatePercentageBreakdown:',
        { firstPartyShare: NaN, vendorShare: 400 }
      )
    })

    it('should handle negative inputs with error logging', () => {
      const result = calculatePercentageBreakdown(-100, 400)
      expect(result).toEqual({
        firstPartyPercentage: 0,
        vendorPercentage: 0
      })
      expect(console.warn).toHaveBeenCalledWith(
        'Negative share amounts provided to calculatePercentageBreakdown:',
        { firstPartyShare: -100, vendorShare: 400 }
      )
    })

    it('should round percentages correctly', () => {
      const result = calculatePercentageBreakdown(333, 667) // Should be 33.3% and 66.7%
      expect(result.firstPartyPercentage).toBe(33)
      expect(result.vendorPercentage).toBe(67)
    })
  })
})

describe('Form Data Validation', () => {
  describe('validateCateringFormData', () => {
    const validData = {
      offerName: 'Valid Catering Offer',
      pricePerPerson: 1000,
      firstPartyShare: 600,
      vendorShare: 400
    }

    it('should validate correct form data', () => {
      const result = validateCateringFormData(validData)
      expect(result.isValid).toBe(true)
      expect(result.errors).toEqual({})
      expect(result.summary).toBe('')
    })

    describe('Offer Name Validation', () => {
      it('should reject empty offer name', () => {
        const result = validateCateringFormData({
          ...validData,
          offerName: ''
        })
        expect(result.isValid).toBe(false)
        expect(result.errors.offerName).toContain('Offer name is required')
      })

      it('should reject whitespace-only offer name', () => {
        const result = validateCateringFormData({
          ...validData,
          offerName: '   '
        })
        expect(result.isValid).toBe(false)
        expect(result.errors.offerName).toContain('Offer name cannot be empty')
      })

      it('should reject offer name with "test"', () => {
        const result = validateCateringFormData({
          ...validData,
          offerName: 'Test Catering'
        })
        expect(result.isValid).toBe(false)
        expect(result.errors.offerName).toContain('Name cannot contain "test"')
      })

      it('should reject offer name that is only numbers', () => {
        const result = validateCateringFormData({
          ...validData,
          offerName: '12345'
        })
        expect(result.isValid).toBe(false)
        expect(result.errors.offerName).toContain('Name cannot be only numbers')
      })

      it('should reject offer name that is too long', () => {
        const result = validateCateringFormData({
          ...validData,
          offerName: 'a'.repeat(101)
        })
        expect(result.isValid).toBe(false)
        expect(result.errors.offerName).toContain('Offer name must be less than 100 characters')
      })

      it('should reject offer name with invalid characters', () => {
        const result = validateCateringFormData({
          ...validData,
          offerName: 'Invalid@Name!'
        })
        expect(result.isValid).toBe(false)
        expect(result.errors.offerName).toContain('Name can only contain letters, numbers, spaces, and basic punctuation')
      })
    })

    describe('Price Per Person Validation', () => {
      it('should reject invalid price per person', () => {
        const result = validateCateringFormData({
          ...validData,
          pricePerPerson: NaN
        })
        expect(result.isValid).toBe(false)
        expect(result.errors.pricePerPerson).toContain('Price per person must be a valid number')
      })

      it('should reject price per person below minimum', () => {
        const result = validateCateringFormData({
          ...validData,
          pricePerPerson: 0
        })
        expect(result.isValid).toBe(false)
        expect(result.errors.pricePerPerson).toContain('Price per person must be at least 1 IQD')
      })

      it('should reject price per person above maximum', () => {
        const result = validateCateringFormData({
          ...validData,
          pricePerPerson: 10000000
        })
        expect(result.isValid).toBe(false)
        expect(result.errors.pricePerPerson).toContain('Price per person cannot exceed 999,999 IQD')
      })
    })

    describe('Share Amount Validation', () => {
      it('should reject invalid first party share', () => {
        const result = validateCateringFormData({
          ...validData,
          firstPartyShare: NaN
        })
        expect(result.isValid).toBe(false)
        expect(result.errors.firstPartyShare).toContain('First party share must be a valid number')
      })

      it('should reject negative first party share', () => {
        const result = validateCateringFormData({
          ...validData,
          firstPartyShare: -100
        })
        expect(result.isValid).toBe(false)
        expect(result.errors.firstPartyShare).toContain('First party share must be at least 0 IQD')
      })

      it('should reject invalid vendor share', () => {
        const result = validateCateringFormData({
          ...validData,
          vendorShare: NaN
        })
        expect(result.isValid).toBe(false)
        expect(result.errors.vendorShare).toContain('Vendor share must be a valid number')
      })

      it('should reject negative vendor share', () => {
        const result = validateCateringFormData({
          ...validData,
          vendorShare: -100
        })
        expect(result.isValid).toBe(false)
        expect(result.errors.vendorShare).toContain('Vendor share must be at least 0 IQD')
      })
    })

    describe('Revenue Sharing Total Validation', () => {
      it('should reject when shares do not add up to price per person', () => {
        const result = validateCateringFormData({
          ...validData,
          pricePerPerson: 1000,
          firstPartyShare: 600,
          vendorShare: 300 // Total: 900, should be 1000
        })
        expect(result.isValid).toBe(false)
        expect(result.errors.vendorShare).toContain('First party share and vendor share must add up to exactly the price per person')
      })

      it('should accept shares that add up correctly', () => {
        const result = validateCateringFormData({
          ...validData,
          pricePerPerson: 1000,
          firstPartyShare: 700,
          vendorShare: 300 // Total: 1000
        })
        expect(result.isValid).toBe(true)
      })
    })

    describe('Summary Generation', () => {
      it('should generate single error summary', () => {
        const result = validateCateringFormData({
          ...validData,
          offerName: ''
        })
        expect(result.summary).toBe('Offer name is required')
      })

      it('should generate multiple error summary', () => {
        const result = validateCateringFormData({
          ...validData,
          offerName: '',
          pricePerPerson: 0
        })
        expect(result.summary).toBe('3 validation errors need to be fixed')
      })
    })
  })
})

describe('Currency Configuration', () => {
  it('should have correct currency configuration', () => {
    expect(CURRENCY_CONFIG).toEqual({
      symbol: "IQD",
      decimalPlaces: 0,
      thousandsSeparator: ",",
      decimalSeparator: ".",
      locale: "en-IQ",
      currency: "IQD",
    })
  })
})

describe('Integration with Booking System', () => {
  describe('Revenue Split for Booking Calculations', () => {
    it('should calculate revenue split for booking integration', () => {
      // Simulate a booking with 15 people for a catering offer
      const quantity = 15
      const firstPartySharePerPerson = 400 // IQD per person
      const vendorSharePerPerson = 600 // IQD per person
      
      const result = calculateRevenueSplit(quantity, firstPartySharePerPerson, vendorSharePerPerson)
      
      expect(result).toEqual({
        totalAmount: 15000, // 15 * (400 + 600)
        firstPartyAmount: 6000, // 15 * 400
        vendorAmount: 9000, // 15 * 600
        firstPartySharePerPerson: 400,
        vendorSharePerPerson: 600
      })
    })

    it('should maintain precision for financial calculations', () => {
      // Test with values that might cause floating point issues
      const quantity = 7
      const firstPartySharePerPerson = 571 // 571 * 7 = 3997
      const vendorSharePerPerson = 429 // 429 * 7 = 3003
      
      const result = calculateRevenueSplit(quantity, firstPartySharePerPerson, vendorSharePerPerson)
      
      expect(result.firstPartyAmount).toBe(3997)
      expect(result.vendorAmount).toBe(3003)
      expect(result.totalAmount).toBe(7000)
    })

    it('should handle large booking quantities', () => {
      const quantity = 1000
      const firstPartySharePerPerson = 500
      const vendorSharePerPerson = 500
      
      const result = calculateRevenueSplit(quantity, firstPartySharePerPerson, vendorSharePerPerson)
      
      expect(result.totalAmount).toBe(1000000)
      expect(result.firstPartyAmount).toBe(500000)
      expect(result.vendorAmount).toBe(500000)
    })
  })

  describe('Validation for Booking System Integration', () => {
    it('should validate revenue sharing for booking system', () => {
      // Test typical catering offer configuration
      const pricePerPerson = 1000
      const firstPartyShare = 400
      const vendorShare = 600
      
      expect(validateRevenueSharing(firstPartyShare, vendorShare, pricePerPerson)).toBe(true)
    })

    it('should reject invalid configurations for booking system', () => {
      const pricePerPerson = 1000
      const firstPartyShare = 500
      const vendorShare = 400 // Total: 900, not 1000
      
      expect(validateRevenueSharing(firstPartyShare, vendorShare, pricePerPerson)).toBe(false)
    })
  })

  describe('calculateBookingRevenueSplit', () => {
    const cateringOffer = {
      pricePerPerson: 1000,
      firstPartyShare: 600,
      vendorShare: 400
    }

    it('should calculate detailed booking revenue split', () => {
      const result = calculateBookingRevenueSplit(cateringOffer, 10)
      
      expect(result).toEqual({
        totalCost: 10000,
        firstPartyRevenue: 6000,
        vendorRevenue: 4000,
        breakdown: {
          pricePerPerson: 1000,
          firstPartySharePerPerson: 600,
          vendorSharePerPerson: 400,
          quantity: 10
        },
        percentages: {
          firstPartyPercentage: 60,
          vendorPercentage: 40
        }
      })
    })

    it('should handle zero quantity', () => {
      const result = calculateBookingRevenueSplit(cateringOffer, 0)
      
      expect(result.totalCost).toBe(0)
      expect(result.firstPartyRevenue).toBe(0)
      expect(result.vendorRevenue).toBe(0)
    })

    it('should handle invalid catering offer', () => {
      const invalidOffer = {
        pricePerPerson: NaN,
        firstPartyShare: 600,
        vendorShare: 400
      }
      
      const result = calculateBookingRevenueSplit(invalidOffer, 10)
      
      expect(result.totalCost).toBe(0)
      expect(result.firstPartyRevenue).toBe(0)
      expect(result.vendorRevenue).toBe(0)
    })
  })

  describe('validateCateringOfferForBooking', () => {
    const validOffer = {
      pricePerPerson: 1000,
      firstPartyShare: 600,
      vendorShare: 400
    }

    it('should validate correct catering offer', () => {
      const result = validateCateringOfferForBooking(validOffer)
      
      expect(result.isValid).toBe(true)
      expect(result.errors).toEqual([])
      expect(result.warnings).toEqual([])
    })

    it('should detect invalid price per person', () => {
      const result = validateCateringOfferForBooking({
        ...validOffer,
        pricePerPerson: 0
      })
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Price per person must be between 1 and 999,999 IQD')
    })

    it('should detect revenue sharing mismatch', () => {
      const result = validateCateringOfferForBooking({
        pricePerPerson: 1000,
        firstPartyShare: 600,
        vendorShare: 300 // Total: 900, not 1000
      })
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('First party share and vendor share must add up to exactly the price per person')
    })

    it('should detect zero-zero split', () => {
      const result = validateCateringOfferForBooking({
        pricePerPerson: 0,
        firstPartyShare: 0,
        vendorShare: 0
      })
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('At least one party must receive revenue (cannot have 0-0 split)')
    })

    it('should warn about one-sided revenue splits', () => {
      const result1 = validateCateringOfferForBooking({
        pricePerPerson: 1000,
        firstPartyShare: 1000,
        vendorShare: 0
      })
      
      expect(result1.isValid).toBe(true)
      expect(result1.warnings).toContain('Vendor receives no revenue from this catering offer')

      const result2 = validateCateringOfferForBooking({
        pricePerPerson: 1000,
        firstPartyShare: 0,
        vendorShare: 1000
      })
      
      expect(result2.isValid).toBe(true)
      expect(result2.warnings).toContain('First party receives no revenue from this catering offer')
    })

    it('should warn about extreme revenue splits', () => {
      const result = validateCateringOfferForBooking({
        pricePerPerson: 1000,
        firstPartyShare: 950,
        vendorShare: 50
      })
      
      expect(result.isValid).toBe(true)
      expect(result.warnings).toContain('First party receives more than 90% of revenue')
    })
  })

  describe('calculateMultipleCateringRevenue', () => {
    const cateringItems = [
      {
        cateringOffer: {
          pricePerPerson: 1000,
          firstPartyShare: 600,
          vendorShare: 400
        },
        quantity: 10
      },
      {
        cateringOffer: {
          pricePerPerson: 500,
          firstPartyShare: 200,
          vendorShare: 300
        },
        quantity: 5
      }
    ]

    it('should calculate total revenue for multiple catering items', () => {
      const result = calculateMultipleCateringRevenue(cateringItems)
      
      expect(result.totalCost).toBe(12500) // (1000*10) + (500*5)
      expect(result.totalFirstPartyRevenue).toBe(7000) // (600*10) + (200*5)
      expect(result.totalVendorRevenue).toBe(5500) // (400*10) + (300*5)
      expect(result.itemBreakdown).toHaveLength(2)
      expect(result.itemBreakdown[0]).toEqual({
        itemCost: 10000,
        firstPartyRevenue: 6000,
        vendorRevenue: 4000,
        quantity: 10
      })
    })

    it('should calculate overall percentages correctly', () => {
      const result = calculateMultipleCateringRevenue(cateringItems)
      
      // Total first party: 7000, Total vendor: 5500, Total: 12500
      // First party: 7000/12500 = 56%, Vendor: 5500/12500 = 44%
      expect(result.overallPercentages.firstPartyPercentage).toBe(56)
      expect(result.overallPercentages.vendorPercentage).toBe(44)
    })

    it('should handle empty catering items array', () => {
      const result = calculateMultipleCateringRevenue([])
      
      expect(result.totalCost).toBe(0)
      expect(result.totalFirstPartyRevenue).toBe(0)
      expect(result.totalVendorRevenue).toBe(0)
      expect(result.itemBreakdown).toEqual([])
      expect(result.overallPercentages).toEqual({
        firstPartyPercentage: 0,
        vendorPercentage: 0
      })
    })

    it('should handle invalid catering items', () => {
      const invalidItems = [
        {
          cateringOffer: {
            pricePerPerson: NaN,
            firstPartyShare: 600,
            vendorShare: 400
          },
          quantity: 10
        }
      ]
      
      const result = calculateMultipleCateringRevenue(invalidItems)
      
      expect(result.totalCost).toBe(0)
      expect(result.totalFirstPartyRevenue).toBe(0)
      expect(result.totalVendorRevenue).toBe(0)
    })
  })
})