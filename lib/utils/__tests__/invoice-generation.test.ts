import { describe, it, expect } from 'vitest';
import {
  generateInvoiceLineItems,
  validateBookingForInvoiceGeneration,
  calculateTotalRevenueBreakdown,
  formatInvoiceAmount,
  generateInvoiceSummary,
  type BookingForInvoice
} from '../invoice-generation';

describe('Invoice Generation Utilities', () => {
  const mockBooking: BookingForInvoice = {
    id: 1,
    customer: {
      id: 1,
      name: 'Test Customer',
      email: '<EMAIL>',
      companyName: 'Test Company'
    },
    resources: [
      {
        id: 1,
        name: 'Conference Room A',
        type: 'MEETING_ROOM',
        basePrice: 50000
      },
      {
        id: 2,
        name: 'Projector Equipment',
        type: 'INDOOR_EVENT_HALL',
        basePrice: 25000
      }
    ],
    caterings: [
      {
        id: 1,
        bookingId: 1,
        cateringId: 1,
        quantity: 20,
        catering: {
          id: 1,
          offerName: 'Premium Lunch Package',
          pricePerPerson: 15000,
          firstPartyShare: 9000,
          vendorShare: 6000
        }
      },
      {
        id: 2,
        bookingId: 1,
        cateringId: 2,
        quantity: 15,
        catering: {
          id: 2,
          offerName: 'Coffee Break Service',
          pricePerPerson: 5000,
          firstPartyShare: 3000,
          vendorShare: 2000
        }
      }
    ],
    invoice: null
  };

  describe('generateInvoiceLineItems', () => {
    it('should generate correct line items for resources and catering', () => {
      const result = generateInvoiceLineItems(mockBooking);

      expect(result.lineItems).toHaveLength(4);
      
      // Check resource line items
      expect(result.lineItems[0]).toEqual({
        description: 'Conference Room A (Meeting Room)',
        amount: 50000,
        quantity: 1,
        isCatering: false
      });

      expect(result.lineItems[1]).toEqual({
        description: 'Projector Equipment (Indoor Event Hall)',
        amount: 25000,
        quantity: 1,
        isCatering: false
      });

      // Check catering line items
      expect(result.lineItems[2]).toEqual({
        description: 'Premium Lunch Package - Catering Service',
        amount: 15000,
        quantity: 20,
        isCatering: true,
        cateringId: 1
      });

      expect(result.lineItems[3]).toEqual({
        description: 'Coffee Break Service - Catering Service',
        amount: 5000,
        quantity: 15,
        isCatering: true,
        cateringId: 2
      });
    });

    it('should calculate correct totals', () => {
      const result = generateInvoiceLineItems(mockBooking);

      expect(result.resourceTotal).toBe(75000); // 50000 + 25000
      expect(result.cateringTotal).toBe(375000); // (15000 * 20) + (5000 * 15)
      expect(result.totalAmount).toBe(450000); // 75000 + 375000
    });

    it('should generate correct catering revenue summary', () => {
      const result = generateInvoiceLineItems(mockBooking);

      expect(result.cateringRevenueSummary).toHaveLength(2);

      // Premium Lunch Package
      expect(result.cateringRevenueSummary[0]).toEqual({
        cateringId: 1,
        offerName: 'Premium Lunch Package',
        quantity: 20,
        unitPrice: 15000,
        totalAmount: 300000,
        firstPartyRevenue: 180000, // 9000 * 20
        vendorRevenue: 120000, // 6000 * 20
        firstPartyShare: 9000,
        vendorShare: 6000
      });

      // Coffee Break Service
      expect(result.cateringRevenueSummary[1]).toEqual({
        cateringId: 2,
        offerName: 'Coffee Break Service',
        quantity: 15,
        unitPrice: 5000,
        totalAmount: 75000,
        firstPartyRevenue: 45000, // 3000 * 15
        vendorRevenue: 30000, // 2000 * 15
        firstPartyShare: 3000,
        vendorShare: 2000
      });
    });

    it('should handle booking with only resources', () => {
      const bookingWithOnlyResources = {
        ...mockBooking,
        caterings: []
      };

      const result = generateInvoiceLineItems(bookingWithOnlyResources);

      expect(result.lineItems).toHaveLength(2);
      expect(result.resourceTotal).toBe(75000);
      expect(result.cateringTotal).toBe(0);
      expect(result.totalAmount).toBe(75000);
      expect(result.cateringRevenueSummary).toHaveLength(0);
    });

    it('should handle booking with only catering', () => {
      const bookingWithOnlyCatering = {
        ...mockBooking,
        resources: []
      };

      const result = generateInvoiceLineItems(bookingWithOnlyCatering);

      expect(result.lineItems).toHaveLength(2);
      expect(result.resourceTotal).toBe(0);
      expect(result.cateringTotal).toBe(375000);
      expect(result.totalAmount).toBe(375000);
      expect(result.cateringRevenueSummary).toHaveLength(2);
    });
  });

  describe('validateBookingForInvoiceGeneration', () => {
    it('should validate a valid booking', () => {
      const result = validateBookingForInvoiceGeneration(mockBooking);
      expect(result.isValid).toBe(true);
    });

    it('should reject booking with existing invoice', () => {
      const bookingWithInvoice = {
        ...mockBooking,
        invoice: { id: 1, status: 'PENDING', total: 100000 }
      };

      const result = validateBookingForInvoiceGeneration(bookingWithInvoice);
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Invoice already exists');
    });

    it('should reject booking with no billable items', () => {
      const emptyBooking = {
        ...mockBooking,
        resources: [],
        caterings: []
      };

      const result = validateBookingForInvoiceGeneration(emptyBooking);
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('No billable items');
    });

    it('should reject booking with invalid revenue sharing', () => {
      const bookingWithInvalidSharing = {
        ...mockBooking,
        caterings: [{
          id: 1,
          bookingId: 1,
          cateringId: 1,
          quantity: 20,
          catering: {
            id: 1,
            offerName: 'Invalid Catering',
            pricePerPerson: 15000,
            firstPartyShare: 8000, // 8000 + 6000 = 14000 ≠ 15000
            vendorShare: 6000
          }
        }]
      };

      const result = validateBookingForInvoiceGeneration(bookingWithInvalidSharing);
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Invalid revenue sharing');
    });
  });

  describe('calculateTotalRevenueBreakdown', () => {
    it('should calculate correct total revenue breakdown', () => {
      const result = generateInvoiceLineItems(mockBooking);
      const breakdown = calculateTotalRevenueBreakdown(result.cateringRevenueSummary);

      expect(breakdown.totalCateringRevenue).toBe(375000);
      expect(breakdown.totalFirstPartyRevenue).toBe(225000); // 180000 + 45000
      expect(breakdown.totalVendorRevenue).toBe(150000); // 120000 + 30000
      expect(Object.keys(breakdown.revenueByOffer)).toHaveLength(2);
      expect(breakdown.revenueByOffer['Premium Lunch Package']).toBeDefined();
      expect(breakdown.revenueByOffer['Coffee Break Service']).toBeDefined();
    });

    it('should handle empty catering revenue summary', () => {
      const breakdown = calculateTotalRevenueBreakdown([]);

      expect(breakdown.totalCateringRevenue).toBe(0);
      expect(breakdown.totalFirstPartyRevenue).toBe(0);
      expect(breakdown.totalVendorRevenue).toBe(0);
      expect(Object.keys(breakdown.revenueByOffer)).toHaveLength(0);
    });
  });

  describe('formatInvoiceAmount', () => {
    it('should format valid amounts correctly', () => {
      expect(formatInvoiceAmount(100000)).toBe('100,000 IQD');
      expect(formatInvoiceAmount(1500)).toBe('1,500 IQD');
      expect(formatInvoiceAmount(0)).toBe('0 IQD');
    });

    it('should handle invalid amounts', () => {
      expect(formatInvoiceAmount(NaN)).toBe('0 IQD');
      expect(formatInvoiceAmount(Infinity)).toBe('0 IQD');
      expect(formatInvoiceAmount(-Infinity)).toBe('0 IQD');
    });
  });

  describe('generateInvoiceSummary', () => {
    it('should generate correct invoice summary', () => {
      const result = generateInvoiceLineItems(mockBooking);
      const summary = generateInvoiceSummary(mockBooking, result);

      expect(summary).toEqual({
        bookingId: 1,
        customerName: 'Test Customer',
        resourceCount: 2,
        cateringCount: 2,
        lineItemCount: 4,
        totalAmount: 450000,
        resourceTotal: 75000,
        cateringTotal: 375000,
        cateringRevenue: {
          totalFirstPartyRevenue: 225000,
          totalVendorRevenue: 150000
        }
      });
    });

    it('should handle booking with no catering', () => {
      const bookingWithoutCatering = {
        ...mockBooking,
        caterings: []
      };

      const result = generateInvoiceLineItems(bookingWithoutCatering);
      const summary = generateInvoiceSummary(bookingWithoutCatering, result);

      expect(summary.cateringCount).toBe(0);
      expect(summary.cateringTotal).toBe(0);
      expect(summary.cateringRevenue.totalFirstPartyRevenue).toBe(0);
      expect(summary.cateringRevenue.totalVendorRevenue).toBe(0);
    });
  });
});