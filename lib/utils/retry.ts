/**
 * Retry utility for handling failed operations with exponential backoff
 */

export interface RetryOptions {
  maxAttempts?: number;
  baseDelay?: number;
  maxDelay?: number;
  backoffFactor?: number;
  shouldRetry?: (error: Error, attempt: number) => boolean;
  onRetry?: (error: Error, attempt: number) => void;
}

export class RetryError extends Error {
  public readonly attempts: number;
  public readonly lastError: Error;

  constructor(message: string, attempts: number, lastError: Error) {
    super(message);
    this.name = "RetryError";
    this.attempts = attempts;
    this.lastError = lastError;
  }
}

/**
 * Default retry condition - retries on network errors and 5xx server errors
 */
const defaultShouldRetry = (error: Error, attempt: number): boolean => {
  // Don't retry client errors (4xx) except for specific cases
  if (error.message.includes("400") || error.message.includes("401") || error.message.includes("403")) {
    return false;
  }

  // Retry on network errors
  if (error.message.includes("fetch") || error.message.includes("Network")) {
    return true;
  }

  // Retry on server errors (5xx)
  if (error.message.includes("500") || error.message.includes("502") || error.message.includes("503")) {
    return true;
  }

  // Retry on timeout errors
  if (error.message.includes("timeout") || error.message.includes("AbortError")) {
    return true;
  }

  return false;
};

/**
 * Retry a function with exponential backoff
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  const {
    maxAttempts = 3,
    baseDelay = 1000,
    maxDelay = 10000,
    backoffFactor = 2,
    shouldRetry = defaultShouldRetry,
    onRetry,
  } = options;

  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      // Don't retry on the last attempt
      if (attempt === maxAttempts) {
        break;
      }
      
      // Check if we should retry this error
      if (!shouldRetry(lastError, attempt)) {
        throw lastError;
      }
      
      // Call retry callback if provided
      if (onRetry) {
        onRetry(lastError, attempt);
      }
      
      // Calculate delay with exponential backoff
      const delay = Math.min(
        baseDelay * Math.pow(backoffFactor, attempt - 1),
        maxDelay
      );
      
      // Add jitter to prevent thundering herd
      const jitteredDelay = delay + Math.random() * 1000;
      
      console.warn(`Attempt ${attempt} failed, retrying in ${Math.round(jitteredDelay)}ms:`, lastError.message);
      
      await new Promise(resolve => setTimeout(resolve, jitteredDelay));
    }
  }
  
  throw new RetryError(
    `Operation failed after ${maxAttempts} attempts`,
    maxAttempts,
    lastError!
  );
}

/**
 * Retry specifically for API calls
 */
export async function retryApiCall<T>(
  apiCall: () => Promise<Response>,
  options: RetryOptions = {}
): Promise<Response> {
  return withRetry(async () => {
    const response = await apiCall();
    
    // Throw error for non-ok responses to trigger retry logic
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return response;
  }, {
    ...options,
    shouldRetry: (error, attempt) => {
      // Custom retry logic for API calls
      const statusMatch = error.message.match(/HTTP (\d+):/);
      if (statusMatch) {
        const status = parseInt(statusMatch[1]);
        
        // Don't retry client errors except for specific cases
        if (status >= 400 && status < 500) {
          // Retry on rate limiting or temporary client errors
          return status === 429 || status === 408;
        }
        
        // Retry on server errors
        if (status >= 500) {
          return true;
        }
      }
      
      // Use default retry logic for other errors
      return defaultShouldRetry(error, attempt);
    },
  });
}

/**
 * Hook for using retry functionality in React components
 */
export function useRetry() {
  const retryOperation = async <T>(
    operation: () => Promise<T>,
    options?: RetryOptions
  ): Promise<T> => {
    return withRetry(operation, options);
  };

  const retryApiOperation = async (
    apiCall: () => Promise<Response>,
    options?: RetryOptions
  ): Promise<Response> => {
    return retryApiCall(apiCall, options);
  };

  return {
    retryOperation,
    retryApiOperation,
  };
}