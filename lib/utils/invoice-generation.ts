/**
 * Invoice Generation Utilities
 * 
 * This module provides utilities for generating invoices from bookings,
 * including proper catering integration and revenue sharing calculations.
 */

import { calculateBookingRevenueSplit } from './catering';
import type { Booking, LineItemFormData } from '../types';

// Types for invoice generation
export interface BookingForInvoice {
  id: number;
  customer: {
    id: number;
    name: string;
    email: string;
    companyName?: string | null;
  };
  resources: Array<{
    id: number;
    name: string;
    type: string;
    basePrice: number;
  }>;
  caterings: Array<{
    id: number;
    bookingId: number;
    cateringId: number;
    quantity: number;
    catering: {
      id: number;
      offerName: string;
      pricePerPerson: number;
      firstPartyShare: number;
      vendorShare: number;
    };
  }>;
  invoice?: {
    id: number;
    status: string;
    total: number;
  } | null;
}

export interface GeneratedLineItem {
  description: string;
  amount: number;
  quantity: number;
  isCatering: boolean;
  cateringId?: number;
}

export interface InvoiceGenerationResult {
  lineItems: GeneratedLineItem[];
  totalAmount: number;
  resourceTotal: number;
  cateringTotal: number;
  cateringRevenueSummary: CateringRevenueSummary[];
}

export interface CateringRevenueSummary {
  cateringId: number;
  offerName: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  firstPartyRevenue: number;
  vendorRevenue: number;
  firstPartyShare: number;
  vendorShare: number;
}

/**
 * Generate line items from a booking for invoice creation
 * 
 * @param booking - The booking data with resources and catering
 * @returns Invoice generation result with line items and totals
 */
export function generateInvoiceLineItems(booking: BookingForInvoice): InvoiceGenerationResult {
  const lineItems: GeneratedLineItem[] = [];
  let resourceTotal = 0;
  let cateringTotal = 0;
  const cateringRevenueSummary: CateringRevenueSummary[] = [];

  // Generate resource line items
  for (const resource of booking.resources) {
    const resourceLineItem: GeneratedLineItem = {
      description: `${resource.name} (${formatResourceType(resource.type)})`,
      amount: resource.basePrice,
      quantity: 1,
      isCatering: false
    };
    
    lineItems.push(resourceLineItem);
    resourceTotal += resource.basePrice;
  }

  // Generate catering line items with revenue sharing calculations
  for (const cateringBooking of booking.caterings) {
    const catering = cateringBooking.catering;
    
    // Create line item
    const cateringLineItem: GeneratedLineItem = {
      description: `${catering.offerName} - Catering Service`,
      amount: catering.pricePerPerson, // Price per person (unit price)
      quantity: cateringBooking.quantity, // Number of people
      isCatering: true,
      cateringId: catering.id
    };
    
    lineItems.push(cateringLineItem);
    
    // Calculate totals
    const itemTotal = catering.pricePerPerson * cateringBooking.quantity;
    cateringTotal += itemTotal;
    
    // Calculate revenue sharing
    const revenueSplit = calculateBookingRevenueSplit(
      {
        pricePerPerson: catering.pricePerPerson,
        firstPartyShare: catering.firstPartyShare,
        vendorShare: catering.vendorShare
      },
      cateringBooking.quantity
    );
    
    // Add to revenue summary
    cateringRevenueSummary.push({
      cateringId: catering.id,
      offerName: catering.offerName,
      quantity: cateringBooking.quantity,
      unitPrice: catering.pricePerPerson,
      totalAmount: itemTotal,
      firstPartyRevenue: revenueSplit.firstPartyRevenue,
      vendorRevenue: revenueSplit.vendorRevenue,
      firstPartyShare: catering.firstPartyShare,
      vendorShare: catering.vendorShare
    });
  }

  const totalAmount = resourceTotal + cateringTotal;

  return {
    lineItems,
    totalAmount,
    resourceTotal,
    cateringTotal,
    cateringRevenueSummary
  };
}

/**
 * Validate that a booking can have an invoice generated
 * 
 * @param booking - The booking to validate
 * @returns Validation result with success status and error message
 */
export function validateBookingForInvoiceGeneration(booking: BookingForInvoice): {
  isValid: boolean;
  error?: string;
  message?: string;
} {
  // Check if booking already has an invoice
  if (booking.invoice) {
    return {
      isValid: false,
      error: 'Invoice already exists',
      message: 'This booking already has an associated invoice'
    };
  }

  // Check if booking has any billable items
  const hasResources = booking.resources && booking.resources.length > 0;
  const hasCatering = booking.caterings && booking.caterings.length > 0;
  
  if (!hasResources && !hasCatering) {
    return {
      isValid: false,
      error: 'No billable items',
      message: 'This booking has no resources or catering items to invoice'
    };
  }

  // Validate catering items have proper revenue sharing
  for (const cateringBooking of booking.caterings) {
    const catering = cateringBooking.catering;
    const shareTotal = catering.firstPartyShare + catering.vendorShare;
    
    if (Math.abs(shareTotal - catering.pricePerPerson) > 0.01) {
      return {
        isValid: false,
        error: 'Invalid revenue sharing',
        message: `Catering offer "${catering.offerName}" has invalid revenue sharing. Shares must add up to the price per person.`
      };
    }
  }

  return { isValid: true };
}

/**
 * Format resource type for display in line item descriptions
 * 
 * @param resourceType - The resource type enum value
 * @returns Formatted display string
 */
function formatResourceType(resourceType: string): string {
  return resourceType
    .replace(/_/g, ' ')
    .toLowerCase()
    .replace(/\b\w/g, l => l.toUpperCase());
}

/**
 * Calculate total revenue breakdown for an invoice
 * 
 * @param cateringRevenueSummary - Array of catering revenue summaries
 * @returns Total revenue breakdown
 */
export function calculateTotalRevenueBreakdown(cateringRevenueSummary: CateringRevenueSummary[]): {
  totalCateringRevenue: number;
  totalFirstPartyRevenue: number;
  totalVendorRevenue: number;
  revenueByOffer: Record<string, CateringRevenueSummary>;
} {
  const totalCateringRevenue = cateringRevenueSummary.reduce((sum, item) => sum + item.totalAmount, 0);
  const totalFirstPartyRevenue = cateringRevenueSummary.reduce((sum, item) => sum + item.firstPartyRevenue, 0);
  const totalVendorRevenue = cateringRevenueSummary.reduce((sum, item) => sum + item.vendorRevenue, 0);
  
  const revenueByOffer: Record<string, CateringRevenueSummary> = {};
  cateringRevenueSummary.forEach(item => {
    revenueByOffer[item.offerName] = item;
  });

  return {
    totalCateringRevenue,
    totalFirstPartyRevenue,
    totalVendorRevenue,
    revenueByOffer
  };
}

/**
 * Format currency amount for display
 * 
 * @param amount - The amount to format
 * @returns Formatted currency string
 */
export function formatInvoiceAmount(amount: number): string {
  if (isNaN(amount) || !isFinite(amount)) {
    return "0 IQD";
  }
  return `${amount.toLocaleString()} IQD`;
}

/**
 * Generate invoice summary for logging and debugging
 * 
 * @param booking - The booking data
 * @param result - The invoice generation result
 * @returns Summary object for logging
 */
export function generateInvoiceSummary(
  booking: BookingForInvoice,
  result: InvoiceGenerationResult
): {
  bookingId: number;
  customerName: string;
  resourceCount: number;
  cateringCount: number;
  lineItemCount: number;
  totalAmount: number;
  resourceTotal: number;
  cateringTotal: number;
  cateringRevenue: {
    totalFirstPartyRevenue: number;
    totalVendorRevenue: number;
  };
} {
  const revenueBreakdown = calculateTotalRevenueBreakdown(result.cateringRevenueSummary);
  
  return {
    bookingId: booking.id,
    customerName: booking.customer.name,
    resourceCount: booking.resources.length,
    cateringCount: booking.caterings.length,
    lineItemCount: result.lineItems.length,
    totalAmount: result.totalAmount,
    resourceTotal: result.resourceTotal,
    cateringTotal: result.cateringTotal,
    cateringRevenue: {
      totalFirstPartyRevenue: revenueBreakdown.totalFirstPartyRevenue,
      totalVendorRevenue: revenueBreakdown.totalVendorRevenue
    }
  };
}