import { z } from "zod"

// User role enum to match Prisma schema
export const UserRole = z.enum(['ADMIN', 'LOGISTICS', 'RECEIPTION'])

// Enhanced name validation with better error messages
const nameSchema = z.string()
  .min(1, "This field is required")
  .max(50, "Must be less than 50 characters")
  .regex(/^[a-zA-Z\s'-]+$/, "Can only contain letters, spaces, hyphens, and apostrophes")
  .transform(val => val.trim())
  .refine(val => val.length > 0, "Cannot be empty or only spaces")

// Enhanced username validation
const usernameSchema = z.string()
  .min(3, "Username must be at least 3 characters")
  .max(30, "Username must be less than 30 characters")
  .regex(/^[a-zA-Z0-9_]+$/, "Username can only contain letters, numbers, and underscores")
  .regex(/^[a-zA-Z]/, "Username must start with a letter")
  .transform(val => val.toLowerCase().trim())
  .refine(val => !val.includes('__'), "Username cannot contain consecutive underscores")
  .refine(val => !['admin', 'root', 'user', 'test', 'guest'].includes(val), "Username is reserved")

// Enhanced password validation with strength requirements
const passwordSchema = z.string()
  .min(6, "Password must be at least 6 characters")
  .max(100, "Password must be less than 100 characters")
  .regex(/[a-z]/, "Password must contain at least one lowercase letter")
  .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
  .regex(/[0-9]/, "Password must contain at least one number")
  .refine(val => !/\s/.test(val), "Password cannot contain spaces")
  .refine(val => !['password', '123456', 'qwerty', 'admin'].includes(val.toLowerCase()), "Password is too common")

// Schema for creating a new user
export const userCreateSchema = z.object({
  firstName: nameSchema,
  lastName: nameSchema,
  username: usernameSchema,
  password: passwordSchema,
  role: UserRole,
  isSuperUser: z.boolean().default(false)
}).refine(data => {
  // Additional cross-field validation
  return !data.username.includes(data.firstName.toLowerCase()) && 
         !data.username.includes(data.lastName.toLowerCase());
}, {
  message: "Username should not contain your first or last name",
  path: ["username"]
})

// Schema for updating a user (password is optional)
export const userUpdateSchema = z.object({
  firstName: nameSchema,
  lastName: nameSchema,
  username: usernameSchema,
  role: UserRole,
  isSuperUser: z.boolean().default(false)
}).refine(data => {
  // Additional cross-field validation
  return !data.username.includes(data.firstName.toLowerCase()) && 
         !data.username.includes(data.lastName.toLowerCase());
}, {
  message: "Username should not contain your first or last name",
  path: ["username"]
})

// Enhanced password change schema with confirmation
export const passwordChangeSchema = z.object({
  password: passwordSchema,
  confirmPassword: z.string().min(1, "Please confirm your password")
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
}).refine(data => data.password.length > 0 && data.confirmPassword.length > 0, {
  message: "Both password fields are required",
  path: ["confirmPassword"]
})

// Schema for user search and filtering
export const userSearchSchema = z.object({
  search: z.string().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  role: UserRole.optional()
})

// Type exports for use in components
export type UserCreateInput = z.infer<typeof userCreateSchema>
export type UserUpdateInput = z.infer<typeof userUpdateSchema>
export type PasswordChangeInput = z.infer<typeof passwordChangeSchema>
export type UserSearchInput = z.infer<typeof userSearchSchema>
export type UserRoleType = z.infer<typeof UserRole>