import { z } from "zod"

// Amenity icon enum to match Prisma schema
export const AmenityIcon = z.enum([
  'PROJECTOR',
  'WHITEBOARD', 
  'SMARTBOARD',
  'TABLE',
  'WIFI',
  'AIR_CONDITIONER',
  'TV',
  'MICROPHONE',
  'SPEAKER',
  'STAGE',
  'COFFEE_MACHINE',
  'WATER_DISPENSER',
  'CATERING',
  'SECURITY',
  'PARKING'
])

// Enhanced name validation with comprehensive rules
const amenityNameSchema = z.string()
  .min(1, "Amenity name is required")
  .max(100, "Amenity name must be less than 100 characters")
  .regex(/^[a-zA-Z0-9\s\-_&()]+$/, "Name can only contain letters, numbers, spaces, and basic punctuation")
  .transform(val => val.trim())
  .refine(val => val.length > 0, "Cannot be empty or only spaces")
  .refine(val => !val.toLowerCase().includes('test'), "Name cannot contain 'test'")
  .refine(val => !/^\d+$/.test(val), "Name cannot be only numbers")

// Schema for creating a new amenity
export const amenityCreateSchema = z.object({
  name: amenityNameSchema,
  icon: AmenityIcon.refine(val => val !== undefined, {
    message: "Please select a valid icon"
  })
})

// Schema for updating an amenity (same as create for amenities)
export const amenityUpdateSchema = amenityCreateSchema

// Schema for amenity search and filtering
export const amenitySearchSchema = z.object({
  search: z.string().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10)
})

// Type exports for use in components
export type AmenityCreateInput = z.infer<typeof amenityCreateSchema>
export type AmenityUpdateInput = z.infer<typeof amenityUpdateSchema>
export type AmenitySearchInput = z.infer<typeof amenitySearchSchema>
export type AmenityIconType = z.infer<typeof AmenityIcon>