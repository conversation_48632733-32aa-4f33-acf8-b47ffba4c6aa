import { z } from "zod"

// Booking status enum to match Prisma schema
export const BookingStatus = z.enum(['PENDING', 'CONFIRMED', 'CANCELLED'])

// Customer ID validation
const customerIdSchema = z.number()
  .int("Customer ID must be a whole number")
  .positive("Customer ID must be a positive number")
  .refine(val => val > 0, "Please select a valid customer")

// Resource IDs validation
const resourceIdsSchema = z.array(z.number().int().positive())
  .min(1, "At least one resource must be selected")
  .max(10, "Cannot select more than 10 resources")
  .refine(ids => new Set(ids).size === ids.length, "Duplicate resources are not allowed")

// Date validation helpers
const dateSchema = z.date()
  .refine(date => date instanceof Date && !isNaN(date.getTime()), "Invalid date")

const startDateSchema = dateSchema
  .refine(date => date > new Date(), "Start date must be in the future")

const endDateSchema = dateSchema

// Catering validation
const cateringItemSchema = z.object({
  cateringId: z.number().int().positive("Invalid catering ID"),
  quantity: z.number()
    .int("Quantity must be a whole number")
    .min(1, "Quantity must be at least 1")
    .max(1000, "Quantity cannot exceed 1000")
})

const cateringsSchema = z.array(cateringItemSchema)
  .default([])
  .refine(caterings => {
    const cateringIds = caterings.map(c => c.cateringId)
    return new Set(cateringIds).size === cateringIds.length
  }, "Duplicate catering items are not allowed")

// Schema for creating a new booking
export const bookingCreateSchema = z.object({
  customerId: customerIdSchema,
  resourceIds: resourceIdsSchema,
  status: BookingStatus.default('PENDING'),
  start: startDateSchema,
  end: endDateSchema,
  caterings: cateringsSchema
}).refine(data => {
  // Validate that end date is after start date
  return data.end > data.start
}, {
  message: "End date must be after start date",
  path: ["end"]
}).refine(data => {
  // Validate minimum booking duration (15 minutes)
  const duration = data.end.getTime() - data.start.getTime()
  const minDuration = 15 * 60 * 1000 // 15 minutes in milliseconds
  return duration >= minDuration
}, {
  message: "Booking duration must be at least 15 minutes",
  path: ["end"]
}).refine(data => {
  // Validate maximum booking duration (7 days)
  const duration = data.end.getTime() - data.start.getTime()
  const maxDuration = 7 * 24 * 60 * 60 * 1000 // 7 days in milliseconds
  return duration <= maxDuration
}, {
  message: "Booking duration cannot exceed 7 days",
  path: ["end"]
}).refine(data => {
  // Validate that booking is not too far in the future (1 year)
  const oneYearFromNow = new Date()
  oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1)
  return data.start <= oneYearFromNow
}, {
  message: "Booking cannot be scheduled more than 1 year in advance",
  path: ["start"]
}).refine(data => {
  // Validate that catering is only allowed for confirmed bookings
  if (data.caterings.length > 0 && data.status !== 'CONFIRMED') {
    return false
  }
  return true
}, {
  message: "Catering options are only available for confirmed bookings",
  path: ["caterings"]
})

// Schema for updating a booking
export const bookingUpdateSchema = z.object({
  customerId: customerIdSchema,
  resourceIds: resourceIdsSchema,
  status: BookingStatus,
  start: dateSchema, // Allow past dates for updates
  end: dateSchema,
  caterings: cateringsSchema
}).refine(data => {
  // Validate that end date is after start date
  return data.end > data.start
}, {
  message: "End date must be after start date",
  path: ["end"]
}).refine(data => {
  // Validate minimum booking duration (15 minutes)
  const duration = data.end.getTime() - data.start.getTime()
  const minDuration = 15 * 60 * 1000 // 15 minutes in milliseconds
  return duration >= minDuration
}, {
  message: "Booking duration must be at least 15 minutes",
  path: ["end"]
}).refine(data => {
  // Validate maximum booking duration (7 days)
  const duration = data.end.getTime() - data.start.getTime()
  const maxDuration = 7 * 24 * 60 * 60 * 1000 // 7 days in milliseconds
  return duration <= maxDuration
}, {
  message: "Booking duration cannot exceed 7 days",
  path: ["end"]
}).refine(data => {
  // Validate that catering is only allowed for confirmed bookings
  if (data.caterings.length > 0 && data.status !== 'CONFIRMED') {
    return false
  }
  return true
}, {
  message: "Catering options are only available for confirmed bookings",
  path: ["caterings"]
})

// Schema for booking search and filtering
export const bookingSearchSchema = z.object({
  search: z.string().optional(),
  customerId: z.coerce.number().int().positive().optional(),
  status: BookingStatus.optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10)
}).refine(data => {
  // If both start and end dates are provided, validate the range
  if (data.startDate && data.endDate) {
    return data.endDate >= data.startDate
  }
  return true
}, {
  message: "End date must be after or equal to start date",
  path: ["endDate"]
})

// Schema for booking ID parameter validation
export const bookingIdSchema = z.object({
  id: z.coerce.number().int().positive("Invalid booking ID")
})

// Schema for calendar event formatting
export const calendarEventSchema = z.object({
  id: z.string(),
  title: z.string(),
  start: z.date(),
  end: z.date(),
  backgroundColor: z.string().optional(),
  borderColor: z.string().optional(),
  textColor: z.string().optional(),
  extendedProps: z.object({
    bookingId: z.number(),
    customerName: z.string(),
    status: BookingStatus,
    resourceNames: z.array(z.string())
  })
})

// Type exports for use in components
export type BookingCreateInput = z.infer<typeof bookingCreateSchema>
export type BookingUpdateInput = z.infer<typeof bookingUpdateSchema>
export type BookingSearchInput = z.infer<typeof bookingSearchSchema>
export type BookingIdInput = z.infer<typeof bookingIdSchema>
export type BookingStatusType = z.infer<typeof BookingStatus>
export type CalendarEventInput = z.infer<typeof calendarEventSchema>

// Validation helper functions
export const validateBookingStatus = (status: string): status is BookingStatusType => {
  return BookingStatus.safeParse(status).success
}

export const getBookingFormErrors = (error: z.ZodError) => {
  const errors: Record<string, string> = {}
  
  error.errors.forEach((err) => {
    const path = err.path.join('.')
    if (!errors[path]) {
      errors[path] = err.message
    }
  })
  
  return errors
}

// Booking validation helpers
export const validateBookingDates = (start: Date, end: Date): boolean => {
  if (end <= start) return false
  
  const duration = end.getTime() - start.getTime()
  const minDuration = 15 * 60 * 1000 // 15 minutes
  const maxDuration = 7 * 24 * 60 * 60 * 1000 // 7 days
  
  return duration >= minDuration && duration <= maxDuration
}

export const validateBookingConflict = (
  start: Date, 
  end: Date, 
  resourceIds: number[], 
  excludeBookingId?: number
): boolean => {
  // This would typically check against the database for conflicts
  // For now, just validate the basic date logic
  return validateBookingDates(start, end)
}

// Calendar event formatting helper
export const formatBookingForCalendar = (booking: {
  id: number
  customer: { name: string }
  resources: { name: string }[]
  status: BookingStatusType
  start: Date
  end: Date
}): CalendarEventInput => {
  const statusColors = {
    PENDING: { backgroundColor: '#fbbf24', borderColor: '#f59e0b' },
    CONFIRMED: { backgroundColor: '#10b981', borderColor: '#059669' },
    CANCELLED: { backgroundColor: '#ef4444', borderColor: '#dc2626' }
  }

  return {
    id: booking.id.toString(),
    title: `${booking.customer.name} - ${booking.resources.map(r => r.name).join(', ')}`,
    start: booking.start,
    end: booking.end,
    ...statusColors[booking.status],
    textColor: '#ffffff',
    extendedProps: {
      bookingId: booking.id,
      customerName: booking.customer.name,
      status: booking.status,
      resourceNames: booking.resources.map(r => r.name)
    }
  }
}