import { describe, it, expect } from 'vitest'
import {
  customerCreateSchema,
  customerUpdateSchema,
  customerSearchSchema,
  customerIdSchema,
  getCustomerFormErrors,
  validateCustomerEmail,
  type CustomerCreateInput,
  type CustomerUpdateInput,
  type CustomerSearchInput,
  type CustomerIdInput
} from '../customer'

describe('Customer Validation Schemas', () => {
  describe('customerCreateSchema', () => {
    const validData = {
      name: '<PERSON>',
      email: '<EMAIL>',
      phoneNumber: '+***********-7890',
      companyName: 'Tech Solutions Inc',
      specialization: 'Software Development',
      industry: 'Technology',
      website: 'https://techsolutions.com',
      linkedIn: 'https://linkedin.com/in/johndoe',
      socialMedia: '@johndoe',
      notes: 'Important client for Q4 projects'
    }

    it('should validate correct customer data', () => {
      const result = customerCreateSchema.safeParse(validData)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.name).toBe('<PERSON>')
        expect(result.data.email).toBe('<EMAIL>')
      }
    })

    describe('Name Validation', () => {
      it('should reject empty name', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          name: ''
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('required')
        }
      })

      it('should trim and validate name', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          name: '  John Doe  '
        })
        expect(result.success).toBe(true)
        if (result.success) {
          expect(result.data.name).toBe('John Doe')
        }
      })

      it('should reject name with only spaces after trimming', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          name: '   '
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('empty or only spaces')
        }
      })

      it('should reject name longer than 100 characters', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          name: 'a'.repeat(101)
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('less than 100 characters')
        }
      })

      it('should reject name with invalid characters', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          name: 'John@Doe!'
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('can only contain letters, spaces')
        }
      })

      it('should reject name containing "test"', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          name: 'Test User'
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('cannot contain')
        }
      })

      it('should reject name that is only numbers', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          name: '12345'
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('cannot be only numbers')
        }
      })

      it('should accept valid names with allowed characters', () => {
        const validNames = [
          'John Doe',
          "Mary O'Connor",
          'Jean-Pierre Dupont',
          'Dr. Smith',
          'Anna-Maria'
        ]

        validNames.forEach(name => {
          const result = customerCreateSchema.safeParse({
            ...validData,
            name
          })
          expect(result.success).toBe(true)
        })
      })
    })

    describe('Email Validation', () => {
      it('should reject empty email', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          email: ''
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('required')
        }
      })

      it('should reject invalid email format', () => {
        const invalidEmails = [
          'invalid-email',
          '@example.com',
          'user@',
          '<EMAIL>',
          '.<EMAIL>',
          '<EMAIL>.'
        ]

        invalidEmails.forEach(email => {
          const result = customerCreateSchema.safeParse({
            ...validData,
            email
          })
          expect(result.success).toBe(false)
        })
      })

      it('should transform email to lowercase and trim', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          email: '  <EMAIL>  '
        })
        expect(result.success).toBe(true)
        if (result.success) {
          expect(result.data.email).toBe('<EMAIL>')
        }
      })

      it('should reject email longer than 255 characters', () => {
        const longEmail = 'a'.repeat(250) + '@example.com'
        const result = customerCreateSchema.safeParse({
          ...validData,
          email: longEmail
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('less than 255 characters')
        }
      })

      it('should reject email with consecutive dots', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          email: '<EMAIL>'
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('consecutive dots')
        }
      })

      it('should reject email starting or ending with dot', () => {
        const result1 = customerCreateSchema.safeParse({
          ...validData,
          email: '.<EMAIL>'
        })
        expect(result1.success).toBe(false)

        const result2 = customerCreateSchema.safeParse({
          ...validData,
          email: '<EMAIL>.'
        })
        expect(result2.success).toBe(false)
      })
    })

    describe('Phone Number Validation', () => {
      it('should accept valid phone numbers', () => {
        const validPhones = [
          '+***********-7890',
          '(*************',
          '************',
          '****** 567 8900',
          '1234567890'
        ]

        validPhones.forEach(phone => {
          const result = customerCreateSchema.safeParse({
            ...validData,
            phoneNumber: phone
          })
          expect(result.success).toBe(true)
        })
      })

      it('should reject phone number with invalid characters', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          phoneNumber: '************abc'
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('can only contain numbers')
        }
      })

      it('should reject phone number longer than 20 characters', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          phoneNumber: '1'.repeat(21)
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('less than 20 characters')
        }
      })

      it('should trim phone number and handle undefined', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          phoneNumber: '  ************  '
        })
        expect(result.success).toBe(true)
        if (result.success) {
          expect(result.data.phoneNumber).toBe('************')
        }
      })

      it('should handle optional phone number', () => {
        const { phoneNumber, ...dataWithoutPhone } = validData
        const result = customerCreateSchema.safeParse(dataWithoutPhone)
        expect(result.success).toBe(true)
        if (result.success) {
          expect(result.data.phoneNumber).toBeUndefined()
        }
      })
    })

    describe('Company Name Validation', () => {
      it('should accept valid company names', () => {
        const validCompanies = [
          'Tech Solutions Inc',
          'ABC Corp & Associates',
          'Company-123',
          'My_Company (LLC)',
          'Solutions.com'
        ]

        validCompanies.forEach(company => {
          const result = customerCreateSchema.safeParse({
            ...validData,
            companyName: company
          })
          expect(result.success).toBe(true)
        })
      })

      it('should reject company name with invalid characters', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          companyName: 'Invalid@Company!'
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('can only contain letters, numbers')
        }
      })

      it('should reject company name longer than 100 characters', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          companyName: 'a'.repeat(101)
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('less than 100 characters')
        }
      })

      it('should trim company name and handle undefined', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          companyName: '  Tech Solutions  '
        })
        expect(result.success).toBe(true)
        if (result.success) {
          expect(result.data.companyName).toBe('Tech Solutions')
        }
      })
    })

    describe('Website URL Validation', () => {
      it('should accept valid website URLs', () => {
        const validUrls = [
          'https://example.com',
          'http://example.com',
          'https://www.example.com/path',
          'http://subdomain.example.com'
        ]

        validUrls.forEach(url => {
          const result = customerCreateSchema.safeParse({
            ...validData,
            website: url
          })
          expect(result.success).toBe(true)
        })
      })

      it('should reject invalid website URLs', () => {
        const invalidUrls = [
          'not-a-url',
          'ftp://example.com',
          'example.com',
          'www.example.com'
        ]

        invalidUrls.forEach(url => {
          const result = customerCreateSchema.safeParse({
            ...validData,
            website: url
          })
          expect(result.success).toBe(false)
        })
      })

      it('should reject website URL longer than 255 characters', () => {
        const longUrl = 'https://' + 'a'.repeat(250) + '.com'
        const result = customerCreateSchema.safeParse({
          ...validData,
          website: longUrl
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('less than 255 characters')
        }
      })

      it('should require http/https protocol', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          website: 'example.com'
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('must start with http')
        }
      })
    })

    describe('LinkedIn URL Validation', () => {
      it('should accept valid LinkedIn URLs', () => {
        const validUrls = [
          'https://linkedin.com/in/johndoe',
          'http://www.linkedin.com/in/johndoe',
          'https://www.linkedin.com/company/techsolutions'
        ]

        validUrls.forEach(url => {
          const result = customerCreateSchema.safeParse({
            ...validData,
            linkedIn: url
          })
          expect(result.success).toBe(true)
        })
      })

      it('should reject non-LinkedIn URLs', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          linkedIn: 'https://facebook.com/johndoe'
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('valid LinkedIn URL')
        }
      })

      it('should reject invalid URL format', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          linkedIn: 'not-a-url'
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('valid LinkedIn URL')
        }
      })
    })

    describe('Complex Validation Rules', () => {
      it('should require specialization or industry when company name is provided', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          companyName: 'Tech Solutions',
          specialization: undefined,
          industry: undefined
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('specialization or industry')
        }
      })

      it('should accept company name with specialization', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          companyName: 'Tech Solutions',
          specialization: 'Software Development',
          industry: undefined
        })
        expect(result.success).toBe(true)
      })

      it('should accept company name with industry', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          companyName: 'Tech Solutions',
          specialization: undefined,
          industry: 'Technology'
        })
        expect(result.success).toBe(true)
      })

      it('should not require specialization/industry without company name', () => {
        const result = customerCreateSchema.safeParse({
          name: 'John Doe',
          email: '<EMAIL>',
          companyName: undefined,
          specialization: undefined,
          industry: undefined
        })
        expect(result.success).toBe(true)
      })
    })

    describe('Notes Validation', () => {
      it('should accept valid notes', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          notes: 'Important client for Q4 projects. Prefers morning meetings.'
        })
        expect(result.success).toBe(true)
      })

      it('should reject notes longer than 1000 characters', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          notes: 'a'.repeat(1001)
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('less than 1000 characters')
        }
      })

      it('should trim notes and handle undefined', () => {
        const result = customerCreateSchema.safeParse({
          ...validData,
          notes: '  Important notes  '
        })
        expect(result.success).toBe(true)
        if (result.success) {
          expect(result.data.notes).toBe('Important notes')
        }
      })
    })
  })

  describe('customerUpdateSchema', () => {
    it('should be identical to create schema', () => {
      const validData = {
        name: 'Updated Name',
        email: '<EMAIL>',
        phoneNumber: '+***********-3210',
        companyName: 'Updated Company',
        specialization: 'Updated Specialization'
      }

      const createResult = customerCreateSchema.safeParse(validData)
      const updateResult = customerUpdateSchema.safeParse(validData)

      expect(createResult.success).toBe(updateResult.success)
      if (createResult.success && updateResult.success) {
        expect(createResult.data).toEqual(updateResult.data)
      }
    })
  })

  describe('customerSearchSchema', () => {
    it('should validate search parameters with defaults', () => {
      const result = customerSearchSchema.safeParse({})
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data).toEqual({
          page: 1,
          limit: 10
        })
      }
    })

    it('should validate search with query string', () => {
      const result = customerSearchSchema.safeParse({
        search: 'john doe',
        page: 2,
        limit: 20
      })
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data).toEqual({
          search: 'john doe',
          page: 2,
          limit: 20
        })
      }
    })

    it('should coerce string numbers to numbers', () => {
      const result = customerSearchSchema.safeParse({
        page: '3',
        limit: '25'
      })
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.page).toBe(3)
        expect(result.data.limit).toBe(25)
      }
    })

    it('should reject invalid page numbers', () => {
      const result = customerSearchSchema.safeParse({
        page: 0
      })
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('greater than or equal to 1')
      }
    })

    it('should reject invalid limit values', () => {
      const result = customerSearchSchema.safeParse({
        limit: 101
      })
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('less than or equal to 100')
      }
    })
  })

  describe('customerIdSchema', () => {
    it('should validate positive integer IDs', () => {
      const result = customerIdSchema.safeParse({ id: 123 })
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.id).toBe(123)
      }
    })

    it('should coerce string numbers to numbers', () => {
      const result = customerIdSchema.safeParse({ id: '456' })
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.id).toBe(456)
      }
    })

    it('should reject negative IDs', () => {
      const result = customerIdSchema.safeParse({ id: -1 })
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Invalid customer ID')
      }
    })

    it('should reject zero ID', () => {
      const result = customerIdSchema.safeParse({ id: 0 })
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Invalid customer ID')
      }
    })

    it('should reject non-integer IDs', () => {
      const result = customerIdSchema.safeParse({ id: 123.45 })
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Expected integer')
      }
    })
  })

  describe('Helper Functions', () => {
    describe('getCustomerFormErrors', () => {
      it('should format validation errors correctly', () => {
        const result = customerCreateSchema.safeParse({
          name: '',
          email: 'invalid-email',
          phoneNumber: 'invalid-phone!'
        })
        
        expect(result.success).toBe(false)
        if (!result.success) {
          const errors = getCustomerFormErrors(result.error)
          expect(errors).toHaveProperty('name')
          expect(errors).toHaveProperty('email')
          expect(errors).toHaveProperty('phoneNumber')
          expect(typeof errors.name).toBe('string')
          expect(typeof errors.email).toBe('string')
          expect(typeof errors.phoneNumber).toBe('string')
        }
      })

      it('should handle nested path errors', () => {
        const result = customerCreateSchema.safeParse({
          name: 'John Doe',
          email: '<EMAIL>',
          companyName: 'Tech Solutions',
          // Missing specialization/industry - should trigger nested validation error
        })
        
        expect(result.success).toBe(false)
        if (!result.success) {
          const errors = getCustomerFormErrors(result.error)
          expect(errors).toHaveProperty('specialization')
        }
      })
    })

    describe('validateCustomerEmail', () => {
      it('should validate email format', async () => {
        const validEmail = await validateCustomerEmail('<EMAIL>')
        expect(validEmail).toBe(true)

        const invalidEmail = await validateCustomerEmail('invalid-email')
        expect(invalidEmail).toBe(false)
      })

      it('should handle exclude ID parameter', async () => {
        const result = await validateCustomerEmail('<EMAIL>', 123)
        expect(typeof result).toBe('boolean')
      })
    })
  })

  describe('Type Exports', () => {
    it('should export correct TypeScript types', () => {
      const createInput: CustomerCreateInput = {
        name: 'Type Test',
        email: '<EMAIL>'
      }

      const updateInput: CustomerUpdateInput = {
        name: 'Updated Type Test',
        email: '<EMAIL>'
      }

      const searchInput: CustomerSearchInput = {
        search: 'test',
        page: 1,
        limit: 10
      }

      const idInput: CustomerIdInput = {
        id: 123
      }

      expect(createInput.name).toBe('Type Test')
      expect(updateInput.email).toBe('<EMAIL>')
      expect(searchInput.page).toBe(1)
      expect(idInput.id).toBe(123)
    })
  })

  describe('Real-world Validation Scenarios', () => {
    it('should validate typical business customer', () => {
      const result = customerCreateSchema.safeParse({
        name: 'Ahmed Al-Rashid',
        email: '<EMAIL>',
        phoneNumber: '+964-************',
        companyName: 'Baghdad Tech Corporation',
        specialization: 'Enterprise Software Solutions',
        industry: 'Information Technology',
        website: 'https://baghdadtech.iq',
        linkedIn: 'https://linkedin.com/in/ahmed-al-rashid',
        notes: 'Key decision maker for government contracts. Prefers Arabic communication.'
      })
      expect(result.success).toBe(true)
    })

    it('should validate individual customer without company', () => {
      const result = customerCreateSchema.safeParse({
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        phoneNumber: '(*************',
        notes: 'Freelance consultant, works on small projects'
      })
      expect(result.success).toBe(true)
    })

    it('should validate international customer', () => {
      const result = customerCreateSchema.safeParse({
        name: 'Jean-Pierre Dubois',
        email: '<EMAIL>',
        phoneNumber: '+33-1-23-45-67-89',
        companyName: 'Solutions Européennes SARL',
        industry: 'Consulting',
        website: 'https://solutions-eu.fr',
        linkedIn: 'https://linkedin.com/in/jean-pierre-dubois'
      })
      expect(result.success).toBe(true)
    })

    it('should handle edge cases for minimum data', () => {
      const result = customerCreateSchema.safeParse({
        name: 'A',
        email: '<EMAIL>'
      })
      expect(result.success).toBe(true)
    })

    it('should handle edge cases for maximum data', () => {
      const result = customerCreateSchema.safeParse({
        name: 'A'.repeat(100),
        email: 'a'.repeat(240) + '@example.com',
        phoneNumber: '1'.repeat(20),
        companyName: 'C'.repeat(100),
        specialization: 'S'.repeat(100),
        industry: 'I'.repeat(100),
        website: 'https://' + 'w'.repeat(240) + '.com',
        linkedIn: 'https://linkedin.com/in/' + 'l'.repeat(220),
        socialMedia: 'S'.repeat(255),
        notes: 'N'.repeat(1000)
      })
      expect(result.success).toBe(true)
    })
  })
})