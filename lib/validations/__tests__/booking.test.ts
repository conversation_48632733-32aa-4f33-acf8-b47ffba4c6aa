import { describe, it, expect } from 'vitest'
import {
  bookingCreateSchema,
  bookingUpdateSchema,
  bookingSearchSchema,
  bookingIdSchema,
  calendarEventSchema,
  BookingStatus,
  validateBookingStatus,
  getBookingFormErrors,
  validateBookingDates,
  validateBookingConflict,
  formatBookingForCalendar,
  type BookingCreateInput,
  type BookingUpdateInput,
  type BookingSearchInput,
  type BookingIdInput,
  type BookingStatusType,
  type CalendarEventInput
} from '../booking'

describe('Booking Validation Schemas', () => {
  describe('BookingStatus enum', () => {
    it('should validate valid booking statuses', () => {
      expect(BookingStatus.safeParse('PENDING').success).toBe(true)
      expect(BookingStatus.safeParse('CONFIRMED').success).toBe(true)
      expect(BookingStatus.safeParse('CANCELLED').success).toBe(true)
    })

    it('should reject invalid booking statuses', () => {
      expect(BookingStatus.safeParse('INVALID').success).toBe(false)
      expect(BookingStatus.safeParse('pending').success).toBe(false)
      expect(BookingStatus.safeParse('').success).toBe(false)
    })
  })

  describe('bookingCreateSchema', () => {
    const validData = {
      customerId: 1,
      resourceIds: [1, 2],
      status: 'PENDING' as BookingStatusType,
      start: new Date('2024-12-01T10:00:00Z'),
      end: new Date('2024-12-01T12:00:00Z'),
      caterings: [
        { cateringId: 1, quantity: 10 },
        { cateringId: 2, quantity: 5 }
      ]
    }

    it('should validate correct booking data', () => {
      const result = bookingCreateSchema.safeParse(validData)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.customerId).toBe(1)
        expect(result.data.resourceIds).toEqual([1, 2])
        expect(result.data.status).toBe('PENDING')
      }
    })

    describe('Customer ID Validation', () => {
      it('should reject non-positive customer ID', () => {
        const result = bookingCreateSchema.safeParse({
          ...validData,
          customerId: 0
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('positive number')
        }
      })

      it('should reject negative customer ID', () => {
        const result = bookingCreateSchema.safeParse({
          ...validData,
          customerId: -1
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('positive number')
        }
      })

      it('should reject non-integer customer ID', () => {
        const result = bookingCreateSchema.safeParse({
          ...validData,
          customerId: 1.5
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('whole number')
        }
      })
    })

    describe('Resource IDs Validation', () => {
      it('should reject empty resource array', () => {
        const result = bookingCreateSchema.safeParse({
          ...validData,
          resourceIds: []
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('At least one resource')
        }
      })

      it('should reject too many resources', () => {
        const result = bookingCreateSchema.safeParse({
          ...validData,
          resourceIds: Array.from({ length: 11 }, (_, i) => i + 1)
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('more than 10 resources')
        }
      })

      it('should reject duplicate resource IDs', () => {
        const result = bookingCreateSchema.safeParse({
          ...validData,
          resourceIds: [1, 2, 1]
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('Duplicate resources')
        }
      })

      it('should reject non-positive resource IDs', () => {
        const result = bookingCreateSchema.safeParse({
          ...validData,
          resourceIds: [1, 0, 2]
        })
        expect(result.success).toBe(false)
      })

      it('should reject non-integer resource IDs', () => {
        const result = bookingCreateSchema.safeParse({
          ...validData,
          resourceIds: [1, 2.5, 3]
        })
        expect(result.success).toBe(false)
      })
    })

    describe('Date Validation', () => {
      it('should reject start date in the past', () => {
        const pastDate = new Date('2020-01-01T10:00:00Z')
        const result = bookingCreateSchema.safeParse({
          ...validData,
          start: pastDate
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('must be in the future')
        }
      })

      it('should reject end date before start date', () => {
        const result = bookingCreateSchema.safeParse({
          ...validData,
          start: new Date('2024-12-01T12:00:00Z'),
          end: new Date('2024-12-01T10:00:00Z')
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('End date must be after start date')
        }
      })

      it('should reject booking duration less than 15 minutes', () => {
        const start = new Date('2024-12-01T10:00:00Z')
        const end = new Date('2024-12-01T10:10:00Z') // 10 minutes
        const result = bookingCreateSchema.safeParse({
          ...validData,
          start,
          end
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('at least 15 minutes')
        }
      })

      it('should reject booking duration more than 7 days', () => {
        const start = new Date('2024-12-01T10:00:00Z')
        const end = new Date('2024-12-09T10:00:00Z') // 8 days
        const result = bookingCreateSchema.safeParse({
          ...validData,
          start,
          end
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('cannot exceed 7 days')
        }
      })

      it('should reject booking more than 1 year in advance', () => {
        const futureDate = new Date()
        futureDate.setFullYear(futureDate.getFullYear() + 2)
        const result = bookingCreateSchema.safeParse({
          ...validData,
          start: futureDate,
          end: new Date(futureDate.getTime() + 2 * 60 * 60 * 1000) // +2 hours
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('more than 1 year in advance')
        }
      })

      it('should accept valid date ranges', () => {
        const start = new Date()
        start.setDate(start.getDate() + 1) // Tomorrow
        const end = new Date(start.getTime() + 2 * 60 * 60 * 1000) // +2 hours

        const result = bookingCreateSchema.safeParse({
          ...validData,
          start,
          end
        })
        expect(result.success).toBe(true)
      })

      it('should accept exactly 15 minutes duration', () => {
        const start = new Date('2024-12-01T10:00:00Z')
        const end = new Date('2024-12-01T10:15:00Z')
        const result = bookingCreateSchema.safeParse({
          ...validData,
          start,
          end
        })
        expect(result.success).toBe(true)
      })

      it('should accept exactly 7 days duration', () => {
        const start = new Date('2024-12-01T10:00:00Z')
        const end = new Date('2024-12-08T10:00:00Z')
        const result = bookingCreateSchema.safeParse({
          ...validData,
          start,
          end
        })
        expect(result.success).toBe(true)
      })
    })

    describe('Status Validation', () => {
      it('should default to PENDING status', () => {
        const { status, ...dataWithoutStatus } = validData
        const result = bookingCreateSchema.safeParse(dataWithoutStatus)
        expect(result.success).toBe(true)
        if (result.success) {
          expect(result.data.status).toBe('PENDING')
        }
      })

      it('should accept all valid statuses', () => {
        const statuses: BookingStatusType[] = ['PENDING', 'CONFIRMED', 'CANCELLED']
        
        statuses.forEach(status => {
          const result = bookingCreateSchema.safeParse({
            ...validData,
            status
          })
          expect(result.success).toBe(true)
          if (result.success) {
            expect(result.data.status).toBe(status)
          }
        })
      })
    })

    describe('Catering Validation', () => {
      it('should accept empty catering array', () => {
        const result = bookingCreateSchema.safeParse({
          ...validData,
          caterings: []
        })
        expect(result.success).toBe(true)
        if (result.success) {
          expect(result.data.caterings).toEqual([])
        }
      })

      it('should validate catering item structure', () => {
        const result = bookingCreateSchema.safeParse({
          ...validData,
          caterings: [
            { cateringId: 1, quantity: 10 }
          ]
        })
        expect(result.success).toBe(true)
      })

      it('should reject invalid catering ID', () => {
        const result = bookingCreateSchema.safeParse({
          ...validData,
          caterings: [
            { cateringId: 0, quantity: 10 }
          ]
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('Invalid catering ID')
        }
      })

      it('should reject invalid quantity', () => {
        const result = bookingCreateSchema.safeParse({
          ...validData,
          caterings: [
            { cateringId: 1, quantity: 0 }
          ]
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('at least 1')
        }
      })

      it('should reject quantity above maximum', () => {
        const result = bookingCreateSchema.safeParse({
          ...validData,
          caterings: [
            { cateringId: 1, quantity: 1001 }
          ]
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('cannot exceed 1000')
        }
      })

      it('should reject non-integer quantity', () => {
        const result = bookingCreateSchema.safeParse({
          ...validData,
          caterings: [
            { cateringId: 1, quantity: 10.5 }
          ]
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('whole number')
        }
      })

      it('should reject duplicate catering items', () => {
        const result = bookingCreateSchema.safeParse({
          ...validData,
          caterings: [
            { cateringId: 1, quantity: 10 },
            { cateringId: 1, quantity: 5 }
          ]
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('Duplicate catering items')
        }
      })

      it('should reject catering for non-confirmed bookings', () => {
        const result = bookingCreateSchema.safeParse({
          ...validData,
          status: 'PENDING',
          caterings: [
            { cateringId: 1, quantity: 10 }
          ]
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('only available for confirmed bookings')
        }
      })

      it('should accept catering for confirmed bookings', () => {
        const result = bookingCreateSchema.safeParse({
          ...validData,
          status: 'CONFIRMED',
          caterings: [
            { cateringId: 1, quantity: 10 }
          ]
        })
        expect(result.success).toBe(true)
      })
    })
  })

  describe('bookingUpdateSchema', () => {
    const validUpdateData = {
      customerId: 1,
      resourceIds: [1, 2],
      status: 'CONFIRMED' as BookingStatusType,
      start: new Date('2023-01-01T10:00:00Z'), // Past date allowed for updates
      end: new Date('2023-01-01T12:00:00Z'),
      caterings: []
    }

    it('should allow past dates for updates', () => {
      const result = bookingUpdateSchema.safeParse(validUpdateData)
      expect(result.success).toBe(true)
    })

    it('should still validate date relationships', () => {
      const result = bookingUpdateSchema.safeParse({
        ...validUpdateData,
        start: new Date('2023-01-01T12:00:00Z'),
        end: new Date('2023-01-01T10:00:00Z')
      })
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('End date must be after start date')
      }
    })

    it('should validate all other constraints like create schema', () => {
      const result = bookingUpdateSchema.safeParse({
        ...validUpdateData,
        resourceIds: []
      })
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('At least one resource')
      }
    })
  })

  describe('bookingSearchSchema', () => {
    it('should validate search parameters with defaults', () => {
      const result = bookingSearchSchema.safeParse({})
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data).toEqual({
          page: 1,
          limit: 10
        })
      }
    })

    it('should validate complete search parameters', () => {
      const result = bookingSearchSchema.safeParse({
        search: 'customer name',
        customerId: 123,
        status: 'CONFIRMED',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
        page: 2,
        limit: 20
      })
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.search).toBe('customer name')
        expect(result.data.customerId).toBe(123)
        expect(result.data.status).toBe('CONFIRMED')
        expect(result.data.page).toBe(2)
        expect(result.data.limit).toBe(20)
      }
    })

    it('should coerce string dates and numbers', () => {
      const result = bookingSearchSchema.safeParse({
        customerId: '456',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        page: '3',
        limit: '25'
      })
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.customerId).toBe(456)
        expect(result.data.page).toBe(3)
        expect(result.data.limit).toBe(25)
        expect(result.data.startDate).toBeInstanceOf(Date)
        expect(result.data.endDate).toBeInstanceOf(Date)
      }
    })

    it('should validate date range', () => {
      const result = bookingSearchSchema.safeParse({
        startDate: new Date('2024-12-31'),
        endDate: new Date('2024-01-01')
      })
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('End date must be after or equal to start date')
      }
    })

    it('should accept equal start and end dates', () => {
      const sameDate = new Date('2024-06-15')
      const result = bookingSearchSchema.safeParse({
        startDate: sameDate,
        endDate: sameDate
      })
      expect(result.success).toBe(true)
    })

    it('should reject invalid page and limit values', () => {
      const result1 = bookingSearchSchema.safeParse({ page: 0 })
      expect(result1.success).toBe(false)

      const result2 = bookingSearchSchema.safeParse({ limit: 101 })
      expect(result2.success).toBe(false)
    })
  })

  describe('bookingIdSchema', () => {
    it('should validate positive integer IDs', () => {
      const result = bookingIdSchema.safeParse({ id: 123 })
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.id).toBe(123)
      }
    })

    it('should coerce string numbers', () => {
      const result = bookingIdSchema.safeParse({ id: '456' })
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.id).toBe(456)
      }
    })

    it('should reject invalid IDs', () => {
      const invalidIds = [0, -1, 1.5, 'abc']
      
      invalidIds.forEach(id => {
        const result = bookingIdSchema.safeParse({ id })
        expect(result.success).toBe(false)
      })
    })
  })

  describe('calendarEventSchema', () => {
    const validEvent = {
      id: '123',
      title: 'Customer Meeting',
      start: new Date('2024-12-01T10:00:00Z'),
      end: new Date('2024-12-01T12:00:00Z'),
      backgroundColor: '#10b981',
      borderColor: '#059669',
      textColor: '#ffffff',
      extendedProps: {
        bookingId: 123,
        customerName: 'John Doe',
        status: 'CONFIRMED' as BookingStatusType,
        resourceNames: ['Conference Room A', 'Projector']
      }
    }

    it('should validate complete calendar event', () => {
      const result = calendarEventSchema.safeParse(validEvent)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.id).toBe('123')
        expect(result.data.title).toBe('Customer Meeting')
        expect(result.data.extendedProps.bookingId).toBe(123)
      }
    })

    it('should validate minimal calendar event', () => {
      const minimalEvent = {
        id: '456',
        title: 'Simple Event',
        start: new Date('2024-12-01T14:00:00Z'),
        end: new Date('2024-12-01T15:00:00Z'),
        extendedProps: {
          bookingId: 456,
          customerName: 'Jane Smith',
          status: 'PENDING' as BookingStatusType,
          resourceNames: ['Meeting Room']
        }
      }

      const result = calendarEventSchema.safeParse(minimalEvent)
      expect(result.success).toBe(true)
    })

    it('should reject invalid extended props', () => {
      const result = calendarEventSchema.safeParse({
        ...validEvent,
        extendedProps: {
          bookingId: 'invalid',
          customerName: '',
          status: 'INVALID_STATUS',
          resourceNames: 'not-an-array'
        }
      })
      expect(result.success).toBe(false)
    })

    it('should require all extended props fields', () => {
      const result = calendarEventSchema.safeParse({
        ...validEvent,
        extendedProps: {
          bookingId: 123,
          customerName: 'John Doe'
          // Missing status and resourceNames
        }
      })
      expect(result.success).toBe(false)
    })
  })

  describe('Helper Functions', () => {
    describe('validateBookingStatus', () => {
      it('should validate correct booking statuses', () => {
        expect(validateBookingStatus('PENDING')).toBe(true)
        expect(validateBookingStatus('CONFIRMED')).toBe(true)
        expect(validateBookingStatus('CANCELLED')).toBe(true)
      })

      it('should reject invalid booking statuses', () => {
        expect(validateBookingStatus('INVALID')).toBe(false)
        expect(validateBookingStatus('pending')).toBe(false)
        expect(validateBookingStatus('')).toBe(false)
        expect(validateBookingStatus('123')).toBe(false)
      })
    })

    describe('getBookingFormErrors', () => {
      it('should format validation errors correctly', () => {
        const result = bookingCreateSchema.safeParse({
          customerId: 0,
          resourceIds: [],
          start: new Date('2020-01-01'),
          end: new Date('2020-01-01')
        })
        
        expect(result.success).toBe(false)
        if (!result.success) {
          const errors = getBookingFormErrors(result.error)
          expect(typeof errors).toBe('object')
          expect(Object.keys(errors).length).toBeGreaterThan(0)
          Object.values(errors).forEach(error => {
            expect(typeof error).toBe('string')
          })
        }
      })

      it('should handle nested validation errors', () => {
        const result = bookingCreateSchema.safeParse({
          customerId: 1,
          resourceIds: [1],
          status: 'PENDING',
          start: new Date('2024-12-01T10:00:00Z'),
          end: new Date('2024-12-01T09:00:00Z'), // End before start
          caterings: [
            { cateringId: 1, quantity: 10 } // Catering with PENDING status
          ]
        })
        
        expect(result.success).toBe(false)
        if (!result.success) {
          const errors = getBookingFormErrors(result.error)
          expect(errors).toHaveProperty('end')
          expect(errors).toHaveProperty('caterings')
        }
      })
    })

    describe('validateBookingDates', () => {
      it('should validate correct date ranges', () => {
        const start = new Date('2024-12-01T10:00:00Z')
        const end = new Date('2024-12-01T12:00:00Z')
        expect(validateBookingDates(start, end)).toBe(true)
      })

      it('should reject end date before start date', () => {
        const start = new Date('2024-12-01T12:00:00Z')
        const end = new Date('2024-12-01T10:00:00Z')
        expect(validateBookingDates(start, end)).toBe(false)
      })

      it('should reject duration less than 15 minutes', () => {
        const start = new Date('2024-12-01T10:00:00Z')
        const end = new Date('2024-12-01T10:10:00Z')
        expect(validateBookingDates(start, end)).toBe(false)
      })

      it('should reject duration more than 7 days', () => {
        const start = new Date('2024-12-01T10:00:00Z')
        const end = new Date('2024-12-09T10:00:00Z')
        expect(validateBookingDates(start, end)).toBe(false)
      })

      it('should accept exactly 15 minutes', () => {
        const start = new Date('2024-12-01T10:00:00Z')
        const end = new Date('2024-12-01T10:15:00Z')
        expect(validateBookingDates(start, end)).toBe(true)
      })

      it('should accept exactly 7 days', () => {
        const start = new Date('2024-12-01T10:00:00Z')
        const end = new Date('2024-12-08T10:00:00Z')
        expect(validateBookingDates(start, end)).toBe(true)
      })
    })

    describe('validateBookingConflict', () => {
      it('should validate non-conflicting bookings', () => {
        const start = new Date('2024-12-01T10:00:00Z')
        const end = new Date('2024-12-01T12:00:00Z')
        const resourceIds = [1, 2]
        expect(validateBookingConflict(start, end, resourceIds)).toBe(true)
      })

      it('should handle exclude booking ID', () => {
        const start = new Date('2024-12-01T10:00:00Z')
        const end = new Date('2024-12-01T12:00:00Z')
        const resourceIds = [1, 2]
        expect(validateBookingConflict(start, end, resourceIds, 123)).toBe(true)
      })

      it('should validate date logic even without database check', () => {
        const start = new Date('2024-12-01T12:00:00Z')
        const end = new Date('2024-12-01T10:00:00Z')
        const resourceIds = [1, 2]
        expect(validateBookingConflict(start, end, resourceIds)).toBe(false)
      })
    })

    describe('formatBookingForCalendar', () => {
      const mockBooking = {
        id: 123,
        customer: { name: 'John Doe' },
        resources: [
          { name: 'Conference Room A' },
          { name: 'Projector' }
        ],
        status: 'CONFIRMED' as BookingStatusType,
        start: new Date('2024-12-01T10:00:00Z'),
        end: new Date('2024-12-01T12:00:00Z')
      }

      it('should format booking for calendar display', () => {
        const event = formatBookingForCalendar(mockBooking)
        expect(event.id).toBe('123')
        expect(event.title).toBe('John Doe - Conference Room A, Projector')
        expect(event.start).toEqual(mockBooking.start)
        expect(event.end).toEqual(mockBooking.end)
        expect(event.extendedProps.bookingId).toBe(123)
        expect(event.extendedProps.customerName).toBe('John Doe')
        expect(event.extendedProps.status).toBe('CONFIRMED')
        expect(event.extendedProps.resourceNames).toEqual(['Conference Room A', 'Projector'])
      })

      it('should apply correct colors for different statuses', () => {
        const pendingBooking = { ...mockBooking, status: 'PENDING' as BookingStatusType }
        const confirmedBooking = { ...mockBooking, status: 'CONFIRMED' as BookingStatusType }
        const cancelledBooking = { ...mockBooking, status: 'CANCELLED' as BookingStatusType }

        const pendingEvent = formatBookingForCalendar(pendingBooking)
        const confirmedEvent = formatBookingForCalendar(confirmedBooking)
        const cancelledEvent = formatBookingForCalendar(cancelledBooking)

        expect(pendingEvent.backgroundColor).toBe('#fbbf24')
        expect(confirmedEvent.backgroundColor).toBe('#10b981')
        expect(cancelledEvent.backgroundColor).toBe('#ef4444')
      })

      it('should handle single resource', () => {
        const singleResourceBooking = {
          ...mockBooking,
          resources: [{ name: 'Meeting Room' }]
        }
        const event = formatBookingForCalendar(singleResourceBooking)
        expect(event.title).toBe('John Doe - Meeting Room')
        expect(event.extendedProps.resourceNames).toEqual(['Meeting Room'])
      })

      it('should handle no resources', () => {
        const noResourceBooking = {
          ...mockBooking,
          resources: []
        }
        const event = formatBookingForCalendar(noResourceBooking)
        expect(event.title).toBe('John Doe - ')
        expect(event.extendedProps.resourceNames).toEqual([])
      })
    })
  })

  describe('Type Exports', () => {
    it('should export correct TypeScript types', () => {
      const createInput: BookingCreateInput = {
        customerId: 1,
        resourceIds: [1, 2],
        status: 'PENDING',
        start: new Date(),
        end: new Date(),
        caterings: []
      }

      const updateInput: BookingUpdateInput = {
        customerId: 2,
        resourceIds: [3, 4],
        status: 'CONFIRMED',
        start: new Date(),
        end: new Date(),
        caterings: [{ cateringId: 1, quantity: 5 }]
      }

      const searchInput: BookingSearchInput = {
        search: 'test',
        page: 1,
        limit: 10
      }

      const idInput: BookingIdInput = {
        id: 123
      }

      const statusType: BookingStatusType = 'PENDING'

      const calendarEvent: CalendarEventInput = {
        id: '123',
        title: 'Test Event',
        start: new Date(),
        end: new Date(),
        extendedProps: {
          bookingId: 123,
          customerName: 'Test Customer',
          status: 'PENDING',
          resourceNames: ['Test Resource']
        }
      }

      expect(createInput.customerId).toBe(1)
      expect(updateInput.status).toBe('CONFIRMED')
      expect(searchInput.page).toBe(1)
      expect(idInput.id).toBe(123)
      expect(statusType).toBe('PENDING')
      expect(calendarEvent.id).toBe('123')
    })
  })

  describe('Real-world Validation Scenarios', () => {
    it('should validate typical meeting booking', () => {
      const meetingBooking = {
        customerId: 1,
        resourceIds: [1, 2], // Conference room + projector
        status: 'CONFIRMED' as BookingStatusType,
        start: new Date('2024-12-15T09:00:00Z'),
        end: new Date('2024-12-15T11:00:00Z'),
        caterings: [
          { cateringId: 1, quantity: 8 } // Coffee service for 8 people
        ]
      }

      const result = bookingCreateSchema.safeParse(meetingBooking)
      expect(result.success).toBe(true)
    })

    it('should validate all-day event booking', () => {
      const allDayBooking = {
        customerId: 2,
        resourceIds: [3], // Large conference hall
        status: 'CONFIRMED' as BookingStatusType,
        start: new Date('2024-12-20T08:00:00Z'),
        end: new Date('2024-12-20T18:00:00Z'), // 10 hours
        caterings: [
          { cateringId: 1, quantity: 50 }, // Breakfast
          { cateringId: 2, quantity: 50 }, // Lunch
          { cateringId: 3, quantity: 50 }  // Afternoon break
        ]
      }

      const result = bookingCreateSchema.safeParse(allDayBooking)
      expect(result.success).toBe(true)
    })

    it('should validate multi-day workshop booking', () => {
      const workshopBooking = {
        customerId: 3,
        resourceIds: [4, 5, 6], // Training room + equipment
        status: 'CONFIRMED' as BookingStatusType,
        start: new Date('2024-12-25T09:00:00Z'),
        end: new Date('2024-12-27T17:00:00Z'), // 3 days
        caterings: [
          { cateringId: 4, quantity: 25 } // Daily catering package
        ]
      }

      const result = bookingCreateSchema.safeParse(workshopBooking)
      expect(result.success).toBe(true)
    })

    it('should validate simple room booking without catering', () => {
      const simpleBooking = {
        customerId: 4,
        resourceIds: [7], // Small meeting room
        status: 'PENDING' as BookingStatusType,
        start: new Date('2024-12-10T14:00:00Z'),
        end: new Date('2024-12-10T15:30:00Z'), // 1.5 hours
        caterings: []
      }

      const result = bookingCreateSchema.safeParse(simpleBooking)
      expect(result.success).toBe(true)
    })

    it('should handle edge case: minimum duration booking', () => {
      const minBooking = {
        customerId: 5,
        resourceIds: [8],
        status: 'PENDING' as BookingStatusType,
        start: new Date('2024-12-05T16:00:00Z'),
        end: new Date('2024-12-05T16:15:00Z'), // Exactly 15 minutes
        caterings: []
      }

      const result = bookingCreateSchema.safeParse(minBooking)
      expect(result.success).toBe(true)
    })

    it('should handle edge case: maximum duration booking', () => {
      const maxBooking = {
        customerId: 6,
        resourceIds: [9],
        status: 'CONFIRMED' as BookingStatusType,
        start: new Date('2024-12-01T00:00:00Z'),
        end: new Date('2024-12-08T00:00:00Z'), // Exactly 7 days
        caterings: [
          { cateringId: 5, quantity: 100 }
        ]
      }

      const result = bookingCreateSchema.safeParse(maxBooking)
      expect(result.success).toBe(true)
    })
  })
})