import { describe, it, expect } from 'vitest'
import {
  invoiceCreateSchema,
  invoiceUpdateSchema,
  invoiceSearchSchema,
  invoiceIdSchema,
  paymentCreateSchema,
  paymentUpdateSchema,
  paymentSearchSchema,
  paymentIdSchema,
  InvoiceStatus,
  PaymentMethod,
  PaymentStatus,
  validateInvoiceStatus,
  validatePaymentMethod,
  validatePaymentStatus,
  getInvoiceFormErrors,
  getPaymentFormErrors,
  calculateInvoiceTotal,
  calculateInvoiceStatus,
  calculateRemainingBalance,
  validatePaymentAmount,
  validatePaymentAgainstInvoice,
  validateLineItemCatering,
  validateLineItemTotalFormula,
  type InvoiceCreateInput,
  type InvoiceUpdateInput,
  type InvoiceSearchInput,
  type InvoiceIdInput,
  type PaymentCreateInput,
  type PaymentUpdateInput,
  type PaymentSearchInput,
  type PaymentIdInput,
  type InvoiceStatusType,
  type PaymentMethodType,
  type PaymentStatusType,
  type LineItemInput
} from '../invoice'

describe('Invoice Validation Schemas', () => {
  describe('Enum Validations', () => {
    describe('InvoiceStatus', () => {
      it('should validate valid invoice statuses', () => {
        const validStatuses = ['PENDING', 'PARTIALLY_PAID', 'PAID', 'CANCELLED']
        validStatuses.forEach(status => {
          expect(InvoiceStatus.safeParse(status).success).toBe(true)
        })
      })

      it('should reject invalid invoice statuses', () => {
        const invalidStatuses = ['INVALID', 'pending', '', 'DRAFT']
        invalidStatuses.forEach(status => {
          expect(InvoiceStatus.safeParse(status).success).toBe(false)
        })
      })
    })

    describe('PaymentMethod', () => {
      it('should validate valid payment methods', () => {
        const validMethods = ['CASH', 'CREDIT_CARD', 'DEBIT_CARD', 'BANK_TRANSFER', 'CHECK', 'ONLINE', 'OTHER']
        validMethods.forEach(method => {
          expect(PaymentMethod.safeParse(method).success).toBe(true)
        })
      })

      it('should reject invalid payment methods', () => {
        const invalidMethods = ['INVALID', 'cash', '', 'PAYPAL']
        invalidMethods.forEach(method => {
          expect(PaymentMethod.safeParse(method).success).toBe(false)
        })
      })
    })

    describe('PaymentStatus', () => {
      it('should validate valid payment statuses', () => {
        const validStatuses = ['PENDING', 'COMPLETED', 'FAILED', 'REFUNDED']
        validStatuses.forEach(status => {
          expect(PaymentStatus.safeParse(status).success).toBe(true)
        })
      })

      it('should reject invalid payment statuses', () => {
        const invalidStatuses = ['INVALID', 'completed', '', 'PROCESSING']
        invalidStatuses.forEach(status => {
          expect(PaymentStatus.safeParse(status).success).toBe(false)
        })
      })
    })
  })

  describe('invoiceCreateSchema', () => {
    const validLineItems = [
      {
        description: 'Conference Room A',
        amount: 50000,
        quantity: 1,
        isCatering: false
      },
      {
        description: 'Catering Service - Premium Package',
        amount: 25000,
        quantity: 10,
        isCatering: true,
        cateringId: 1
      }
    ]

    const validData = {
      bookingId: 1,
      lineItems: validLineItems
    }

    it('should validate correct invoice data', () => {
      const result = invoiceCreateSchema.safeParse(validData)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.bookingId).toBe(1)
        expect(result.data.lineItems).toHaveLength(2)
      }
    })

    describe('Booking ID Validation', () => {
      it('should reject non-positive booking ID', () => {
        const result = invoiceCreateSchema.safeParse({
          ...validData,
          bookingId: 0
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('positive number')
        }
      })

      it('should reject negative booking ID', () => {
        const result = invoiceCreateSchema.safeParse({
          ...validData,
          bookingId: -1
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('positive number')
        }
      })

      it('should reject non-integer booking ID', () => {
        const result = invoiceCreateSchema.safeParse({
          ...validData,
          bookingId: 1.5
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('whole number')
        }
      })
    })

    describe('Line Items Validation', () => {
      it('should reject empty line items array', () => {
        const result = invoiceCreateSchema.safeParse({
          ...validData,
          lineItems: []
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('At least one line item')
        }
      })

      it('should reject too many line items', () => {
        const manyItems = Array.from({ length: 51 }, (_, i) => ({
          description: `Item ${i + 1}`,
          amount: 1000,
          quantity: 1,
          isCatering: false
        }))

        const result = invoiceCreateSchema.safeParse({
          ...validData,
          lineItems: manyItems
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('more than 50 line items')
        }
      })

      it('should reject line items with excessive total', () => {
        const expensiveItems = [
          {
            description: 'Expensive Item',
            amount: 9999999,
            quantity: 2, // Total would exceed 9,999,999.99
            isCatering: false
          }
        ]

        const result = invoiceCreateSchema.safeParse({
          ...validData,
          lineItems: expensiveItems
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('cannot exceed 9,999,999.99')
        }
      })

      describe('Line Item Structure Validation', () => {
        it('should validate line item description', () => {
          const result = invoiceCreateSchema.safeParse({
            ...validData,
            lineItems: [{
              description: '',
              amount: 1000,
              quantity: 1,
              isCatering: false
            }]
          })
          expect(result.success).toBe(false)
          if (!result.success) {
            expect(result.error.issues[0].message).toContain('required')
          }
        })

        it('should trim and validate description', () => {
          const result = invoiceCreateSchema.safeParse({
            ...validData,
            lineItems: [{
              description: '  Valid Description  ',
              amount: 1000,
              quantity: 1,
              isCatering: false
            }]
          })
          expect(result.success).toBe(true)
          if (result.success) {
            expect(result.data.lineItems[0].description).toBe('Valid Description')
          }
        })

        it('should reject description longer than 255 characters', () => {
          const result = invoiceCreateSchema.safeParse({
            ...validData,
            lineItems: [{
              description: 'a'.repeat(256),
              amount: 1000,
              quantity: 1,
              isCatering: false
            }]
          })
          expect(result.success).toBe(false)
          if (!result.success) {
            expect(result.error.issues[0].message).toContain('less than 255 characters')
          }
        })

        it('should reject description with only spaces', () => {
          const result = invoiceCreateSchema.safeParse({
            ...validData,
            lineItems: [{
              description: '   ',
              amount: 1000,
              quantity: 1,
              isCatering: false
            }]
          })
          expect(result.success).toBe(false)
          if (!result.success) {
            expect(result.error.issues[0].message).toContain('empty or only spaces')
          }
        })
      })

      describe('Amount Validation', () => {
        it('should reject amount below minimum', () => {
          const result = invoiceCreateSchema.safeParse({
            ...validData,
            lineItems: [{
              description: 'Test Item',
              amount: 0,
              quantity: 1,
              isCatering: false
            }]
          })
          expect(result.success).toBe(false)
          if (!result.success) {
            expect(result.error.issues[0].message).toContain('at least 0.01 IQD')
          }
        })

        it('should reject amount above maximum', () => {
          const result = invoiceCreateSchema.safeParse({
            ...validData,
            lineItems: [{
              description: 'Test Item',
              amount: 10000000,
              quantity: 1,
              isCatering: false
            }]
          })
          expect(result.success).toBe(false)
          if (!result.success) {
            expect(result.error.issues[0].message).toContain('cannot exceed 9,999,999.99')
          }
        })

        it('should round decimal amounts to 2 places', () => {
          const result = invoiceCreateSchema.safeParse({
            ...validData,
            lineItems: [{
              description: 'Test Item',
              amount: 1000.567,
              quantity: 1,
              isCatering: false
            }]
          })
          expect(result.success).toBe(true)
          if (result.success) {
            expect(result.data.lineItems[0].amount).toBe(1000.57)
          }
        })

        it('should reject NaN and infinite amounts', () => {
          const result1 = invoiceCreateSchema.safeParse({
            ...validData,
            lineItems: [{
              description: 'Test Item',
              amount: NaN,
              quantity: 1,
              isCatering: false
            }]
          })
          expect(result1.success).toBe(false)

          const result2 = invoiceCreateSchema.safeParse({
            ...validData,
            lineItems: [{
              description: 'Test Item',
              amount: Infinity,
              quantity: 1,
              isCatering: false
            }]
          })
          expect(result2.success).toBe(false)
        })
      })

      describe('Quantity Validation', () => {
        it('should reject quantity below minimum', () => {
          const result = invoiceCreateSchema.safeParse({
            ...validData,
            lineItems: [{
              description: 'Test Item',
              amount: 1000,
              quantity: 0,
              isCatering: false
            }]
          })
          expect(result.success).toBe(false)
          if (!result.success) {
            expect(result.error.issues[0].message).toContain('at least 1')
          }
        })

        it('should reject quantity above maximum', () => {
          const result = invoiceCreateSchema.safeParse({
            ...validData,
            lineItems: [{
              description: 'Test Item',
              amount: 1000,
              quantity: 1001,
              isCatering: false
            }]
          })
          expect(result.success).toBe(false)
          if (!result.success) {
            expect(result.error.issues[0].message).toContain('cannot exceed 1000')
          }
        })

        it('should reject non-integer quantity', () => {
          const result = invoiceCreateSchema.safeParse({
            ...validData,
            lineItems: [{
              description: 'Test Item',
              amount: 1000,
              quantity: 1.5,
              isCatering: false
            }]
          })
          expect(result.success).toBe(false)
          if (!result.success) {
            expect(result.error.issues[0].message).toContain('whole number')
          }
        })
      })

      describe('Catering Line Item Validation', () => {
        it('should require catering ID when isCatering is true', () => {
          const result = invoiceCreateSchema.safeParse({
            ...validData,
            lineItems: [{
              description: 'Catering Item',
              amount: 1000,
              quantity: 10,
              isCatering: true
              // Missing cateringId
            }]
          })
          expect(result.success).toBe(false)
          if (!result.success) {
            expect(result.error.issues[0].message).toContain('Catering ID is required')
          }
        })

        it('should reject catering ID when isCatering is false', () => {
          const result = invoiceCreateSchema.safeParse({
            ...validData,
            lineItems: [{
              description: 'Regular Item',
              amount: 1000,
              quantity: 1,
              isCatering: false,
              cateringId: 1 // Should not be provided
            }]
          })
          expect(result.success).toBe(false)
          if (!result.success) {
            expect(result.error.issues[0].message).toContain('Catering ID is required when line item is marked as catering')
          }
        })

        it('should accept valid catering line item', () => {
          const result = invoiceCreateSchema.safeParse({
            ...validData,
            lineItems: [{
              description: 'Catering Service',
              amount: 25000,
              quantity: 15,
              isCatering: true,
              cateringId: 2
            }]
          })
          expect(result.success).toBe(true)
        })

        it('should default isCatering to false', () => {
          const result = invoiceCreateSchema.safeParse({
            bookingId: 1,
            lineItems: [{
              description: 'Regular Item',
              amount: 1000,
              quantity: 1
              // isCatering not specified
            }]
          })
          expect(result.success).toBe(true)
          if (result.success) {
            expect(result.data.lineItems[0].isCatering).toBe(false)
          }
        })
      })
    })
  })

  describe('invoiceUpdateSchema', () => {
    it('should include optional status field', () => {
      const result = invoiceUpdateSchema.safeParse({
        bookingId: 1,
        lineItems: [{
          description: 'Updated Item',
          amount: 2000,
          quantity: 1,
          isCatering: false
        }],
        status: 'PARTIALLY_PAID'
      })
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.status).toBe('PARTIALLY_PAID')
      }
    })

    it('should validate same constraints as create schema', () => {
      const result = invoiceUpdateSchema.safeParse({
        bookingId: 0, // Invalid
        lineItems: [] // Invalid
      })
      expect(result.success).toBe(false)
    })
  })

  describe('invoiceSearchSchema', () => {
    it('should validate search parameters with defaults', () => {
      const result = invoiceSearchSchema.safeParse({})
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data).toEqual({
          page: 1,
          limit: 10
        })
      }
    })

    it('should validate complete search parameters', () => {
      const result = invoiceSearchSchema.safeParse({
        search: 'customer name',
        status: 'PAID',
        customerId: 123,
        bookingId: 456,
        page: 2,
        limit: 20
      })
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.search).toBe('customer name')
        expect(result.data.status).toBe('PAID')
        expect(result.data.customerId).toBe(123)
        expect(result.data.bookingId).toBe(456)
      }
    })

    it('should coerce string numbers', () => {
      const result = invoiceSearchSchema.safeParse({
        customerId: '789',
        bookingId: '101',
        page: '3',
        limit: '25'
      })
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.customerId).toBe(789)
        expect(result.data.bookingId).toBe(101)
        expect(result.data.page).toBe(3)
        expect(result.data.limit).toBe(25)
      }
    })
  })

  describe('paymentCreateSchema', () => {
    const validPaymentData = {
      amount: 50000,
      method: 'CASH' as PaymentMethodType,
      reference: 'REF-12345',
      notes: 'Payment received in full',
      paidAt: new Date('2024-12-01T10:00:00Z')
    }

    it('should validate correct payment data', () => {
      const result = paymentCreateSchema.safeParse(validPaymentData)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.amount).toBe(50000)
        expect(result.data.method).toBe('CASH')
      }
    })

    describe('Amount Validation', () => {
      it('should reject amount below minimum', () => {
        const result = paymentCreateSchema.safeParse({
          ...validPaymentData,
          amount: 0
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('at least 0.01 IQD')
        }
      })

      it('should reject amount above maximum', () => {
        const result = paymentCreateSchema.safeParse({
          ...validPaymentData,
          amount: 10000000
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('cannot exceed 9,999,999.99')
        }
      })

      it('should round decimal amounts', () => {
        const result = paymentCreateSchema.safeParse({
          ...validPaymentData,
          amount: 50000.567
        })
        expect(result.success).toBe(true)
        if (result.success) {
          expect(result.data.amount).toBe(50000.57)
        }
      })

      it('should reject NaN and infinite amounts', () => {
        const result1 = paymentCreateSchema.safeParse({
          ...validPaymentData,
          amount: NaN
        })
        expect(result1.success).toBe(false)

        const result2 = paymentCreateSchema.safeParse({
          ...validPaymentData,
          amount: Infinity
        })
        expect(result2.success).toBe(false)
      })
    })

    describe('Payment Method Validation', () => {
      it('should require payment method selection', () => {
        const { method, ...dataWithoutMethod } = validPaymentData
        const result = paymentCreateSchema.safeParse(dataWithoutMethod)
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('select a payment method')
        }
      })

      it('should accept all valid payment methods', () => {
        const methods: PaymentMethodType[] = ['CASH', 'CREDIT_CARD', 'DEBIT_CARD', 'BANK_TRANSFER', 'CHECK', 'ONLINE', 'OTHER']
        
        methods.forEach(method => {
          const result = paymentCreateSchema.safeParse({
            ...validPaymentData,
            method
          })
          expect(result.success).toBe(true)
        })
      })
    })

    describe('Optional Fields Validation', () => {
      it('should handle optional reference', () => {
        const result = paymentCreateSchema.safeParse({
          amount: 50000,
          method: 'CASH' as PaymentMethodType
        })
        expect(result.success).toBe(true)
        if (result.success) {
          expect(result.data.reference).toBeUndefined()
        }
      })

      it('should trim reference and notes', () => {
        const result = paymentCreateSchema.safeParse({
          ...validPaymentData,
          reference: '  REF-12345  ',
          notes: '  Payment notes  '
        })
        expect(result.success).toBe(true)
        if (result.success) {
          expect(result.data.reference).toBe('REF-12345')
          expect(result.data.notes).toBe('Payment notes')
        }
      })

      it('should reject reference longer than 100 characters', () => {
        const result = paymentCreateSchema.safeParse({
          ...validPaymentData,
          reference: 'a'.repeat(101)
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('less than 100 characters')
        }
      })

      it('should reject notes longer than 500 characters', () => {
        const result = paymentCreateSchema.safeParse({
          ...validPaymentData,
          notes: 'a'.repeat(501)
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('less than 500 characters')
        }
      })

      it('should handle optional paidAt date', () => {
        const { paidAt, ...dataWithoutDate } = validPaymentData
        const result = paymentCreateSchema.safeParse(dataWithoutDate)
        expect(result.success).toBe(true)
        if (result.success) {
          expect(result.data.paidAt).toBeUndefined()
        }
      })
    })
  })

  describe('paymentUpdateSchema', () => {
    it('should include payment status field', () => {
      const result = paymentUpdateSchema.safeParse({
        amount: 50000,
        method: 'CASH',
        status: 'COMPLETED'
      })
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.status).toBe('COMPLETED')
      }
    })

    it('should validate same constraints as create schema', () => {
      const result = paymentUpdateSchema.safeParse({
        amount: 0, // Invalid
        method: 'INVALID_METHOD' // Invalid
      })
      expect(result.success).toBe(false)
    })
  })

  describe('Helper Functions', () => {
    describe('validateInvoiceStatus', () => {
      it('should validate correct invoice statuses', () => {
        expect(validateInvoiceStatus('PENDING')).toBe(true)
        expect(validateInvoiceStatus('PARTIALLY_PAID')).toBe(true)
        expect(validateInvoiceStatus('PAID')).toBe(true)
        expect(validateInvoiceStatus('CANCELLED')).toBe(true)
      })

      it('should reject invalid invoice statuses', () => {
        expect(validateInvoiceStatus('INVALID')).toBe(false)
        expect(validateInvoiceStatus('pending')).toBe(false)
        expect(validateInvoiceStatus('')).toBe(false)
      })
    })

    describe('validatePaymentMethod', () => {
      it('should validate correct payment methods', () => {
        expect(validatePaymentMethod('CASH')).toBe(true)
        expect(validatePaymentMethod('CREDIT_CARD')).toBe(true)
        expect(validatePaymentMethod('BANK_TRANSFER')).toBe(true)
      })

      it('should reject invalid payment methods', () => {
        expect(validatePaymentMethod('INVALID')).toBe(false)
        expect(validatePaymentMethod('cash')).toBe(false)
        expect(validatePaymentMethod('')).toBe(false)
      })
    })

    describe('validatePaymentStatus', () => {
      it('should validate correct payment statuses', () => {
        expect(validatePaymentStatus('PENDING')).toBe(true)
        expect(validatePaymentStatus('COMPLETED')).toBe(true)
        expect(validatePaymentStatus('FAILED')).toBe(true)
        expect(validatePaymentStatus('REFUNDED')).toBe(true)
      })

      it('should reject invalid payment statuses', () => {
        expect(validatePaymentStatus('INVALID')).toBe(false)
        expect(validatePaymentStatus('completed')).toBe(false)
        expect(validatePaymentStatus('')).toBe(false)
      })
    })

    describe('getInvoiceFormErrors', () => {
      it('should format validation errors correctly', () => {
        const result = invoiceCreateSchema.safeParse({
          bookingId: 0,
          lineItems: []
        })
        
        expect(result.success).toBe(false)
        if (!result.success) {
          const errors = getInvoiceFormErrors(result.error)
          expect(typeof errors).toBe('object')
          expect(Object.keys(errors).length).toBeGreaterThan(0)
          Object.values(errors).forEach(error => {
            expect(typeof error).toBe('string')
          })
        }
      })
    })

    describe('getPaymentFormErrors', () => {
      it('should format payment validation errors correctly', () => {
        const result = paymentCreateSchema.safeParse({
          amount: 0
          // Missing method
        })
        
        expect(result.success).toBe(false)
        if (!result.success) {
          const errors = getPaymentFormErrors(result.error)
          expect(typeof errors).toBe('object')
          expect(Object.keys(errors).length).toBeGreaterThan(0)
        }
      })
    })

    describe('calculateInvoiceTotal', () => {
      it('should calculate total from line items', () => {
        const lineItems: LineItemInput[] = [
          { description: 'Item 1', amount: 1000, quantity: 2, isCatering: false },
          { description: 'Item 2', amount: 500, quantity: 3, isCatering: false }
        ]
        const total = calculateInvoiceTotal(lineItems)
        expect(total).toBe(3500) // (1000 * 2) + (500 * 3)
      })

      it('should handle catering items correctly', () => {
        const lineItems: LineItemInput[] = [
          { description: 'Regular Item', amount: 1000, quantity: 1, isCatering: false },
          { description: 'Catering Item', amount: 250, quantity: 10, isCatering: true, cateringId: 1 }
        ]
        const total = calculateInvoiceTotal(lineItems)
        expect(total).toBe(3500) // 1000 + (250 * 10)
      })

      it('should handle empty line items', () => {
        const total = calculateInvoiceTotal([])
        expect(total).toBe(0)
      })

      it('should handle invalid numbers gracefully', () => {
        const lineItems: LineItemInput[] = [
          { description: 'Item 1', amount: NaN, quantity: 2, isCatering: false },
          { description: 'Item 2', amount: 500, quantity: 3, isCatering: false }
        ]
        const total = calculateInvoiceTotal(lineItems)
        expect(total).toBe(1500) // Only valid item: 500 * 3
      })

      it('should handle infinite values gracefully', () => {
        const lineItems: LineItemInput[] = [
          { description: 'Item 1', amount: Infinity, quantity: 1, isCatering: false },
          { description: 'Item 2', amount: 500, quantity: 2, isCatering: false }
        ]
        const total = calculateInvoiceTotal(lineItems)
        expect(total).toBe(1000) // Only valid item: 500 * 2
      })
    })

    describe('calculateInvoiceStatus', () => {
      it('should return PENDING for zero payment', () => {
        expect(calculateInvoiceStatus(1000, 0)).toBe('PENDING')
      })

      it('should return PARTIALLY_PAID for partial payment', () => {
        expect(calculateInvoiceStatus(1000, 500)).toBe('PARTIALLY_PAID')
      })

      it('should return PAID for full payment', () => {
        expect(calculateInvoiceStatus(1000, 1000)).toBe('PAID')
      })

      it('should return PAID for overpayment', () => {
        expect(calculateInvoiceStatus(1000, 1200)).toBe('PAID')
      })
    })

    describe('calculateRemainingBalance', () => {
      it('should calculate remaining balance correctly', () => {
        expect(calculateRemainingBalance(1000, 300)).toBe(700)
      })

      it('should return zero for full payment', () => {
        expect(calculateRemainingBalance(1000, 1000)).toBe(0)
      })

      it('should return zero for overpayment', () => {
        expect(calculateRemainingBalance(1000, 1200)).toBe(0)
      })

      it('should handle zero total', () => {
        expect(calculateRemainingBalance(0, 0)).toBe(0)
      })
    })

    describe('validatePaymentAmount', () => {
      it('should validate correct payment amounts', () => {
        expect(validatePaymentAmount(500, 1000, 300)).toBe(true) // 500 <= (1000 - 300)
      })

      it('should reject payment exceeding remaining balance', () => {
        expect(validatePaymentAmount(800, 1000, 300)).toBe(false) // 800 > (1000 - 300)
      })

      it('should reject zero or negative payments', () => {
        expect(validatePaymentAmount(0, 1000, 300)).toBe(false)
        expect(validatePaymentAmount(-100, 1000, 300)).toBe(false)
      })

      it('should handle fully paid invoices', () => {
        expect(validatePaymentAmount(100, 1000, 1000)).toBe(false) // No remaining balance
      })
    })

    describe('validatePaymentAgainstInvoice', () => {
      it('should validate correct payment', () => {
        const payment: PaymentCreateInput = { amount: 500, method: 'CASH' }
        const invoice = { total: 1000, paid: 300 }
        const result = validatePaymentAgainstInvoice(payment, invoice)
        expect(result.isValid).toBe(true)
      })

      it('should reject payment exceeding balance', () => {
        const payment: PaymentCreateInput = { amount: 800, method: 'CASH' }
        const invoice = { total: 1000, paid: 300 }
        const result = validatePaymentAgainstInvoice(payment, invoice)
        expect(result.isValid).toBe(false)
        expect(result.error).toContain('exceeds remaining balance')
      })

      it('should reject zero payment', () => {
        const payment: PaymentCreateInput = { amount: 0, method: 'CASH' }
        const invoice = { total: 1000, paid: 300 }
        const result = validatePaymentAgainstInvoice(payment, invoice)
        expect(result.isValid).toBe(false)
        expect(result.error).toContain('must be greater than 0')
      })
    })

    describe('validateLineItemCatering', () => {
      const availableCaterings = [
        { id: 1, offerName: 'Premium Package', pricePerPerson: 25000 },
        { id: 2, offerName: 'Basic Package', pricePerPerson: 15000 }
      ]

      it('should validate non-catering items', () => {
        const lineItem: LineItemInput = {
          description: 'Regular Item',
          amount: 1000,
          quantity: 1,
          isCatering: false
        }
        const result = validateLineItemCatering(lineItem, availableCaterings)
        expect(result.isValid).toBe(true)
      })

      it('should validate correct catering item', () => {
        const lineItem: LineItemInput = {
          description: 'Catering Service',
          amount: 25000,
          quantity: 10,
          isCatering: true,
          cateringId: 1
        }
        const result = validateLineItemCatering(lineItem, availableCaterings)
        expect(result.isValid).toBe(true)
      })

      it('should reject catering item without catering ID', () => {
        const lineItem: LineItemInput = {
          description: 'Catering Service',
          amount: 25000,
          quantity: 10,
          isCatering: true
        }
        const result = validateLineItemCatering(lineItem, availableCaterings)
        expect(result.isValid).toBe(false)
        expect(result.error).toContain('Catering ID is required')
      })

      it('should reject catering item with non-existent catering ID', () => {
        const lineItem: LineItemInput = {
          description: 'Catering Service',
          amount: 25000,
          quantity: 10,
          isCatering: true,
          cateringId: 999
        }
        const result = validateLineItemCatering(lineItem, availableCaterings)
        expect(result.isValid).toBe(false)
        expect(result.error).toContain('not found')
      })

      it('should reject catering item with incorrect price', () => {
        const lineItem: LineItemInput = {
          description: 'Catering Service',
          amount: 30000, // Should be 25000
          quantity: 10,
          isCatering: true,
          cateringId: 1
        }
        const result = validateLineItemCatering(lineItem, availableCaterings)
        expect(result.isValid).toBe(false)
        expect(result.error).toContain('must match the price per person')
      })

      it('should reject catering item with zero quantity', () => {
        const lineItem: LineItemInput = {
          description: 'Catering Service',
          amount: 25000,
          quantity: 0,
          isCatering: true,
          cateringId: 1
        }
        const result = validateLineItemCatering(lineItem, availableCaterings)
        expect(result.isValid).toBe(false)
        expect(result.error).toContain('must be at least 1')
      })
    })

    describe('validateLineItemTotalFormula', () => {
      it('should validate correct line item calculation', () => {
        const lineItem: LineItemInput = {
          description: 'Test Item',
          amount: 1000,
          quantity: 5,
          isCatering: false
        }
        const result = validateLineItemTotalFormula(lineItem)
        expect(result.isValid).toBe(true)
        expect(result.expectedTotal).toBe(5000)
      })

      it('should reject zero amount', () => {
        const lineItem: LineItemInput = {
          description: 'Test Item',
          amount: 0,
          quantity: 5,
          isCatering: false
        }
        const result = validateLineItemTotalFormula(lineItem)
        expect(result.isValid).toBe(false)
        expect(result.error).toContain('must be greater than 0')
      })

      it('should reject zero quantity', () => {
        const lineItem: LineItemInput = {
          description: 'Test Item',
          amount: 1000,
          quantity: 0,
          isCatering: false
        }
        const result = validateLineItemTotalFormula(lineItem)
        expect(result.isValid).toBe(false)
        expect(result.error).toContain('must be at least 1')
      })

      it('should handle invalid numbers', () => {
        const lineItem: LineItemInput = {
          description: 'Test Item',
          amount: NaN,
          quantity: 5,
          isCatering: false
        }
        const result = validateLineItemTotalFormula(lineItem)
        expect(result.isValid).toBe(false)
        expect(result.error).toContain('invalid values')
      })

      it('should handle infinite values', () => {
        const lineItem: LineItemInput = {
          description: 'Test Item',
          amount: Infinity,
          quantity: 5,
          isCatering: false
        }
        const result = validateLineItemTotalFormula(lineItem)
        expect(result.isValid).toBe(false)
        expect(result.error).toContain('invalid values')
      })
    })
  })

  describe('Type Exports', () => {
    it('should export correct TypeScript types', () => {
      const createInput: InvoiceCreateInput = {
        bookingId: 1,
        lineItems: [{
          description: 'Test Item',
          amount: 1000,
          quantity: 1,
          isCatering: false
        }]
      }

      const updateInput: InvoiceUpdateInput = {
        bookingId: 2,
        lineItems: [{
          description: 'Updated Item',
          amount: 2000,
          quantity: 2,
          isCatering: true,
          cateringId: 1
        }],
        status: 'PAID'
      }

      const searchInput: InvoiceSearchInput = {
        search: 'test',
        status: 'PENDING',
        page: 1,
        limit: 10
      }

      const paymentInput: PaymentCreateInput = {
        amount: 50000,
        method: 'CASH'
      }

      const statusType: InvoiceStatusType = 'PENDING'
      const methodType: PaymentMethodType = 'CASH'
      const paymentStatusType: PaymentStatusType = 'COMPLETED'

      expect(createInput.bookingId).toBe(1)
      expect(updateInput.status).toBe('PAID')
      expect(searchInput.status).toBe('PENDING')
      expect(paymentInput.method).toBe('CASH')
      expect(statusType).toBe('PENDING')
      expect(methodType).toBe('CASH')
      expect(paymentStatusType).toBe('COMPLETED')
    })
  })

  describe('Real-world Validation Scenarios', () => {
    it('should validate typical service invoice', () => {
      const serviceInvoice = {
        bookingId: 1,
        lineItems: [
          {
            description: 'Conference Room A - Full Day',
            amount: 100000,
            quantity: 1,
            isCatering: false
          },
          {
            description: 'Audio/Visual Equipment',
            amount: 25000,
            quantity: 1,
            isCatering: false
          },
          {
            description: 'Premium Lunch Catering',
            amount: 35000,
            quantity: 20,
            isCatering: true,
            cateringId: 1
          }
        ]
      }

      const result = invoiceCreateSchema.safeParse(serviceInvoice)
      expect(result.success).toBe(true)
      if (result.success) {
        const total = calculateInvoiceTotal(result.data.lineItems)
        expect(total).toBe(825000) // 100000 + 25000 + (35000 * 20)
      }
    })

    it('should validate multi-day workshop invoice', () => {
      const workshopInvoice = {
        bookingId: 2,
        lineItems: [
          {
            description: 'Training Room - 3 Days',
            amount: 150000,
            quantity: 3,
            isCatering: false
          },
          {
            description: 'Equipment Rental - 3 Days',
            amount: 50000,
            quantity: 3,
            isCatering: false
          },
          {
            description: 'Daily Catering Package',
            amount: 40000,
            quantity: 75, // 25 people × 3 days
            isCatering: true,
            cateringId: 2
          }
        ]
      }

      const result = invoiceCreateSchema.safeParse(workshopInvoice)
      expect(result.success).toBe(true)
      if (result.success) {
        const total = calculateInvoiceTotal(result.data.lineItems)
        expect(total).toBe(3600000) // (150000 * 3) + (50000 * 3) + (40000 * 75)
      }
    })

    it('should validate payment scenarios', () => {
      const fullPayment: PaymentCreateInput = {
        amount: 825000,
        method: 'BANK_TRANSFER',
        reference: 'TXN-2024-001',
        notes: 'Full payment for conference room booking',
        paidAt: new Date('2024-12-01T14:30:00Z')
      }

      const partialPayment: PaymentCreateInput = {
        amount: 400000,
        method: 'CREDIT_CARD',
        reference: 'CC-2024-002',
        notes: 'Partial payment - 50% deposit'
      }

      const cashPayment: PaymentCreateInput = {
        amount: 425000,
        method: 'CASH',
        notes: 'Remaining balance paid in cash'
      }

      expect(paymentCreateSchema.safeParse(fullPayment).success).toBe(true)
      expect(paymentCreateSchema.safeParse(partialPayment).success).toBe(true)
      expect(paymentCreateSchema.safeParse(cashPayment).success).toBe(true)
    })

    it('should handle edge cases for maximum values', () => {
      const maxInvoice = {
        bookingId: 999999,
        lineItems: [
          {
            description: 'Maximum Value Item',
            amount: 9999999.99,
            quantity: 1,
            isCatering: false
          }
        ]
      }

      const result = invoiceCreateSchema.safeParse(maxInvoice)
      expect(result.success).toBe(true)
    })

    it('should handle edge cases for minimum values', () => {
      const minInvoice = {
        bookingId: 1,
        lineItems: [
          {
            description: 'A',
            amount: 0.01,
            quantity: 1,
            isCatering: false
          }
        ]
      }

      const result = invoiceCreateSchema.safeParse(minInvoice)
      expect(result.success).toBe(true)
    })
  })
})