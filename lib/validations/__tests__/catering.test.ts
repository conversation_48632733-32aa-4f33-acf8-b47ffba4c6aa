import { describe, it, expect } from 'vitest'
import {
  cateringCreateSchema,
  cateringUpdateSchema,
  cateringSearchSchema,
  type CateringCreateInput,
  type CateringUpdateInput,
  type CateringSearchInput
} from '../catering'

describe('Catering Validation Schemas', () => {
  describe('cateringCreateSchema', () => {
    const validData = {
      offerName: 'Premium Catering Package',
      pricePerPerson: 1000,
      firstPartyShare: 600,
      vendorShare: 400
    }

    it('should validate correct catering data', () => {
      const result = cateringCreateSchema.safeParse(validData)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data).toEqual(validData)
      }
    })

    describe('Offer Name Validation', () => {
      it('should reject empty offer name', () => {
        const result = cateringCreateSchema.safeParse({
          ...validData,
          offerName: ''
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('required')
        }
      })

      it('should trim and validate offer name', () => {
        const result = cateringCreateSchema.safeParse({
          ...validData,
          offerName: '  Valid Name  '
        })
        expect(result.success).toBe(true)
        if (result.success) {
          expect(result.data.offerName).toBe('Valid Name')
        }
      })

      it('should reject offer name with only spaces after trimming', () => {
        const result = cateringCreateSchema.safeParse({
          ...validData,
          offerName: '   '
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('empty or only spaces')
        }
      })

      it('should reject offer name longer than 100 characters', () => {
        const result = cateringCreateSchema.safeParse({
          ...validData,
          offerName: 'a'.repeat(101)
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('less than 100 characters')
        }
      })

      it('should reject offer name with invalid characters', () => {
        const result = cateringCreateSchema.safeParse({
          ...validData,
          offerName: 'Invalid@Name!'
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('can only contain letters, numbers')
        }
      })

      it('should reject offer name containing "test"', () => {
        const result = cateringCreateSchema.safeParse({
          ...validData,
          offerName: 'Test Catering'
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('cannot contain the word')
        }
      })

      it('should reject offer name that is only numbers', () => {
        const result = cateringCreateSchema.safeParse({
          ...validData,
          offerName: '12345'
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('cannot be only numbers')
        }
      })

      it('should reject offer name shorter than 3 characters', () => {
        const result = cateringCreateSchema.safeParse({
          ...validData,
          offerName: 'AB'
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('at least 3 characters')
        }
      })

      it('should accept valid offer names with allowed characters', () => {
        const validNames = [
          'Premium Catering',
          'Lunch & Dinner Package',
          'Meeting Room Service (VIP)',
          'Corporate Event-2024',
          'Breakfast_Special'
        ]

        validNames.forEach(name => {
          const result = cateringCreateSchema.safeParse({
            ...validData,
            offerName: name
          })
          expect(result.success).toBe(true)
        })
      })
    })

    describe('Price Per Person Validation', () => {
      it('should reject price below minimum', () => {
        const result = cateringCreateSchema.safeParse({
          ...validData,
          pricePerPerson: 0
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('at least 1 IQD')
        }
      })

      it('should reject price above maximum', () => {
        const result = cateringCreateSchema.safeParse({
          ...validData,
          pricePerPerson: 1000000
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('cannot exceed 999,999 IQD')
        }
      })

      it('should round decimal prices to whole numbers', () => {
        const result = cateringCreateSchema.safeParse({
          ...validData,
          pricePerPerson: 1000.7,
          firstPartyShare: 600,
          vendorShare: 401 // Adjusted to match rounded price
        })
        expect(result.success).toBe(true)
        if (result.success) {
          expect(result.data.pricePerPerson).toBe(1001)
        }
      })

      it('should accept valid price ranges', () => {
        const validPrices = [1, 500, 1000, 50000, 999999]
        
        validPrices.forEach(price => {
          const result = cateringCreateSchema.safeParse({
            ...validData,
            pricePerPerson: price,
            firstPartyShare: Math.floor(price * 0.6),
            vendorShare: price - Math.floor(price * 0.6)
          })
          expect(result.success).toBe(true)
        })
      })
    })

    describe('Share Amount Validation', () => {
      it('should reject negative first party share', () => {
        const result = cateringCreateSchema.safeParse({
          ...validData,
          firstPartyShare: -100
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('at least 0 IQD')
        }
      })

      it('should reject negative vendor share', () => {
        const result = cateringCreateSchema.safeParse({
          ...validData,
          vendorShare: -100
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('at least 0 IQD')
        }
      })

      it('should reject share amounts above maximum', () => {
        const result = cateringCreateSchema.safeParse({
          ...validData,
          firstPartyShare: 1000000
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('cannot exceed 999,999 IQD')
        }
      })

      it('should round decimal share amounts to whole numbers', () => {
        const result = cateringCreateSchema.safeParse({
          ...validData,
          pricePerPerson: 1000,
          firstPartyShare: 600.7,
          vendorShare: 399.3
        })
        expect(result.success).toBe(true)
        if (result.success) {
          expect(result.data.firstPartyShare).toBe(601)
          expect(result.data.vendorShare).toBe(399)
        }
      })
    })

    describe('Revenue Sharing Validation', () => {
      it('should reject when shares do not add up to price per person', () => {
        const result = cateringCreateSchema.safeParse({
          ...validData,
          pricePerPerson: 1000,
          firstPartyShare: 600,
          vendorShare: 300 // Total: 900, should be 1000
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('must add up to exactly the price per person')
        }
      })

      it('should accept when shares add up correctly', () => {
        const result = cateringCreateSchema.safeParse({
          ...validData,
          pricePerPerson: 1000,
          firstPartyShare: 700,
          vendorShare: 300
        })
        expect(result.success).toBe(true)
      })

      it('should handle floating point precision in validation', () => {
        const result = cateringCreateSchema.safeParse({
          ...validData,
          pricePerPerson: 1000,
          firstPartyShare: 333,
          vendorShare: 667 // Total: 1000
        })
        expect(result.success).toBe(true)
      })

      it('should reject when individual shares exceed price per person', () => {
        const result = cateringCreateSchema.safeParse({
          ...validData,
          pricePerPerson: 1000,
          firstPartyShare: 1200, // Exceeds price per person
          vendorShare: -200 // Negative to make total correct
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues.some(issue => 
            issue.message.includes('cannot exceed the total price per person')
          )).toBe(true)
        }
      })

      it('should reject zero-zero split', () => {
        const result = cateringCreateSchema.safeParse({
          ...validData,
          pricePerPerson: 0,
          firstPartyShare: 0,
          vendorShare: 0
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues.some(issue => 
            issue.message.includes('At least one party must receive a share')
          )).toBe(true)
        }
      })

      it('should accept one-sided splits', () => {
        const result1 = cateringCreateSchema.safeParse({
          ...validData,
          pricePerPerson: 1000,
          firstPartyShare: 1000,
          vendorShare: 0
        })
        expect(result1.success).toBe(true)

        const result2 = cateringCreateSchema.safeParse({
          ...validData,
          pricePerPerson: 1000,
          firstPartyShare: 0,
          vendorShare: 1000
        })
        expect(result2.success).toBe(true)
      })
    })

    describe('Complex Validation Scenarios', () => {
      it('should handle multiple validation errors', () => {
        const result = cateringCreateSchema.safeParse({
          offerName: '', // Invalid
          pricePerPerson: -100, // Invalid
          firstPartyShare: -50, // Invalid
          vendorShare: -50 // Invalid
        })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues.length).toBeGreaterThan(1)
        }
      })

      it('should validate edge cases for revenue sharing', () => {
        // Test with very small amounts
        const result = cateringCreateSchema.safeParse({
          ...validData,
          pricePerPerson: 1,
          firstPartyShare: 1,
          vendorShare: 0
        })
        expect(result.success).toBe(true)
      })

      it('should validate edge cases for maximum values', () => {
        const result = cateringCreateSchema.safeParse({
          ...validData,
          pricePerPerson: 999999,
          firstPartyShare: 500000,
          vendorShare: 499999
        })
        expect(result.success).toBe(true)
      })
    })
  })

  describe('cateringUpdateSchema', () => {
    it('should be identical to create schema', () => {
      const validData = {
        offerName: 'Updated Catering Package',
        pricePerPerson: 1200,
        firstPartyShare: 720,
        vendorShare: 480
      }

      const createResult = cateringCreateSchema.safeParse(validData)
      const updateResult = cateringUpdateSchema.safeParse(validData)

      expect(createResult.success).toBe(updateResult.success)
      if (createResult.success && updateResult.success) {
        expect(createResult.data).toEqual(updateResult.data)
      }
    })
  })

  describe('cateringSearchSchema', () => {
    it('should validate search parameters with defaults', () => {
      const result = cateringSearchSchema.safeParse({})
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data).toEqual({
          page: 1,
          limit: 10
        })
      }
    })

    it('should validate search with query string', () => {
      const result = cateringSearchSchema.safeParse({
        search: 'premium catering',
        page: 2,
        limit: 20
      })
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data).toEqual({
          search: 'premium catering',
          page: 2,
          limit: 20
        })
      }
    })

    it('should coerce string numbers to numbers', () => {
      const result = cateringSearchSchema.safeParse({
        page: '3',
        limit: '25'
      })
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.page).toBe(3)
        expect(result.data.limit).toBe(25)
      }
    })

    it('should reject invalid page numbers', () => {
      const result = cateringSearchSchema.safeParse({
        page: 0
      })
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('greater than or equal to 1')
      }
    })

    it('should reject invalid limit values', () => {
      const result = cateringSearchSchema.safeParse({
        limit: 101
      })
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('less than or equal to 100')
      }
    })

    it('should accept empty search string', () => {
      const result = cateringSearchSchema.safeParse({
        search: ''
      })
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.search).toBe('')
      }
    })
  })

  describe('Type Exports', () => {
    it('should export correct TypeScript types', () => {
      // This test ensures the types are properly exported and can be used
      const createInput: CateringCreateInput = {
        offerName: 'Type Test',
        pricePerPerson: 1000,
        firstPartyShare: 600,
        vendorShare: 400
      }

      const updateInput: CateringUpdateInput = {
        offerName: 'Updated Type Test',
        pricePerPerson: 1200,
        firstPartyShare: 720,
        vendorShare: 480
      }

      const searchInput: CateringSearchInput = {
        search: 'test',
        page: 1,
        limit: 10
      }

      // If this compiles without errors, the types are correctly exported
      expect(createInput.offerName).toBe('Type Test')
      expect(updateInput.pricePerPerson).toBe(1200)
      expect(searchInput.page).toBe(1)
    })
  })

  describe('Real-world Validation Scenarios', () => {
    it('should validate typical restaurant catering offer', () => {
      const result = cateringCreateSchema.safeParse({
        offerName: 'Corporate Lunch Package',
        pricePerPerson: 25000, // 25,000 IQD
        firstPartyShare: 15000, // 60%
        vendorShare: 10000 // 40%
      })
      expect(result.success).toBe(true)
    })

    it('should validate high-end catering offer', () => {
      const result = cateringCreateSchema.safeParse({
        offerName: 'Premium Wedding Catering',
        pricePerPerson: 100000, // 100,000 IQD
        firstPartyShare: 70000, // 70%
        vendorShare: 30000 // 30%
      })
      expect(result.success).toBe(true)
    })

    it('should validate budget catering offer', () => {
      const result = cateringCreateSchema.safeParse({
        offerName: 'Basic Meeting Refreshments',
        pricePerPerson: 5000, // 5,000 IQD
        firstPartyShare: 2000, // 40%
        vendorShare: 3000 // 60%
      })
      expect(result.success).toBe(true)
    })

    it('should reject unrealistic pricing', () => {
      const result = cateringCreateSchema.safeParse({
        offerName: 'Unrealistic Offer',
        pricePerPerson: 2000000, // 2,000,000 IQD - too high
        firstPartyShare: 1000000,
        vendorShare: 1000000
      })
      expect(result.success).toBe(false)
    })
  })
})