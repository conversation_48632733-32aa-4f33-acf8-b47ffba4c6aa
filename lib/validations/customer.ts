import { z } from "zod"

// Enhanced name validation with comprehensive rules
const customerNameSchema = z.string()
  .min(1, "Customer name is required")
  .max(100, "Customer name must be less than 100 characters")
  .regex(/^[a-zA-Z\s\-'\.]+$/, "Name can only contain letters, spaces, hyphens, apostrophes, and periods")
  .transform(val => val.trim())
  .refine(val => val.length > 0, "Cannot be empty or only spaces")
  .refine(val => !val.toLowerCase().includes('test'), "Name cannot contain 'test'")
  .refine(val => !/^\d+$/.test(val), "Name cannot be only numbers")

// Enhanced email validation
const emailSchema = z.string()
  .min(1, "Email is required")
  .email("Please enter a valid email address")
  .max(255, "Email must be less than 255 characters")
  .transform(val => val.toLowerCase().trim())
  .refine(val => !val.includes('..'), "Email cannot contain consecutive dots")
  .refine(val => !val.startsWith('.') && !val.endsWith('.'), "Email cannot start or end with a dot")

// Phone number validation (optional)
const phoneNumberSchema = z.string()
  .max(20, "Phone number must be less than 20 characters")
  .regex(/^[\+]?[0-9\s\-\(\)]+$/, "Phone number can only contain numbers, spaces, hyphens, parentheses, and plus sign")
  .optional()
  .transform(val => val?.trim() || undefined)

// Company name validation (optional)
const companyNameSchema = z.string()
  .max(100, "Company name must be less than 100 characters")
  .regex(/^[a-zA-Z0-9\s\-_&()\.]+$/, "Company name can only contain letters, numbers, spaces, and basic punctuation")
  .optional()
  .transform(val => val?.trim() || undefined)

// Specialization validation (optional)
const specializationSchema = z.string()
  .max(100, "Specialization must be less than 100 characters")
  .optional()
  .transform(val => val?.trim() || undefined)

// Industry validation (optional)
const industrySchema = z.string()
  .max(100, "Industry must be less than 100 characters")
  .optional()
  .transform(val => val?.trim() || undefined)

// Website URL validation (optional)
const websiteSchema = z.string()
  .max(255, "Website URL must be less than 255 characters")
  .url("Please enter a valid website URL")
  .optional()
  .transform(val => val?.trim() || undefined)
  .refine(val => !val || val.startsWith('http'), "Website URL must start with http:// or https://")

// LinkedIn URL validation (optional)
const linkedInSchema = z.string()
  .max(255, "LinkedIn URL must be less than 255 characters")
  .url("Please enter a valid LinkedIn URL")
  .optional()
  .transform(val => val?.trim() || undefined)
  .refine(val => !val || val.includes('linkedin.com'), "Must be a valid LinkedIn URL")

// Social media validation (optional)
const socialMediaSchema = z.string()
  .max(255, "Social media must be less than 255 characters")
  .optional()
  .transform(val => val?.trim() || undefined)

// Notes validation (optional)
const notesSchema = z.string()
  .max(1000, "Notes must be less than 1000 characters")
  .optional()
  .transform(val => val?.trim() || undefined)

// Schema for creating a new customer
export const customerCreateSchema = z.object({
  name: customerNameSchema,
  email: emailSchema,
  phoneNumber: phoneNumberSchema,
  companyName: companyNameSchema,
  specialization: specializationSchema.optional(),
  industry: industrySchema.optional(),
  website: websiteSchema.optional(),
  linkedIn: linkedInSchema.optional(),
  socialMedia: socialMediaSchema.optional(),
  notes: notesSchema.optional()
}).refine(data => {
  // Additional validation: if company name is provided, specialization or industry should be provided
  if (data.companyName && !data.specialization && !data.industry) {
    return false;
  }
  return true;
}, {
  message: "When company name is provided, please specify either specialization or industry",
  path: ["specialization"]
})

// Schema for updating a customer (same as create)
export const customerUpdateSchema = customerCreateSchema

// Schema for customer search and filtering
export const customerSearchSchema = z.object({
  search: z.string().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10)
})

// Schema for customer ID parameter validation
export const customerIdSchema = z.object({
  id: z.coerce.number().int().positive("Invalid customer ID")
})

// Type exports for use in components
export type CustomerCreateInput = z.infer<typeof customerCreateSchema>
export type CustomerUpdateInput = z.infer<typeof customerUpdateSchema>
export type CustomerSearchInput = z.infer<typeof customerSearchSchema>
export type CustomerIdInput = z.infer<typeof customerIdSchema>

// Validation helper functions
export const getCustomerFormErrors = (error: z.ZodError) => {
  const errors: Record<string, string> = {}
  
  error.errors.forEach((err) => {
    const path = err.path.join('.')
    if (!errors[path]) {
      errors[path] = err.message
    }
  })
  
  return errors
}

// Customer validation helper
export const validateCustomerEmail = async (email: string, excludeId?: number): Promise<boolean> => {
  // This would typically check against the database
  // For now, just validate the format
  return emailSchema.safeParse(email).success
}