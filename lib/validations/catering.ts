import { z } from "zod"

// Enhanced offer name validation with comprehensive rules and detailed error messages
const offerNameSchema = z.string()
  .min(1, "Catering offer name is required and cannot be empty")
  .max(100, "Catering offer name must be less than 100 characters")
  .regex(/^[a-zA-Z0-9\s\-_&()]+$/, "Catering offer name can only contain letters, numbers, spaces, hyphens, underscores, ampersands, and parentheses")
  .transform(val => val.trim())
  .refine(val => val.length > 0, "Catering offer name cannot be empty or only spaces")
  .refine(val => !val.toLowerCase().includes('test'), "Catering offer name cannot contain the word 'test'")
  .refine(val => !/^\d+$/.test(val), "Catering offer name cannot be only numbers")
  .refine(val => val.length >= 3, "Catering offer name must be at least 3 characters long")

// Price per person validation with monetary precision for IQD and enhanced error messages
const pricePerPersonSchema = z.number()
  .min(1, "Price per person must be at least 1 IQD (Iraqi Dinar)")
  .max(999999, "Price per person cannot exceed 999,999 IQD to ensure reasonable pricing")
  .refine(val => Number.isFinite(val), "Price per person must be a valid finite number")
  .refine(val => !Number.isNaN(val), "Price per person cannot be NaN (Not a Number)")
  .refine(val => val > 0, "Price per person must be a positive amount greater than zero")
  .transform(val => Math.round(val)) // Ensure whole numbers for IQD

// Revenue sharing fixed amount validation for IQD with enhanced error messages
const shareAmountSchema = z.number()
  .min(0, "Share amount must be at least 0 IQD (cannot be negative)")
  .max(999999, "Share amount cannot exceed 999,999 IQD to ensure reasonable values")
  .refine(val => Number.isFinite(val), "Share amount must be a valid finite number")
  .refine(val => !Number.isNaN(val), "Share amount cannot be NaN (Not a Number)")
  .refine(val => val >= 0, "Share amount cannot be negative")
  .transform(val => Math.round(val)) // Ensure whole numbers for IQD

// Schema for creating a new catering offer with enhanced validation and error messages
export const cateringCreateSchema = z.object({
  offerName: offerNameSchema,
  pricePerPerson: pricePerPersonSchema,
  firstPartyShare: shareAmountSchema,
  vendorShare: shareAmountSchema
}).refine((data) => {
  // Enhanced revenue sharing validation with detailed error checking
  const total = data.firstPartyShare + data.vendorShare;
  const difference = Math.abs(total - data.pricePerPerson);
  
  // Allow for minimal floating point precision errors
  return difference < 0.01;
}, {
  message: "Revenue sharing error: First party share and vendor share must add up to exactly the price per person. Please adjust the values so they total the full price.",
  path: ["vendorShare"] // Show error on vendor share field for better UX
}).refine((data) => {
  // Additional validation to ensure shares don't exceed price
  return data.firstPartyShare <= data.pricePerPerson && data.vendorShare <= data.pricePerPerson;
}, {
  message: "Individual share amounts cannot exceed the total price per person",
  path: ["firstPartyShare"]
}).refine((data) => {
  // Ensure at least one party gets some share (prevent 0-0 split)
  return data.firstPartyShare > 0 || data.vendorShare > 0;
}, {
  message: "At least one party must receive a share of the revenue (cannot be 0-0 split)",
  path: ["firstPartyShare"]
})

// Schema for updating a catering offer (same as create)
export const cateringUpdateSchema = cateringCreateSchema

// Schema for catering search and filtering
export const cateringSearchSchema = z.object({
  search: z.string().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10)
})

// Type exports for use in components
export type CateringCreateInput = z.infer<typeof cateringCreateSchema>
export type CateringUpdateInput = z.infer<typeof cateringUpdateSchema>
export type CateringSearchInput = z.infer<typeof cateringSearchSchema>