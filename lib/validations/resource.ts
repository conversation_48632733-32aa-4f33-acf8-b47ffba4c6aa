import { z } from "zod"
import { ResourceType, SeatingStyle, StageStyle, RESOURCE_TYPE_CONFIG } from "../types"

// Resource type enums to match Prisma schema
export const ResourceTypeEnum = z.enum([
  'INDOOR_EVENT_HALL',
  'OUTDOOR_EVENT_HALL',
  'TRAINING_ROOM',
  'MEETING_ROOM',
  'DESK',
  'PRIVATE_OFFICE'
])

export const SeatingStyleEnum = z.enum(['CINEMA', 'ROUND_TABLE'])
export const StageStyleEnum = z.enum(['PODIUM', 'PANEL'])

// Enhanced name validation with comprehensive rules
const resourceNameSchema = z.string()
  .min(1, "Resource name is required")
  .max(100, "Resource name must be less than 100 characters")
  .regex(/^[a-zA-Z0-9\s\-_&()]+$/, "Name can only contain letters, numbers, spaces, and basic punctuation")
  .transform(val => val.trim())
  .refine(val => val.length > 0, "Cannot be empty or only spaces")
  .refine(val => !val.toLowerCase().includes('test'), "Name cannot contain 'test'")
  .refine(val => !/^\d+$/.test(val), "Name cannot be only numbers")

// Base price validation
const basePriceSchema = z.number()
  .min(0, "Base price must be a positive number")
  .max(9999999.99, "Base price cannot exceed 999,999.99")
  .refine(val => Number.isFinite(val), "Base price must be a valid number")

// Details validation
const detailsSchema = z.string()
  .max(500, "Details must be less than 500 characters")
  .optional()
  .transform(val => val?.trim() || undefined)

// Seating style validation
const seatingStyleSchema = SeatingStyleEnum.optional()

// Number of attendees validation
const numberOfAttendeesSchema = z.number()
  .min(1, "Number of attendees must be at least 1")
  .max(10000, "Number of attendees cannot exceed 10,000")
  .int("Number of attendees must be a whole number")
  .optional()

// Number of desks validation
const numberOfDesksSchema = z.number()
  .min(1, "Number of desks must be at least 1")
  .max(100, "Number of desks cannot exceed 100")
  .int("Number of desks must be a whole number")
  .optional()

// Number of chairs validation
const numberOfChairsSchema = z.number()
  .min(1, "Number of chairs must be at least 1")
  .max(500, "Number of chairs cannot exceed 500")
  .int("Number of chairs must be a whole number")
  .optional()

// Amenity IDs validation
const amenityIdsSchema = z.array(z.number().int().positive())
  .default([])
  .refine(ids => new Set(ids).size === ids.length, "Duplicate amenities are not allowed")

// Stage styles validation
const stageStylesSchema = z.array(StageStyleEnum)
  .default([])
  .refine(styles => new Set(styles).size === styles.length, "Duplicate stage styles are not allowed")

// Schema for creating a new resource
export const resourceCreateSchema = z.object({
  name: resourceNameSchema,
  type: ResourceTypeEnum.refine(val => val !== undefined, {
    message: "Please select a valid resource type"
  }),
  basePrice: basePriceSchema,
  details: detailsSchema,
  seatingStyle: seatingStyleSchema,
  numberOfAttendees: numberOfAttendeesSchema,
  numberOfDesks: numberOfDesksSchema,
  numberOfChairs: numberOfChairsSchema,
  amenityIds: amenityIdsSchema,
  stageStyles: stageStylesSchema
}).superRefine((data, ctx) => {
  // Type-specific validation rules
  const config = RESOURCE_TYPE_CONFIG[data.type as ResourceType]
  
  if (!config) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Invalid resource type selected",
      path: ['type']
    })
    return
  }
  
  // Check required fields for the resource type
  for (const field of config.required) {
    const fieldValue = data[field as keyof typeof data]
    if (fieldValue === undefined || fieldValue === null || fieldValue === '') {
      const fieldDisplayName = field === 'numberOfAttendees' ? 'Number of Attendees' :
                              field === 'numberOfDesks' ? 'Number of Desks' :
                              field === 'numberOfChairs' ? 'Number of Chairs' :
                              field === 'seatingStyle' ? 'Seating Style' :
                              field.charAt(0).toUpperCase() + field.slice(1)
      
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `${fieldDisplayName} is required for ${config.displayName}`,
        path: [field]
      })
    }
  }
  
  // Validate seating style is provided when it's in the fields
  if (config.fields.includes('seatingStyle') && !data.seatingStyle) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: `Seating style must be selected for ${config.displayName}`,
      path: ['seatingStyle']
    })
  }
  
  // Validate stage styles for indoor event halls
  if (data.type === 'INDOOR_EVENT_HALL') {
    if (!data.stageStyles || data.stageStyles.length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "At least one stage style must be selected for Indoor Event Hall",
        path: ['stageStyles']
      })
    }
    
    // Validate stage styles are valid
    if (data.stageStyles && data.stageStyles.length > 0) {
      const validStageStyles = ['PODIUM', 'PANEL']
      const invalidStyles = data.stageStyles.filter(style => !validStageStyles.includes(style))
      if (invalidStyles.length > 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `Invalid stage style(s): ${invalidStyles.join(', ')}. Valid options are: Podium, Panel`,
          path: ['stageStyles']
        })
      }
    }
  } else {
    // Clear stage styles for non-indoor event halls
    if (data.stageStyles && data.stageStyles.length > 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Stage styles are only applicable to Indoor Event Halls",
        path: ['stageStyles']
      })
    }
  }
  
  // Validate private office specific fields
  if (data.type === 'PRIVATE_OFFICE') {
    if (data.numberOfDesks && data.numberOfChairs) {
      if (data.numberOfChairs < data.numberOfDesks) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Number of chairs should be at least equal to number of desks for proper workspace setup",
          path: ['numberOfChairs']
        })
      }
    }
    
    // Validate reasonable ratios
    if (data.numberOfDesks && data.numberOfDesks > 20) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Private offices typically have fewer than 20 desks. Consider using a different resource type for larger spaces",
        path: ['numberOfDesks']
      })
    }
  }
  
  // Validate event hall and training room capacities
  if ((data.type === 'INDOOR_EVENT_HALL' || data.type === 'OUTDOOR_EVENT_HALL' || data.type === 'TRAINING_ROOM') && data.numberOfAttendees) {
    if (data.numberOfAttendees < 5) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `${config.displayName} should accommodate at least 5 attendees. Consider using a Meeting Room for smaller groups`,
        path: ['numberOfAttendees']
      })
    }
  }
  
  // Validate meeting room capacity
  if (data.type === 'MEETING_ROOM' && data.numberOfAttendees) {
    if (data.numberOfAttendees > 50) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Meeting rooms typically accommodate up to 50 people. Consider using an Event Hall for larger groups",
        path: ['numberOfAttendees']
      })
    }
  }
  
  // Validate seating style compatibility
  if (data.seatingStyle && data.numberOfAttendees) {
    if (data.seatingStyle === 'ROUND_TABLE' && data.numberOfAttendees > 500) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Round table seating is typically used for smaller groups (up to 500 people). Consider cinema seating for larger events",
        path: ['seatingStyle']
      })
    }
  }
  
  // Clear fields that are not applicable to the resource type
  if (!config.fields.includes('seatingStyle')) {
    data.seatingStyle = undefined
  }
  if (!config.fields.includes('numberOfAttendees')) {
    data.numberOfAttendees = undefined
  }
  if (!config.fields.includes('numberOfDesks')) {
    data.numberOfDesks = undefined
  }
  if (!config.fields.includes('numberOfChairs')) {
    data.numberOfChairs = undefined
  }
  if (data.type !== 'INDOOR_EVENT_HALL') {
    data.stageStyles = []
  }
})

// Schema for updating a resource (same validation as create)
export const resourceUpdateSchema = resourceCreateSchema

// Schema for resource search and filtering
export const resourceSearchSchema = z.object({
  search: z.string().optional(),
  type: ResourceTypeEnum.optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10)
})

// Schema for resource ID parameter validation
export const resourceIdSchema = z.object({
  id: z.coerce.number().int().positive("Invalid resource ID")
})

// Type exports for use in components
export type ResourceCreateInput = z.infer<typeof resourceCreateSchema>
export type ResourceUpdateInput = z.infer<typeof resourceUpdateSchema>
export type ResourceSearchInput = z.infer<typeof resourceSearchSchema>
export type ResourceIdInput = z.infer<typeof resourceIdSchema>
export type ResourceTypeType = z.infer<typeof ResourceTypeEnum>
export type SeatingStyleType = z.infer<typeof SeatingStyleEnum>
export type StageStyleType = z.infer<typeof StageStyleEnum>

// Validation helper functions
export const validateResourceType = (type: string): type is ResourceType => {
  return ResourceTypeEnum.safeParse(type).success
}

export const validateSeatingStyle = (style: string): style is SeatingStyle => {
  return SeatingStyleEnum.safeParse(style).success
}

export const validateStageStyle = (style: string): style is StageStyle => {
  return StageStyleEnum.safeParse(style).success
}

// Form validation helpers
export const getResourceFormErrors = (error: z.ZodError) => {
  const errors: Record<string, string> = {}
  
  error.errors.forEach((err) => {
    const path = err.path.join('.')
    if (!errors[path]) {
      errors[path] = err.message
    }
  })
  
  return errors
}

// Resource type validation helper
export const isValidResourceTypeConfiguration = (type: ResourceType, data: Partial<ResourceCreateInput>): boolean => {
  const config = RESOURCE_TYPE_CONFIG[type]
  
  // Check if all required fields are present
  for (const field of config.required) {
    if (!data[field as keyof typeof data]) {
      return false
    }
  }
  
  return true
}