export type Expense = {
  id: string;
  amount: number;
  category: string;
  description: string;
  date: Date;
};

export type ExpenseFormData = Omit<Expense, "id" | "date"> & {
  date: string;
};

export const EXPENSE_CATEGORIES = [
  "Food",
  "Transportation",
  "Housing",
  "Utilities",
  "Entertainment",
  "Healthcare",
  "Shopping",
  "Education",
  "Other",
] as const;

export type DateRange = {
  from: Date | undefined;
  to: Date | undefined;
};

// User Management Types
export type UserRole = "ADMIN" | "LOGISTICS" | "RECEIPTION";

export interface User {
  id: number;
  firstName: string;
  lastName: string;
  username: string;
  role: UserRole;
  isSuperUser: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface UserFormData {
  firstName: string;
  lastName: string;
  username: string;
  password?: string; // Only for create operations
  role: UserRole;
  isSuperUser: boolean;
}

export interface PasswordChangeData {
  password: string;
  confirmPassword: string;
}

export interface UserSearchParams {
  search?: string;
  page?: number;
  limit?: number;
  role?: UserRole;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export type UserListResponse = PaginatedResponse<User>;

// Amenity Management Types
export type AmenityIcon =
  | "PROJECTOR"
  | "WHITEBOARD"
  | "SMARTBOARD"
  | "TABLE"
  | "WIFI"
  | "AIR_CONDITIONER"
  | "TV"
  | "MICROPHONE"
  | "SPEAKER"
  | "STAGE"
  | "COFFEE_MACHINE"
  | "WATER_DISPENSER"
  | "CATERING"
  | "SECURITY"
  | "PARKING";

export interface Amenity {
  id: number;
  name: string;
  icon: AmenityIcon;
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface AmenityFormData {
  name: string;
  icon: AmenityIcon;
}

export interface AmenitySearchParams {
  search?: string;
  page?: number;
  limit?: number;
}

export type AmenityListResponse = PaginatedResponse<Amenity>;

// Resource Management Types
export type ResourceType =
  | "INDOOR_EVENT_HALL"
  | "OUTDOOR_EVENT_HALL"
  | "TRAINING_ROOM"
  | "MEETING_ROOM"
  | "DESK"
  | "PRIVATE_OFFICE";

export type SeatingStyle = "CINEMA" | "ROUND_TABLE";
export type StageStyle = "PODIUM" | "PANEL";

export interface Resource {
  id: number;
  name: string;
  type: ResourceType;
  basePrice: number;
  details?: string | null;
  seatingStyle?: SeatingStyle | null;
  numberOfAttendees?: number | null;
  numberOfDesks?: number | null;
  numberOfChairs?: number | null;
  amenities: Amenity[];
  stageStyles: ResourceStageStyle[];
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface ResourceStageStyle {
  id: number;
  resourceId: number;
  style: StageStyle;
  createdAt: Date;
  updatedAt: Date;
}

export interface ResourceFormData {
  name: string;
  type: ResourceType;
  basePrice: number;
  details?: string;
  seatingStyle?: SeatingStyle;
  numberOfAttendees?: number;
  numberOfDesks?: number;
  numberOfChairs?: number;
  amenityIds: number[];
  stageStyles: StageStyle[];
}

export interface ResourceSearchParams {
  search?: string;
  type?: ResourceType;
  page?: number;
  limit?: number;
}

export type ResourceListResponse = PaginatedResponse<Resource>;

// Resource type configuration for dynamic form fields
export interface ResourceTypeConfig {
  fields: string[];
  required: string[];
  allowMultipleStageStyles: boolean;
  displayName: string;
  description: string;
  capacityLabel?: string;
}

export const RESOURCE_TYPE_CONFIG: Record<ResourceType, ResourceTypeConfig> = {
  INDOOR_EVENT_HALL: {
    fields: ["seatingStyle", "numberOfAttendees", "stageStyles"],
    required: ["seatingStyle", "numberOfAttendees"],
    allowMultipleStageStyles: true,
    displayName: "Indoor Event Hall",
    description:
      "Large indoor space for events with configurable seating and stage options",
    capacityLabel: "attendees",
  },
  OUTDOOR_EVENT_HALL: {
    fields: ["seatingStyle", "numberOfAttendees"],
    required: ["seatingStyle", "numberOfAttendees"],
    allowMultipleStageStyles: false,
    displayName: "Outdoor Event Hall",
    description: "Open-air venue for outdoor events and gatherings",
    capacityLabel: "attendees",
  },
  TRAINING_ROOM: {
    fields: ["seatingStyle", "numberOfAttendees"],
    required: ["numberOfAttendees"],
    allowMultipleStageStyles: false,
    displayName: "Training Room",
    description: "Dedicated space for training sessions and workshops",
    capacityLabel: "attendees",
  },
  MEETING_ROOM: {
    fields: ["numberOfAttendees"],
    required: ["numberOfAttendees"],
    allowMultipleStageStyles: false,
    displayName: "Meeting Room",
    description: "Professional space for meetings and conferences",
    capacityLabel: "attendees",
  },
  DESK: {
    fields: [],
    required: [],
    allowMultipleStageStyles: false,
    displayName: "Desk",
    description: "Individual workspace for focused work",
    capacityLabel: undefined,
  },
  PRIVATE_OFFICE: {
    fields: ["numberOfDesks", "numberOfChairs"],
    required: ["numberOfDesks", "numberOfChairs"],
    allowMultipleStageStyles: false,
    displayName: "Private Office",
    description: "Enclosed office space with configurable furniture",
    capacityLabel: "desks",
  },
};

// Resource type display utilities
export const SEATING_STYLE_LABELS: Record<SeatingStyle, string> = {
  CINEMA: "Cinema Style",
  ROUND_TABLE: "Round Table",
};

export const STAGE_STYLE_LABELS: Record<StageStyle, string> = {
  PODIUM: "Podium",
  PANEL: "Panel",
};

// Helper functions for resource type management
export const getResourceTypeDisplayName = (type: ResourceType): string => {
  return RESOURCE_TYPE_CONFIG[type].displayName;
};

export const getResourceTypeDescription = (type: ResourceType): string => {
  return RESOURCE_TYPE_CONFIG[type].description;
};

export const getResourceCapacityLabel = (
  type: ResourceType
): string | undefined => {
  return RESOURCE_TYPE_CONFIG[type].capacityLabel;
};

export const getRequiredFieldsForResourceType = (
  type: ResourceType
): string[] => {
  return RESOURCE_TYPE_CONFIG[type].required;
};

export const getAvailableFieldsForResourceType = (
  type: ResourceType
): string[] => {
  return RESOURCE_TYPE_CONFIG[type].fields;
};

export const doesResourceTypeAllowMultipleStageStyles = (
  type: ResourceType
): boolean => {
  return RESOURCE_TYPE_CONFIG[type].allowMultipleStageStyles;
};

export const formatResourceCapacity = (resource: Resource): string => {
  const config = RESOURCE_TYPE_CONFIG[resource.type];

  switch (resource.type) {
    case "INDOOR_EVENT_HALL":
    case "OUTDOOR_EVENT_HALL":
    case "TRAINING_ROOM":
    case "MEETING_ROOM":
      return resource.numberOfAttendees
        ? `${resource.numberOfAttendees} ${config.capacityLabel}`
        : "N/A";
    case "PRIVATE_OFFICE":
      const desks = resource.numberOfDesks || 0;
      const chairs = resource.numberOfChairs || 0;
      return `${desks} desks, ${chairs} chairs`;
    case "DESK":
      return "1 person";
    default:
      return "N/A";
  }
};

export const formatResourcePrice = (price: number): string => {
  return `${price.toLocaleString()} IQD`;
};

// Catering Management Types
export interface Catering {
  id: number;
  offerName: string;
  pricePerPerson: number;
  firstPartyShare: number; // Fixed amount in IQD
  vendorShare: number; // Fixed amount in IQD
  bookings: CateringOnBooking[];
  lineItems: LineItem[];
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface CateringFormData {
  offerName: string;
  pricePerPerson: number;
  firstPartyShare: number;
  vendorShare: number;
}

export interface CateringOnBooking {
  id: number;
  bookingId: number;
  cateringId: number;
  quantity: number;
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface CateringSearchParams {
  search?: string;
  page?: number;
  limit?: number;
}

export interface RevenueSplit {
  totalAmount: number;
  firstPartyAmount: number;
  vendorAmount: number;
  firstPartySharePerPerson: number; // Fixed amount per person
  vendorSharePerPerson: number; // Fixed amount per person
}

export interface CateringWithBookingCount extends Catering {
  _count: {
    bookings: number;
  };
}

export type CateringListResponse = PaginatedResponse<Catering>;

// LineItem interface for catering integration (updated for invoice management)
export interface LineItem {
  id: number;
  invoiceId: number;
  description: string;
  amount: number;
  quantity: number;
  isCatering: boolean;
  catering?: Catering | null;
  cateringId?: number | null;
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

// Customer Management Types
export interface Customer {
  id: number;
  name: string;
  email: string;
  phoneNumber?: string | null;
  companyName?: string | null;
  specialization?: string | null;
  industry?: string | null;
  website?: string | null;
  linkedIn?: string | null;
  socialMedia?: string | null;
  notes?: string | null;
  bookings?: Booking[];
  _count?: {
    bookings: number;
  };
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface CustomerFormData {
  name: string;
  email: string;
  phoneNumber?: string;
  companyName?: string;
  specialization?: string;
  industry?: string;
  website?: string;
  linkedIn?: string;
  socialMedia?: string;
  notes?: string;
}

export interface CustomerSearchParams {
  search?: string;
  page?: number;
  limit?: number;
}

export type CustomerListResponse = PaginatedResponse<Customer>;

// Booking Management Types
export type BookingStatus = "PENDING" | "CONFIRMED" | "CANCELLED";

export interface Booking {
  id: number;
  customer: Customer;
  customerId: number;
  resources: Resource[];
  status: BookingStatus;
  start: Date;
  end: Date;
  invoice?: Invoice | null;
  caterings: CateringOnBooking[];
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface BookingFormData {
  customerId: number;
  resourceIds: number[];
  status: BookingStatus;
  start: Date;
  end: Date;
  caterings: {
    cateringId: number;
    quantity: number;
  }[];
}

export interface BookingSearchParams {
  search?: string;
  customerId?: number;
  status?: BookingStatus;
  startDate?: Date;
  endDate?: Date;
  page?: number;
  limit?: number;
}

export type BookingListResponse = PaginatedResponse<Booking>;

// Calendar Event Type for ilamy integration
export interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  backgroundColor?: string;
  borderColor?: string;
  textColor?: string;
  extendedProps: {
    bookingId: number;
    customerName: string;
    status: BookingStatus;
    resourceNames: string[];
  };
}

// Invoice Management Types
export type InvoiceStatus = "PENDING" | "PARTIALLY_PAID" | "PAID" | "CANCELLED";

export interface Invoice {
  id: number;
  booking: Booking;
  bookingId: number;
  lineItems: LineItem[];
  payments: Payment[];
  status: InvoiceStatus;
  total: number;
  paid: number;
  pdfUrl?: string | null;
  _count?: {
    payments: number;
  };
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface InvoiceFormData {
  bookingId: number;
  lineItems: {
    description: string;
    amount: number;
    quantity: number;
    isCatering: boolean;
    cateringId?: number;
  }[];
}

export interface InvoiceSearchParams {
  search?: string;
  status?: InvoiceStatus;
  customerId?: number;
  bookingId?: number;
  page?: number;
  limit?: number;
}

export type InvoiceListResponse = PaginatedResponse<Invoice>;

export interface LineItemFormData {
  description: string;
  amount: number;
  quantity: number;
  isCatering: boolean;
  cateringId?: number;
}

// Payment Management Types
export type PaymentMethod =
  | "CASH"
  | "CREDIT_CARD"
  | "DEBIT_CARD"
  | "BANK_TRANSFER"
  | "CHECK"
  | "ONLINE"
  | "OTHER";
export type PaymentStatus = "PENDING" | "COMPLETED" | "FAILED" | "REFUNDED";

export interface Payment {
  id: number;
  invoice: Invoice;
  invoiceId: number;
  amount: number;
  method: PaymentMethod;
  status: PaymentStatus;
  reference?: string | null;
  notes?: string | null;
  paidAt?: Date | null;
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface PaymentFormData {
  amount: number;
  method: PaymentMethod;
  reference?: string;
  notes?: string;
  paidAt?: Date;
}

export interface PaymentSearchParams {
  invoiceId?: number;
  method?: PaymentMethod;
  status?: PaymentStatus;
  page?: number;
  limit?: number;
}

export type PaymentListResponse = PaginatedResponse<Payment>;

// Payment method display utilities
export const PAYMENT_METHOD_LABELS: Record<PaymentMethod, string> = {
  CASH: "Cash",
  CREDIT_CARD: "Credit Card",
  DEBIT_CARD: "Debit Card",
  BANK_TRANSFER: "Bank Transfer",
  CHECK: "Check",
  ONLINE: "Online Payment",
  OTHER: "Other",
};

export const PAYMENT_STATUS_LABELS: Record<PaymentStatus, string> = {
  PENDING: "Pending",
  COMPLETED: "Completed",
  FAILED: "Failed",
  REFUNDED: "Refunded",
};

// Booking status display utilities
export const BOOKING_STATUS_LABELS: Record<BookingStatus, string> = {
  PENDING: "Pending",
  CONFIRMED: "Confirmed",
  CANCELLED: "Cancelled",
};

// Invoice status display utilities
export const INVOICE_STATUS_LABELS: Record<InvoiceStatus, string> = {
  PENDING: "Pending",
  PARTIALLY_PAID: "Partially Paid",
  PAID: "Paid",
  CANCELLED: "Cancelled",
};

// Helper functions for financial calculations
export const calculateInvoiceBalance = (invoice: Invoice): number => {
  return Number(invoice.total) - Number(invoice.paid);
};

export const calculateInvoiceStatus = (
  total: number,
  paid: number
): InvoiceStatus => {
  if (paid === 0) return "PENDING";
  if (paid >= total) return "PAID";
  return "PARTIALLY_PAID";
};

export const formatCurrency = (amount: number): string => {
  if (isNaN(amount) || !isFinite(amount)) {
    return "0 IQD";
  }
  return `${amount.toLocaleString()} IQD`;
};

export const formatBookingDuration = (start: Date, end: Date): string => {
  const duration = end.getTime() - start.getTime();
  const hours = Math.floor(duration / (1000 * 60 * 60));
  const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));

  if (hours === 0) return `${minutes}m`;
  if (minutes === 0) return `${hours}h`;
  return `${hours}h ${minutes}m`;
};

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface ApiError {
  message: string;
  code?: string;
  details?: Record<string, string[]>;
}
