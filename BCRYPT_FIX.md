# Fix for bcrypt Module Error

The error you're seeing is a common issue with the `bcrypt` package where the native bindings aren't properly compiled for your system.

## Solution Options:

### Option 1: Reinstall bcrypt (Recommended)

```bash
# Remove bcrypt and reinstall
pnpm remove bcrypt
pnpm remove @types/bcrypt
pnpm add bcrypt@5.1.1
pnpm add -D @types/bcrypt

# If that doesn't work, try rebuilding
pnpm rebuild bcrypt
```

### Option 2: Clear node_modules and reinstall

```bash
# Remove node_modules and pnpm-lock.yaml
rm -rf node_modules
rm pnpm-lock.yaml

# Reinstall all dependencies
pnpm install
```

### Option 3: Use bcryptjs instead (Alternative)

If bcrypt continues to cause issues, you can switch to bcryptjs which is a pure JavaScript implementation:

```bash
pnpm remove bcrypt @types/bcrypt
pnpm add bcryptjs
pnpm add -D @types/bcryptjs
```

Then update `lib/utils/password.ts` to use bcryptjs instead of bcrypt:

```typescript
import bcrypt from "bcryptjs"; // Change this line
```

### Option 4: Platform-specific fix for macOS

If you're on macOS and have issues with native compilation:

```bash
# Install Xcode command line tools if not already installed
xcode-select --install

# Try installing with specific Python version
pnpm add bcrypt --python=python3
```

## Current Workaround Applied

I've temporarily modified the API routes to use dynamic imports for the password utility, which should allow the GET /api/users endpoint to work without bcrypt. However, creating users and changing passwords will still require bcrypt to be properly installed.

Try Option 1 first, and if that doesn't work, try the other options in order.
