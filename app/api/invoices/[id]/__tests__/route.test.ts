import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { NextRequest } from 'next/server'
import { GET, PUT, DELETE } from '../route'

// Mock the database
vi.mock('@/lib/db', () => ({
  prisma: {
    invoice: {
      findUnique: vi.fn(),
      update: vi.fn(),
      delete: vi.fn()
    },
    lineItem: {
      deleteMany: vi.fn(),
      createMany: vi.fn()
    },
    $transaction: vi.fn()
  }
}))

// Mock the validation functions
vi.mock('@/lib/validations/invoice', () => ({
  invoiceUpdateSchema: {
    parse: vi.fn()
  }
}))

import { prisma } from '@/lib/db'
import { invoiceUpdateSchema } from '@/lib/validations/invoice'

describe('/api/invoices/[id]', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('GET /api/invoices/[id]', () => {
    const mockInvoice = {
      id: 1,
      booking: {
        id: 1,
        customer: {
          id: 1,
          name: 'John Doe',
          email: '<EMAIL>',
          phoneNumber: '+1234567890',
          companyName: 'Acme Corp'
        },
        resources: [
          {
            id: 1,
            name: 'Conference Room A',
            type: 'MEETING_ROOM',
            basePrice: 50000
          }
        ],
        status: 'CONFIRMED',
        start: '2024-01-15T09:00:00.000Z',
        end: '2024-01-15T17:00:00.000Z',
        caterings: [
          {
            id: 1,
            quantity: 10,
            catering: {
              id: 1,
              offerName: 'Lunch Package',
              pricePerPerson: 25000
            }
          }
        ]
      },
      bookingId: 1,
      lineItems: [
        {
          id: 1,
          description: 'Room rental',
          amount: 50000,
          qty: 1,
          isCatering: false,
          catering: null,
          cateringId: null
        },
        {
          id: 2,
          description: 'Lunch catering for 10 people',
          amount: 250000,
          qty: 1,
          isCatering: true,
          catering: {
            id: 1,
            offerName: 'Lunch Package',
            pricePerPerson: 25000,
            firstPartyShare: 20000,
            vendorShare: 5000
          },
          cateringId: 1
        }
      ],
      payments: [
        {
          id: 1,
          amount: 150000,
          method: 'CASH',
          status: 'COMPLETED',
          reference: 'PAY-001',
          notes: 'Partial payment',
          paidAt: '2024-01-02T10:00:00.000Z',
          createdAt: '2024-01-02T10:00:00.000Z'
        }
      ],
      status: 'PARTIALLY_PAID',
      total: 300000,
      paid: 150000,
      pdfUrl: null,
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-02T10:00:00.000Z',
      createdById: null,
      updatedById: null
    }

    it('should return invoice details successfully', async () => {
      vi.mocked(prisma.invoice.findUnique).mockResolvedValue(mockInvoice)

      const response = await GET(
        new NextRequest('http://localhost:3000/api/invoices/1'),
        { params: Promise.resolve({ id: '1' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toEqual(mockInvoice)
      expect(prisma.invoice.findUnique).toHaveBeenCalledWith({
        where: { id: 1 },
        select: expect.any(Object)
      })
    })

    it('should return 400 for invalid invoice ID', async () => {
      const response = await GET(
        new NextRequest('http://localhost:3000/api/invoices/invalid'),
        { params: Promise.resolve({ id: 'invalid' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid invoice ID')
    })

    it('should return 404 for non-existent invoice', async () => {
      vi.mocked(prisma.invoice.findUnique).mockResolvedValue(null)

      const response = await GET(
        new NextRequest('http://localhost:3000/api/invoices/999'),
        { params: Promise.resolve({ id: '999' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invoice not found')
    })

    it('should handle database errors', async () => {
      vi.mocked(prisma.invoice.findUnique).mockRejectedValue(new Error('Database error'))

      const response = await GET(
        new NextRequest('http://localhost:3000/api/invoices/1'),
        { params: Promise.resolve({ id: '1' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Internal server error')
    })
  })

  describe('PUT /api/invoices/[id]', () => {
    const mockExistingInvoice = {
      id: 1,
      lineItems: [],
      payments: [
        { status: 'COMPLETED', amount: 50000 }
      ]
    }

    const mockUpdatedInvoice = {
      id: 1,
      booking: {
        id: 1,
        customer: {
          id: 1,
          name: 'John Doe',
          email: '<EMAIL>',
          phoneNumber: '+1234567890',
          companyName: 'Acme Corp'
        },
        resources: [
          {
            id: 1,
            name: 'Conference Room A',
            type: 'MEETING_ROOM',
            basePrice: 50000
          }
        ],
        status: 'CONFIRMED',
        start: '2024-01-15T09:00:00.000Z',
        end: '2024-01-15T17:00:00.000Z'
      },
      bookingId: 1,
      lineItems: [
        {
          id: 1,
          description: 'Updated room rental',
          amount: 75000,
          qty: 1,
          isCatering: false,
          catering: null,
          cateringId: null
        }
      ],
      payments: [
        {
          id: 1,
          amount: 50000,
          method: 'CASH',
          status: 'COMPLETED',
          reference: 'PAY-001',
          notes: 'Payment received',
          paidAt: '2024-01-02T10:00:00.000Z',
          createdAt: '2024-01-02T10:00:00.000Z'
        }
      ],
      status: 'PARTIALLY_PAID',
      total: 75000,
      paid: 50000,
      pdfUrl: null,
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-02T10:00:00.000Z',
      createdById: null,
      updatedById: null
    }

    const validUpdateData = {
      bookingId: 1,
      lineItems: [
        {
          description: 'Updated room rental',
          amount: 75000,
          quantity: 1,
          isCatering: false
        }
      ]
    }

    it('should update invoice successfully', async () => {
      vi.mocked(invoiceUpdateSchema.parse).mockReturnValue(validUpdateData)
      vi.mocked(prisma.invoice.findUnique).mockResolvedValue(mockExistingInvoice)
      vi.mocked(prisma.$transaction).mockResolvedValue(mockUpdatedInvoice)

      const response = await PUT(
        new NextRequest('http://localhost:3000/api/invoices/1', {
          method: 'PUT',
          body: JSON.stringify(validUpdateData)
        }),
        { params: Promise.resolve({ id: '1' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toEqual(mockUpdatedInvoice)
    })

    it('should return 400 for invalid invoice ID', async () => {
      const response = await PUT(
        new NextRequest('http://localhost:3000/api/invoices/invalid', {
          method: 'PUT',
          body: JSON.stringify(validUpdateData)
        }),
        { params: Promise.resolve({ id: 'invalid' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid invoice ID')
    })

    it('should return 404 for non-existent invoice', async () => {
      vi.mocked(invoiceUpdateSchema.parse).mockReturnValue(validUpdateData)
      vi.mocked(prisma.invoice.findUnique).mockResolvedValue(null)

      const response = await PUT(
        new NextRequest('http://localhost:3000/api/invoices/999', {
          method: 'PUT',
          body: JSON.stringify(validUpdateData)
        }),
        { params: Promise.resolve({ id: '999' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invoice not found')
    })

    it('should handle validation errors', async () => {
      const mockError = {
        name: 'ZodError',
        errors: [{ path: ['bookingId'], message: 'Booking ID is required' }]
      }
      
      vi.mocked(invoiceUpdateSchema.parse).mockImplementation(() => {
        throw mockError
      })

      const response = await PUT(
        new NextRequest('http://localhost:3000/api/invoices/1', {
          method: 'PUT',
          body: JSON.stringify({ invalid: 'data' })
        }),
        { params: Promise.resolve({ id: '1' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid input data')
    })
  })

  describe('DELETE /api/invoices/[id]', () => {
    const mockInvoiceWithoutPayments = {
      id: 1,
      payments: [],
      lineItems: [{ id: 1 }]
    }

    const mockInvoiceWithPayments = {
      id: 1,
      payments: [{ id: 1, amount: 50000 }],
      lineItems: [{ id: 1 }]
    }

    it('should delete invoice successfully when no payments exist', async () => {
      vi.mocked(prisma.invoice.findUnique).mockResolvedValue(mockInvoiceWithoutPayments)
      vi.mocked(prisma.$transaction).mockResolvedValue(undefined)

      const response = await DELETE(
        new NextRequest('http://localhost:3000/api/invoices/1', { method: 'DELETE' }),
        { params: Promise.resolve({ id: '1' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.message).toBe('Invoice deleted successfully')
    })

    it('should return 400 for invalid invoice ID', async () => {
      const response = await DELETE(
        new NextRequest('http://localhost:3000/api/invoices/invalid', { method: 'DELETE' }),
        { params: Promise.resolve({ id: 'invalid' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid invoice ID')
    })

    it('should return 404 for non-existent invoice', async () => {
      vi.mocked(prisma.invoice.findUnique).mockResolvedValue(null)

      const response = await DELETE(
        new NextRequest('http://localhost:3000/api/invoices/999', { method: 'DELETE' }),
        { params: Promise.resolve({ id: '999' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invoice not found')
    })

    it('should return 409 when invoice has payments', async () => {
      vi.mocked(prisma.invoice.findUnique).mockResolvedValue(mockInvoiceWithPayments)

      const response = await DELETE(
        new NextRequest('http://localhost:3000/api/invoices/1', { method: 'DELETE' }),
        { params: Promise.resolve({ id: '1' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(409)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Cannot delete invoice: invoice has payments')
    })

    it('should handle database errors', async () => {
      vi.mocked(prisma.invoice.findUnique).mockRejectedValue(new Error('Database error'))

      const response = await DELETE(
        new NextRequest('http://localhost:3000/api/invoices/1', { method: 'DELETE' }),
        { params: Promise.resolve({ id: '1' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Internal server error')
    })
  })
})