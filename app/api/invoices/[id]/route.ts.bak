import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { invoiceUpdateSchema } from '@/lib/validations/invoice';
import { Prisma } from '@prisma/client';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const invoiceId = parseInt((await params).id);
    
    if (isNaN(invoiceId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid invoice ID',
          message: 'Invoice ID must be a valid number'
        },
        { status: 400 }
      );
    }

    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      select: {
        id: true,
        booking: {
          select: {
            id: true,
            customer: {
              select: {
                id: true,
                name: true,
                email: true,
                phoneNumber: true,
                companyName: true
              }
            },
            resources: {
              select: {
                id: true,
                name: true,
                type: true,
                basePrice: true
              }
            },
            status: true,
            start: true,
            end: true,
            caterings: {
              select: {
                id: true,
                quantity: true,
                catering: {
                  select: {
                    id: true,
                    offerName: true,
                    pricePerPerson: true
                  }
                }
              }
            }
          }
        },
        bookingId: true,
        lineItems: {
          select: {
            id: true,
            description: true,
            amount: true,
            qty: true,
            isCatering: true,
            catering: {
              select: {
                id: true,
                offerName: true,
                pricePerPerson: true,
                firstPartyShare: true,
                vendorShare: true
              }
            },
            cateringId: true
          }
        },
        payments: {
          select: {
            id: true,
            amount: true,
            method: true,
            status: true,
            reference: true,
            notes: true,
            paidAt: true,
            createdAt: true
          }
        },
        status: true,
        total: true,
        paid: true,
        pdfUrl: true,
        createdAt: true,
        updatedAt: true,
        createdById: true,
        updatedById: true
      }
    });

    if (!invoice) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invoice not found',
          message: 'The requested invoice does not exist'
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: invoice
    });
  } catch (error) {
    console.error('Error fetching invoice:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `GET /api/invoices/${(await params).id}`,
      invoiceId: (await params).id
    });

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching the invoice. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  let body: any = null;
  
  try {
    const invoiceId = parseInt((await params).id);
    
    if (isNaN(invoiceId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid invoice ID',
          message: 'Invoice ID must be a valid number'
        },
        { status: 400 }
      );
    }

    body = await request.json();
    
    // Validate input data
    const validatedData = invoiceUpdateSchema.parse(body);

    // Check if invoice exists
    const existingInvoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: {
        lineItems: true,
        payments: true
      }
    });

    if (!existingInvoice) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invoice not found',
          message: 'The requested invoice does not exist'
        },
        { status: 404 }
      );
    }

    // Calculate new total from line items
    const newTotal = validatedData.lineItems.reduce((sum, item) => {
      return sum + (item.amount * item.quantity);
    }, 0);

    // Calculate current paid amount
    const currentPaid = existingInvoice.payments
      .filter(payment => payment.status === 'COMPLETED')
      .reduce((sum, payment) => sum + Number(payment.amount), 0);

    // Determine new status based on payments
    let newStatus = validatedData.status;
    if (!newStatus) {
      if (currentPaid === 0) {
        newStatus = 'PENDING';
      } else if (currentPaid >= newTotal) {
        newStatus = 'PAID';
      } else {
        newStatus = 'PARTIALLY_PAID';
      }
    }

    // Update the invoice with transaction to handle line items
    const updatedInvoice = await prisma.$transaction(async (tx) => {
      // Delete existing line items
      await tx.lineItem.deleteMany({
        where: { invoiceId: invoiceId }
      });

      // Create new line items
      await tx.lineItem.createMany({
        data: validatedData.lineItems.map(item => ({
          invoiceId: invoiceId,
          description: item.description,
          amount: item.amount,
          qty: item.quantity,
          isCatering: item.isCatering,
          cateringId: item.cateringId,
          // TODO: Add createdById from authenticated user session
        }))
      });

      // Update the invoice
      const invoice = await tx.invoice.update({
        where: { id: invoiceId },
        data: {
          bookingId: validatedData.bookingId,
          status: newStatus,
          total: newTotal,
          paid: currentPaid,
          updatedAt: new Date(),
          // TODO: Add updatedById from authenticated user session
        }
      });

      // Return the complete updated invoice with relations
      return await tx.invoice.findUnique({
        where: { id: invoiceId },
        select: {
          id: true,
          booking: {
            select: {
              id: true,
              customer: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  phoneNumber: true,
                  companyName: true
                }
              },
              resources: {
                select: {
                  id: true,
                  name: true,
                  type: true,
                  basePrice: true
                }
              },
              status: true,
              start: true,
              end: true
            }
          },
          bookingId: true,
          lineItems: {
            select: {
              id: true,
              description: true,
              amount: true,
              qty: true,
              isCatering: true,
              catering: {
                select: {
                  id: true,
                  offerName: true,
                  pricePerPerson: true,
                  firstPartyShare: true,
                  vendorShare: true
                }
              },
              cateringId: true
            }
          },
          payments: {
            select: {
              id: true,
              amount: true,
              method: true,
              status: true,
              reference: true,
              notes: true,
              paidAt: true,
              createdAt: true
            }
          },
          status: true,
          total: true,
          paid: true,
          pdfUrl: true,
          createdAt: true,
          updatedAt: true,
          createdById: true,
          updatedById: true
        }
      });
    });

    return NextResponse.json({
      success: true,
      data: updatedInvoice
    });
  } catch (error) {
    console.error('Error updating invoice:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `PUT /api/invoices/${(await params).id}`,
      invoiceId: (await params).id,
      requestBody: body
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid input data',
          message: 'Please check the invoice information and try again.',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2003':
          return NextResponse.json(
            { 
              success: false,
              error: 'Invalid reference',
              message: 'The selected booking or catering items do not exist. Please refresh and try again.'
            },
            { status: 400 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Invoice not found',
              message: 'The invoice you are trying to update no longer exists.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while updating the invoice. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const invoiceId = parseInt((await params).id);
    
    if (isNaN(invoiceId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid invoice ID',
          message: 'Invoice ID must be a valid number'
        },
        { status: 400 }
      );
    }

    // Check if invoice exists and has payments
    const existingInvoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: {
        payments: true,
        lineItems: true
      }
    });

    if (!existingInvoice) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invoice not found',
          message: 'The requested invoice does not exist'
        },
        { status: 404 }
      );
    }

    // Check if invoice has payments (referential integrity)
    if (existingInvoice.payments && existingInvoice.payments.length > 0) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Cannot delete invoice: invoice has payments',
          message: 'This invoice cannot be deleted because it has associated payments. Please remove all payments first.'
        },
        { status: 409 }
      );
    }

    // Delete the invoice with transaction to handle related data
    await prisma.$transaction(async (tx) => {
      // Delete line items first
      await tx.lineItem.deleteMany({
        where: { invoiceId: invoiceId }
      });

      // Finally delete the invoice
      await tx.invoice.delete({
        where: { id: invoiceId }
      });
    });

    return NextResponse.json({
      success: true,
      message: 'Invoice deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting invoice:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `DELETE /api/invoices/${(await params).id}`,
      invoiceId: (await params).id
    });
    
    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2003':
          return NextResponse.json(
            { 
              success: false,
              error: 'Cannot delete invoice: invoice has payments',
              message: 'This invoice cannot be deleted because it has associated payments. Please remove all payments first.'
            },
            { status: 409 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Invoice not found',
              message: 'The invoice you are trying to delete no longer exists.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while deleting the invoice. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}