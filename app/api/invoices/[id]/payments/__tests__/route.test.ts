import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { NextRequest } from 'next/server'
import { GET, POST } from '../route'

// Mock the database
vi.mock('@/lib/db', () => ({
  prisma: {
    invoice: {
      findUnique: vi.fn(),
      update: vi.fn()
    },
    payment: {
      count: vi.fn(),
      findMany: vi.fn(),
      create: vi.fn()
    },
    $transaction: vi.fn()
  }
}))

// Mock the validation functions
vi.mock('@/lib/validations/invoice', () => ({
  paymentCreateSchema: {
    parse: vi.fn()
  },
  paymentSearchSchema: {
    parse: vi.fn()
  },
  validatePaymentAgainstInvoice: vi.fn(),
  calculateInvoiceStatus: vi.fn()
}))

import { prisma } from '@/lib/db'
import { 
  paymentCreateSchema,
  paymentSearchSchema,
  validatePaymentAgainstInvoice,
  calculateInvoiceStatus
} from '@/lib/validations/invoice'

describe('/api/invoices/[id]/payments', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('GET /api/invoices/[id]/payments', () => {
    const mockPayments = [
      {
        id: 1,
        amount: 50000,
        method: 'CASH',
        status: 'COMPLETED',
        reference: 'PAY-001',
        notes: 'First payment',
        paidAt: '2024-01-02T10:00:00.000Z',
        createdAt: '2024-01-02T10:00:00.000Z',
        updatedAt: '2024-01-02T10:00:00.000Z',
        createdById: null,
        updatedById: null
      },
      {
        id: 2,
        amount: 25000,
        method: 'CREDIT_CARD',
        status: 'COMPLETED',
        reference: 'PAY-002',
        notes: 'Second payment',
        paidAt: '2024-01-03T14:30:00.000Z',
        createdAt: '2024-01-03T14:30:00.000Z',
        updatedAt: '2024-01-03T14:30:00.000Z',
        createdById: null,
        updatedById: null
      }
    ]

    it('should return payments with default pagination', async () => {
      const mockSearchParams = {
        invoiceId: 1,
        page: 1,
        limit: 10
      }

      vi.mocked(prisma.invoice.findUnique).mockResolvedValue({ id: 1 })
      vi.mocked(paymentSearchSchema.parse).mockReturnValue(mockSearchParams)
      vi.mocked(prisma.payment.count).mockResolvedValue(2)
      vi.mocked(prisma.payment.findMany).mockResolvedValue(mockPayments)

      const response = await GET(
        new NextRequest('http://localhost:3000/api/invoices/1/payments'),
        { params: Promise.resolve({ id: '1' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toEqual(mockPayments)
      expect(data.data.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 2,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      })
    })

    it('should return 400 for invalid invoice ID', async () => {
      const response = await GET(
        new NextRequest('http://localhost:3000/api/invoices/invalid/payments'),
        { params: Promise.resolve({ id: 'invalid' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid invoice ID')
    })

    it('should return 404 for non-existent invoice', async () => {
      vi.mocked(prisma.invoice.findUnique).mockResolvedValue(null)

      const response = await GET(
        new NextRequest('http://localhost:3000/api/invoices/999/payments'),
        { params: Promise.resolve({ id: '999' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invoice not found')
    })

    it('should handle search and filtering', async () => {
      const mockSearchParams = {
        invoiceId: 1,
        method: 'CASH',
        status: 'COMPLETED',
        page: 1,
        limit: 10
      }

      vi.mocked(prisma.invoice.findUnique).mockResolvedValue({ id: 1 })
      vi.mocked(paymentSearchSchema.parse).mockReturnValue(mockSearchParams)
      vi.mocked(prisma.payment.count).mockResolvedValue(1)
      vi.mocked(prisma.payment.findMany).mockResolvedValue([mockPayments[0]])

      const response = await GET(
        new NextRequest('http://localhost:3000/api/invoices/1/payments?method=CASH&status=COMPLETED'),
        { params: Promise.resolve({ id: '1' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toEqual([mockPayments[0]])
    })

    it('should handle database errors', async () => {
      vi.mocked(prisma.invoice.findUnique).mockRejectedValue(new Error('Database error'))

      const response = await GET(
        new NextRequest('http://localhost:3000/api/invoices/1/payments'),
        { params: Promise.resolve({ id: '1' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Internal server error')
    })
  })

  describe('POST /api/invoices/[id]/payments', () => {
    const mockInvoice = {
      id: 1,
      total: 100000,
      paid: 50000,
      status: 'PARTIALLY_PAID',
      payments: [
        { amount: 50000 }
      ]
    }

    const mockCreatedPayment = {
      id: 1,
      amount: 25000,
      method: 'CASH',
      status: 'COMPLETED',
      reference: 'PAY-001',
      notes: 'Payment received',
      paidAt: '2024-01-02T10:00:00.000Z',
      createdAt: '2024-01-02T10:00:00.000Z',
      updatedAt: '2024-01-02T10:00:00.000Z',
      createdById: null,
      updatedById: null
    }

    const validPaymentData = {
      amount: 25000,
      method: 'CASH',
      reference: 'PAY-001',
      notes: 'Payment received'
    }

    it('should create payment successfully', async () => {
      vi.mocked(paymentCreateSchema.parse).mockReturnValue(validPaymentData)
      vi.mocked(prisma.invoice.findUnique).mockResolvedValue(mockInvoice)
      vi.mocked(validatePaymentAgainstInvoice).mockReturnValue({ isValid: true })
      vi.mocked(calculateInvoiceStatus).mockReturnValue('PARTIALLY_PAID')
      vi.mocked(prisma.$transaction).mockResolvedValue(mockCreatedPayment)

      const response = await POST(
        new NextRequest('http://localhost:3000/api/invoices/1/payments', {
          method: 'POST',
          body: JSON.stringify(validPaymentData)
        }),
        { params: Promise.resolve({ id: '1' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data).toEqual(mockCreatedPayment)
    })

    it('should return 400 for invalid invoice ID', async () => {
      const response = await POST(
        new NextRequest('http://localhost:3000/api/invoices/invalid/payments', {
          method: 'POST',
          body: JSON.stringify(validPaymentData)
        }),
        { params: Promise.resolve({ id: 'invalid' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid invoice ID')
    })

    it('should return 404 for non-existent invoice', async () => {
      vi.mocked(paymentCreateSchema.parse).mockReturnValue(validPaymentData)
      vi.mocked(prisma.invoice.findUnique).mockResolvedValue(null)

      const response = await POST(
        new NextRequest('http://localhost:3000/api/invoices/999/payments', {
          method: 'POST',
          body: JSON.stringify(validPaymentData)
        }),
        { params: Promise.resolve({ id: '999' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invoice not found')
    })

    it('should reject invalid payment amount', async () => {
      vi.mocked(paymentCreateSchema.parse).mockReturnValue(validPaymentData)
      vi.mocked(prisma.invoice.findUnique).mockResolvedValue(mockInvoice)
      vi.mocked(validatePaymentAgainstInvoice).mockReturnValue({ 
        isValid: false, 
        error: 'Payment amount exceeds remaining balance' 
      })

      const response = await POST(
        new NextRequest('http://localhost:3000/api/invoices/1/payments', {
          method: 'POST',
          body: JSON.stringify({ ...validPaymentData, amount: 100000 })
        }),
        { params: Promise.resolve({ id: '1' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid payment amount')
      expect(data.message).toBe('Payment amount exceeds remaining balance')
    })

    it('should handle validation errors', async () => {
      const mockError = {
        name: 'ZodError',
        errors: [{ path: ['amount'], message: 'Amount is required' }]
      }
      
      vi.mocked(paymentCreateSchema.parse).mockImplementation(() => {
        throw mockError
      })

      const response = await POST(
        new NextRequest('http://localhost:3000/api/invoices/1/payments', {
          method: 'POST',
          body: JSON.stringify({ invalid: 'data' })
        }),
        { params: Promise.resolve({ id: '1' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid input data')
    })

    it('should update invoice status when payment makes it fully paid', async () => {
      const fullPaymentData = { ...validPaymentData, amount: 50000 }
      const fullyPaidInvoice = { ...mockInvoice, paid: 50000 }
      
      vi.mocked(paymentCreateSchema.parse).mockReturnValue(fullPaymentData)
      vi.mocked(prisma.invoice.findUnique).mockResolvedValue(fullyPaidInvoice)
      vi.mocked(validatePaymentAgainstInvoice).mockReturnValue({ isValid: true })
      vi.mocked(calculateInvoiceStatus).mockReturnValue('PAID')
      
      // Mock the transaction to capture the update data
      let invoiceUpdateData: any = null
      vi.mocked(prisma.$transaction).mockImplementation(async (callback: any) => {
        const mockTx = {
          payment: {
            create: vi.fn().mockResolvedValue(mockCreatedPayment)
          },
          invoice: {
            update: vi.fn().mockImplementation((args) => {
              invoiceUpdateData = args.data
              return Promise.resolve({})
            })
          }
        }
        return callback(mockTx)
      })

      const response = await POST(
        new NextRequest('http://localhost:3000/api/invoices/1/payments', {
          method: 'POST',
          body: JSON.stringify(fullPaymentData)
        }),
        { params: Promise.resolve({ id: '1' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      // Verify that the invoice was updated with the correct status
      expect(invoiceUpdateData).toBeTruthy()
      expect(invoiceUpdateData.status).toBe('PAID')
      expect(invoiceUpdateData.paid).toBe(100000) // 50000 (existing) + 50000 (new payment)
    })

    it('should handle database errors', async () => {
      vi.mocked(paymentCreateSchema.parse).mockReturnValue(validPaymentData)
      vi.mocked(prisma.invoice.findUnique).mockRejectedValue(new Error('Database error'))

      const response = await POST(
        new NextRequest('http://localhost:3000/api/invoices/1/payments', {
          method: 'POST',
          body: JSON.stringify(validPaymentData)
        }),
        { params: Promise.resolve({ id: '1' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Internal server error')
    })
  })
})