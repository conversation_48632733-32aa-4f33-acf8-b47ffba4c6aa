import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { calculateInvoiceStatus } from '@/lib/validations/invoice';
import { Prisma } from '@prisma/client';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const invoiceId = parseInt((await params).id);
    
    if (isNaN(invoiceId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid invoice ID',
          message: 'Invoice ID must be a valid number'
        },
        { status: 400 }
      );
    }

    // Get invoice with current payment information
    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      select: {
        id: true,
        total: true,
        paid: true,
        status: true,
        payments: {
          where: {
            status: 'COMPLETED'
          },
          select: {
            amount: true
          }
        },
        booking: {
          select: {
            customer: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    if (!invoice) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invoice not found',
          message: 'The requested invoice does not exist'
        },
        { status: 404 }
      );
    }

    // Calculate actual paid amount from completed payments
    const actualPaidAmount = invoice.payments.reduce((sum, payment) => {
      return sum + Number(payment.amount);
    }, 0);

    // Calculate correct status based on actual payments
    const correctStatus = calculateInvoiceStatus(Number(invoice.total), actualPaidAmount);

    // Check if recalculation is needed
    const needsUpdate = Number(invoice.paid) !== actualPaidAmount || invoice.status !== correctStatus;

    if (!needsUpdate) {
      // Return current invoice data if no update needed
      const updatedInvoice = await prisma.invoice.findUnique({
        where: { id: invoiceId },
        select: {
          id: true,
          bookingId: true,
          status: true,
          total: true,
          paid: true,
          pdfUrl: true,
          createdAt: true,
          updatedAt: true,
          createdById: true,
          updatedById: true,
          booking: {
            select: {
              id: true,
              start: true,
              end: true,
              status: true,
              customer: {
                select: {
                  id: true,
                  name: true,
                  email: true
                }
              },
              resources: {
                select: {
                  id: true,
                  name: true,
                  type: true,
                  basePrice: true
                }
              },
              caterings: {
                select: {
                  id: true,
                  quantity: true,
                  catering: {
                    select: {
                      id: true,
                      offerName: true,
                      pricePerPerson: true
                    }
                  }
                }
              }
            }
          },
          lineItems: {
            select: {
              id: true,
              description: true,
              amount: true,
              quantity: true,
              isCatering: true,
              cateringId: true,
              catering: {
                select: {
                  id: true,
                  offerName: true,
                  pricePerPerson: true
                }
              }
            }
          },
          payments: {
            select: {
              id: true,
              amount: true,
              method: true,
              status: true,
              reference: true,
              notes: true,
              paidAt: true,
              createdAt: true,
              updatedAt: true
            },
            orderBy: {
              createdAt: 'desc'
            }
          }
        }
      });

      return NextResponse.json({
        success: true,
        data: updatedInvoice,
        message: 'Invoice status is already up to date'
      });
    }

    // Update invoice with recalculated values
    const updatedInvoice = await prisma.invoice.update({
      where: { id: invoiceId },
      data: {
        paid: actualPaidAmount,
        status: correctStatus,
        updatedAt: new Date(),
        // TODO: Add updatedById from authenticated user session
      },
      select: {
        id: true,
        bookingId: true,
        status: true,
        total: true,
        paid: true,
        pdfUrl: true,
        createdAt: true,
        updatedAt: true,
        createdById: true,
        updatedById: true,
        booking: {
          select: {
            id: true,
            start: true,
            end: true,
            status: true,
            customer: {
              select: {
                id: true,
                name: true,
                email: true
              }
            },
            resources: {
              select: {
                id: true,
                name: true,
                type: true,
                basePrice: true
              }
            },
            caterings: {
              select: {
                id: true,
                quantity: true,
                catering: {
                  select: {
                    id: true,
                    offerName: true,
                    pricePerPerson: true
                  }
                }
              }
            }
          }
        },
        lineItems: {
          select: {
            id: true,
            description: true,
            amount: true,
            quantity: true,
            isCatering: true,
            cateringId: true,
            catering: {
              select: {
                id: true,
                offerName: true,
                pricePerPerson: true
              }
            }
          }
        },
        payments: {
          select: {
            id: true,
            amount: true,
            method: true,
            status: true,
            reference: true,
            notes: true,
            paidAt: true,
            createdAt: true,
            updatedAt: true
          },
          orderBy: {
            createdAt: 'desc'
          }
        }
      }
    });

    // Log the recalculation for audit purposes
    console.log('Invoice status recalculated:', {
      invoiceId,
      customerName: invoice.booking.customer.name,
      previousPaid: Number(invoice.paid),
      newPaid: actualPaidAmount,
      previousStatus: invoice.status,
      newStatus: correctStatus,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json({
      success: true,
      data: updatedInvoice,
      message: `Invoice status updated from ${invoice.status} to ${correctStatus}. Paid amount corrected from ${Number(invoice.paid)} to ${actualPaidAmount} IQD.`
    });
  } catch (error) {
    console.error('Error recalculating invoice status:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `POST /api/invoices/${(await params).id}/recalculate`,
      invoiceId: (await params).id
    });

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Invoice not found',
              message: 'The invoice you are trying to recalculate no longer exists.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while recalculating the invoice status. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}