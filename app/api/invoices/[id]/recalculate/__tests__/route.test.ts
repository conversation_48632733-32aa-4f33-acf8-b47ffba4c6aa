import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { NextRequest } from 'next/server'
import { POST } from '../route'

// Mock the database
vi.mock('@/lib/db', () => ({
  prisma: {
    invoice: {
      findUnique: vi.fn(),
      update: vi.fn()
    }
  }
}))

// Mock the validation functions
vi.mock('@/lib/validations/invoice', () => ({
  calculateInvoiceStatus: vi.fn()
}))

import { prisma } from '@/lib/db'
import { calculateInvoiceStatus } from '@/lib/validations/invoice'

describe('/api/invoices/[id]/recalculate', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('POST /api/invoices/[id]/recalculate', () => {
    const mockInvoice = {
      id: 1,
      total: 100000,
      paid: 50000,
      status: 'PARTIALLY_PAID',
      payments: [
        { amount: 30000 },
        { amount: 45000 }
      ],
      booking: {
        customer: {
          name: '<PERSON>'
        }
      }
    }

    const mockUpdatedInvoice = {
      id: 1,
      bookingId: 1,
      status: 'PARTIALLY_PAID',
      total: 100000,
      paid: 75000,
      pdfUrl: null,
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-02T00:00:00.000Z',
      createdById: null,
      updatedById: null,
      booking: {
        id: 1,
        start: '2024-01-15T10:00:00.000Z',
        end: '2024-01-15T18:00:00.000Z',
        status: 'CONFIRMED',
        customer: {
          id: 1,
          name: 'John Doe',
          email: '<EMAIL>'
        },
        resources: [],
        caterings: []
      },
      lineItems: [],
      payments: []
    }

    it('should recalculate invoice status successfully', async () => {
      vi.mocked(prisma.invoice.findUnique).mockResolvedValue(mockInvoice)
      vi.mocked(calculateInvoiceStatus).mockReturnValue('PARTIALLY_PAID')
      vi.mocked(prisma.invoice.update).mockResolvedValue(mockUpdatedInvoice)

      const response = await POST(
        new NextRequest('http://localhost:3000/api/invoices/1/recalculate', {
          method: 'POST'
        }),
        { params: Promise.resolve({ id: '1' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toEqual(mockUpdatedInvoice)
      expect(data.message).toContain('Invoice status updated')
    })

    it('should return current data if no recalculation needed', async () => {
      const upToDateInvoice = {
        ...mockInvoice,
        paid: 75000, // Matches calculated amount
        payments: [
          { amount: 30000 },
          { amount: 45000 }
        ]
      }

      vi.mocked(prisma.invoice.findUnique)
        .mockResolvedValueOnce(upToDateInvoice)
        .mockResolvedValueOnce(mockUpdatedInvoice)
      vi.mocked(calculateInvoiceStatus).mockReturnValue('PARTIALLY_PAID')

      const response = await POST(
        new NextRequest('http://localhost:3000/api/invoices/1/recalculate', {
          method: 'POST'
        }),
        { params: Promise.resolve({ id: '1' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.message).toContain('already up to date')
      expect(prisma.invoice.update).not.toHaveBeenCalled()
    })

    it('should return 400 for invalid invoice ID', async () => {
      const response = await POST(
        new NextRequest('http://localhost:3000/api/invoices/invalid/recalculate', {
          method: 'POST'
        }),
        { params: Promise.resolve({ id: 'invalid' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid invoice ID')
    })

    it('should return 404 for non-existent invoice', async () => {
      vi.mocked(prisma.invoice.findUnique).mockResolvedValue(null)

      const response = await POST(
        new NextRequest('http://localhost:3000/api/invoices/999/recalculate', {
          method: 'POST'
        }),
        { params: Promise.resolve({ id: '999' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invoice not found')
    })

    it('should handle database errors', async () => {
      vi.mocked(prisma.invoice.findUnique).mockRejectedValue(new Error('Database error'))

      const response = await POST(
        new NextRequest('http://localhost:3000/api/invoices/1/recalculate', {
          method: 'POST'
        }),
        { params: Promise.resolve({ id: '1' }) }
      )
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Internal server error')
    })

    it('should correctly calculate paid amount from completed payments only', async () => {
      const invoiceWithMixedPayments = {
        ...mockInvoice,
        payments: [
          { amount: 30000 }, // COMPLETED
          { amount: 20000 }, // COMPLETED
          { amount: 15000 }  // COMPLETED
        ]
      }

      vi.mocked(prisma.invoice.findUnique).mockResolvedValue(invoiceWithMixedPayments)
      vi.mocked(calculateInvoiceStatus).mockReturnValue('PARTIALLY_PAID')
      vi.mocked(prisma.invoice.update).mockResolvedValue(mockUpdatedInvoice)

      const response = await POST(
        new NextRequest('http://localhost:3000/api/invoices/1/recalculate', {
          method: 'POST'
        }),
        { params: Promise.resolve({ id: '1' }) }
      )

      expect(response.status).toBe(200)
      expect(calculateInvoiceStatus).toHaveBeenCalledWith(100000, 65000) // 30000 + 20000 + 15000
    })
  })
})