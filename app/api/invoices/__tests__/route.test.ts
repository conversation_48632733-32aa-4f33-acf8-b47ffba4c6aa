import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { NextRequest } from 'next/server'
import { GET, POST } from '../route'

// Mock the database
vi.mock('@/lib/db', () => ({
  prisma: {
    invoice: {
      count: vi.fn(),
      findMany: vi.fn(),
      create: vi.fn(),
      findUnique: vi.fn()
    },
    booking: {
      findUnique: vi.fn()
    },
    catering: {
      findMany: vi.fn()
    },
    lineItem: {
      createMany: vi.fn()
    },
    $transaction: vi.fn()
  }
}))

// Mock the validation functions
vi.mock('@/lib/validations/invoice', () => ({
  invoiceSearchSchema: {
    parse: vi.fn()
  },
  invoiceCreateSchema: {
    parse: vi.fn()
  },
  calculateInvoiceTotal: vi.fn(),
  validateLineItemCatering: vi.fn(),
  validateLineItemTotalFormula: vi.fn()
}))

import { prisma } from '@/lib/db'
import { 
  invoiceSearchSchema, 
  invoiceCreateSchema,
  calculateInvoiceTotal,
  validateLineItemCatering,
  validateLineItemTotalFormula
} from '@/lib/validations/invoice'

describe('/api/invoices', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('GET /api/invoices', () => {
    const mockInvoices = [
      {
        id: 1,
        bookingId: 1,
        total: 100000,
        paid: 50000,
        status: 'PARTIALLY_PAID',
        pdfUrl: null,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        createdById: null,
        updatedById: null,
        booking: {
          id: 1,
          start: '2024-01-15T00:00:00.000Z',
          customer: {
            id: 1,
            name: 'John Doe',
            email: '<EMAIL>',
            companyName: 'Acme Corp'
          },
          resources: [
            {
              id: 1,
              name: 'Conference Room A',
              type: 'MEETING_ROOM',
              basePrice: 50000
            }
          ]
        },
        lineItems: [
          {
            id: 1,
            description: 'Room rental',
            amount: 100000,
            qty: 1,
            isCatering: false,
            catering: null,
            createdAt: '2024-01-01T00:00:00.000Z'
          }
        ],
        payments: [
          {
            id: 1,
            amount: 50000,
            method: 'CASH',
            status: 'COMPLETED',
            reference: 'PAY-001',
            paidAt: '2024-01-02T00:00:00.000Z',
            createdAt: '2024-01-02T00:00:00.000Z'
          }
        ]
      }
    ]

    it('should return invoices with default pagination', async () => {
      const mockSearchParams = {
        page: 1,
        limit: 10
      }

      vi.mocked(invoiceSearchSchema.parse).mockReturnValue(mockSearchParams)
      vi.mocked(prisma.invoice.count).mockResolvedValue(1)
      vi.mocked(prisma.invoice.findMany).mockResolvedValue(mockInvoices)

      const request = new NextRequest('http://localhost:3000/api/invoices')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toEqual(mockInvoices)
      expect(data.data.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      })
    })

    it('should handle search query', async () => {
      const mockSearchParams = {
        search: 'John Doe',
        page: 1,
        limit: 10
      }

      vi.mocked(invoiceSearchSchema.parse).mockReturnValue(mockSearchParams)
      vi.mocked(prisma.invoice.count).mockResolvedValue(1)
      vi.mocked(prisma.invoice.findMany).mockResolvedValue(mockInvoices)

      const request = new NextRequest('http://localhost:3000/api/invoices?search=John%20Doe')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toEqual(mockInvoices)
    })

    it('should handle validation errors', async () => {
      // Create a proper ZodError-like object
      const mockError = {
        name: 'ZodError',
        errors: [{ path: ['page'], message: 'Invalid page number' }]
      }
      
      vi.mocked(invoiceSearchSchema.parse).mockImplementation(() => {
        throw mockError
      })

      const request = new NextRequest('http://localhost:3000/api/invoices?page=invalid')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid query parameters')
    })

    it('should handle database errors', async () => {
      vi.mocked(invoiceSearchSchema.parse).mockReturnValue({ page: 1, limit: 10 })
      vi.mocked(prisma.invoice.count).mockRejectedValue(new Error('Database connection failed'))

      const request = new NextRequest('http://localhost:3000/api/invoices')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Internal server error')
    })
  })

  describe('POST /api/invoices', () => {
    const mockBooking = {
      id: 1,
      invoice: null,
      customer: {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>'
      }
    }

    const mockCreatedInvoice = {
      id: 1,
      bookingId: 1,
      total: 100000,
      paid: 0,
      status: 'PENDING',
      booking: mockBooking,
      lineItems: [
        {
          id: 1,
          description: 'Room rental',
          amount: 100000,
          qty: 1,
          isCatering: false,
          catering: null,
          createdAt: '2024-01-01T00:00:00.000Z'
        }
      ],
      payments: [],
      createdAt: '2024-01-01T00:00:00.000Z'
    }

    const validInvoiceData = {
      bookingId: 1,
      lineItems: [
        {
          description: 'Room rental',
          amount: 100000,
          quantity: 1,
          isCatering: false
        }
      ]
    }

    it('should create invoice successfully', async () => {
      vi.mocked(invoiceCreateSchema.parse).mockReturnValue(validInvoiceData)
      vi.mocked(prisma.booking.findUnique).mockResolvedValue(mockBooking)
      vi.mocked(validateLineItemTotalFormula).mockReturnValue({ isValid: true, expectedTotal: 100000 })
      vi.mocked(calculateInvoiceTotal).mockReturnValue(100000)
      vi.mocked(prisma.$transaction).mockResolvedValue(mockCreatedInvoice)

      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: JSON.stringify(validInvoiceData)
      })
      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data).toEqual(mockCreatedInvoice)
    })

    it('should reject invalid input data', async () => {
      // Create a proper ZodError-like object
      const mockError = {
        name: 'ZodError',
        errors: [{ path: ['bookingId'], message: 'Booking ID is required' }]
      }
      
      vi.mocked(invoiceCreateSchema.parse).mockImplementation(() => {
        throw mockError
      })

      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: JSON.stringify({ invalid: 'data' })
      })
      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid input data')
    })

    it('should reject booking not found', async () => {
      vi.mocked(invoiceCreateSchema.parse).mockReturnValue(validInvoiceData)
      vi.mocked(prisma.booking.findUnique).mockResolvedValue(null)
      vi.mocked(validateLineItemTotalFormula).mockReturnValue({ isValid: true, expectedTotal: 100000 })

      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: JSON.stringify(validInvoiceData)
      })
      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Booking not found')
    })

    it('should reject booking with existing invoice', async () => {
      const bookingWithInvoice = {
        ...mockBooking,
        invoice: { id: 1 }
      }
      
      vi.mocked(invoiceCreateSchema.parse).mockReturnValue(validInvoiceData)
      vi.mocked(prisma.booking.findUnique).mockResolvedValue(bookingWithInvoice)
      vi.mocked(validateLineItemTotalFormula).mockReturnValue({ isValid: true, expectedTotal: 100000 })

      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: JSON.stringify(validInvoiceData)
      })
      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(409)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invoice already exists')
    })

    it('should validate catering line items', async () => {
      const cateringInvoiceData = {
        bookingId: 1,
        lineItems: [
          {
            description: 'Lunch catering',
            amount: 25000,
            quantity: 1,
            isCatering: true,
            cateringId: 1
          }
        ]
      }

      const mockCaterings = [
        { id: 1, offerName: 'Lunch Package', pricePerPerson: 25000 }
      ]

      vi.mocked(invoiceCreateSchema.parse).mockReturnValue(cateringInvoiceData)
      vi.mocked(prisma.booking.findUnique).mockResolvedValue(mockBooking)
      vi.mocked(prisma.catering.findMany).mockResolvedValue(mockCaterings)
      vi.mocked(validateLineItemCatering).mockReturnValue({ isValid: true })
      vi.mocked(validateLineItemTotalFormula).mockReturnValue({ isValid: true, expectedTotal: 25000 })
      vi.mocked(calculateInvoiceTotal).mockReturnValue(25000)
      vi.mocked(prisma.$transaction).mockResolvedValue(mockCreatedInvoice)

      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: JSON.stringify(cateringInvoiceData)
      })
      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(validateLineItemCatering).toHaveBeenCalled()
    })

    it('should reject invalid catering line items', async () => {
      const invalidCateringData = {
        bookingId: 1,
        lineItems: [
          {
            description: 'Invalid catering',
            amount: 25000,
            quantity: 1,
            isCatering: true,
            cateringId: 999
          }
        ]
      }

      vi.mocked(invoiceCreateSchema.parse).mockReturnValue(invalidCateringData)
      vi.mocked(prisma.booking.findUnique).mockResolvedValue(mockBooking)
      vi.mocked(prisma.catering.findMany).mockResolvedValue([])
      vi.mocked(validateLineItemCatering).mockReturnValue({ 
        isValid: false, 
        error: 'Selected catering offer not found' 
      })
      vi.mocked(validateLineItemTotalFormula).mockReturnValue({ isValid: true, expectedTotal: 25000 })

      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: JSON.stringify(invalidCateringData)
      })
      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid catering line item')
    })
  })
})
   