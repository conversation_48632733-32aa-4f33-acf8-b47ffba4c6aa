import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { NextRequest } from 'next/server'
import { GET, POST } from '../route'
import { prisma } from '@/lib/db'

// Mock data for testing
const mockCustomerData = {
  name: '<PERSON>',
  email: '<EMAIL>',
  phoneNumber: '+964-************'
}

const mockResourceData = {
  name: 'Conference Room A',
  type: 'MEETING_ROOM',
  basePrice: 50000,
  capacity: 10,
  description: 'Large conference room with projector'
}

const mockCateringData = {
  offerName: 'Premium Lunch Package',
  pricePerPerson: 25000,
  firstPartyShare: 15000,
  vendorShare: 10000,
  description: 'Premium catering service'
}

describe('Invoices API Integration Tests', () => {
  let testCustomer: any
  let testResource: any
  let testCatering: any
  let testBooking: any

  // Set up test data before each test
  beforeEach(async () => {
    // Clean up any existing test data
    await prisma.invoice.deleteMany({
      where: {
        booking: {
          customer: {
            email: mockCustomerData.email
          }
        }
      }
    })
    await prisma.booking.deleteMany({
      where: {
        customer: {
          email: mockCustomerData.email
        }
      }
    })
    await prisma.customer.deleteMany({
      where: { email: mockCustomerData.email }
    })
    await prisma.resource.deleteMany({
      where: { name: mockResourceData.name }
    })
    await prisma.catering.deleteMany({
      where: { offerName: mockCateringData.offerName }
    })

    // Create test customer
    testCustomer = await prisma.customer.create({
      data: mockCustomerData
    })

    // Create test resource
    testResource = await prisma.resource.create({
      data: mockResourceData
    })

    // Create test catering
    testCatering = await prisma.catering.create({
      data: mockCateringData
    })

    // Create test booking
    const futureDate = new Date()
    futureDate.setDate(futureDate.getDate() + 7)
    const endDate = new Date(futureDate)
    endDate.setHours(futureDate.getHours() + 2)

    testBooking = await prisma.booking.create({
      data: {
        customerId: testCustomer.id,
        status: 'CONFIRMED',
        start: futureDate,
        end: endDate,
        resources: {
          connect: [{ id: testResource.id }]
        }
      }
    })
  })

  afterEach(async () => {
    // Clean up test data
    await prisma.invoice.deleteMany({
      where: {
        booking: {
          customer: {
            email: mockCustomerData.email
          }
        }
      }
    })
    await prisma.booking.deleteMany({
      where: {
        customer: {
          email: mockCustomerData.email
        }
      }
    })
    await prisma.customer.deleteMany({
      where: { email: mockCustomerData.email }
    })
    await prisma.resource.deleteMany({
      where: { name: mockResourceData.name }
    })
    await prisma.catering.deleteMany({
      where: { offerName: mockCateringData.offerName }
    })
  })

  describe('GET /api/invoices', () => {
    it('should return empty list when no invoices exist', async () => {
      const request = new NextRequest('http://localhost:3000/api/invoices')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toEqual([])
      expect(data.data.pagination.total).toBe(0)
      expect(data.data.pagination.totalPages).toBe(0)
    })

    it('should return invoices with default pagination', async () => {
      // Create test invoice
      const invoice = await prisma.invoice.create({
        data: {
          bookingId: testBooking.id,
          total: 75000,
          paid: 0,
          status: 'PENDING'
        }
      })

      await prisma.lineItem.create({
        data: {
          invoiceId: invoice.id,
          description: 'Conference Room A',
          amount: 50000,
          qty: 1,
          isCatering: false
        }
      })

      const request = new NextRequest('http://localhost:3000/api/invoices')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toHaveLength(1)
      expect(data.data.pagination.total).toBe(1)
      expect(data.data.pagination.page).toBe(1)
      expect(data.data.pagination.limit).toBe(10)
      expect(data.data.data[0].booking.customer.name).toBe(mockCustomerData.name)
      expect(data.data.data[0].lineItems).toHaveLength(1)
      expect(data.data.data[0].total).toBe(75000)
    })

    it('should search invoices by customer name', async () => {
      // Create test invoice
      const invoice = await prisma.invoice.create({
        data: {
          bookingId: testBooking.id,
          total: 50000,
          paid: 0,
          status: 'PENDING'
        }
      })

      const request = new NextRequest('http://localhost:3000/api/invoices?search=John')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toHaveLength(1)
      expect(data.data.data[0].booking.customer.name).toBe('John Doe')
    })

    it('should search invoices by invoice ID', async () => {
      // Create test invoice
      const invoice = await prisma.invoice.create({
        data: {
          bookingId: testBooking.id,
          total: 50000,
          paid: 0,
          status: 'PENDING'
        }
      })

      const request = new NextRequest(`http://localhost:3000/api/invoices?search=${invoice.id}`)
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toHaveLength(1)
      expect(data.data.data[0].id).toBe(invoice.id)
    })

    it('should filter invoices by status', async () => {
      // Create test invoices with different statuses
      const invoice1 = await prisma.invoice.create({
        data: {
          bookingId: testBooking.id,
          total: 50000,
          paid: 0,
          status: 'PENDING'
        }
      })

      // Create another booking for second invoice
      const futureDate2 = new Date()
      futureDate2.setDate(futureDate2.getDate() + 8)
      const endDate2 = new Date(futureDate2)
      endDate2.setHours(futureDate2.getHours() + 2)

      const testBooking2 = await prisma.booking.create({
        data: {
          customerId: testCustomer.id,
          status: 'CONFIRMED',
          start: futureDate2,
          end: endDate2,
          resources: {
            connect: [{ id: testResource.id }]
          }
        }
      })

      const invoice2 = await prisma.invoice.create({
        data: {
          bookingId: testBooking2.id,
          total: 75000,
          paid: 75000,
          status: 'PAID'
        }
      })

      const request = new NextRequest('http://localhost:3000/api/invoices?status=PAID')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toHaveLength(1)
      expect(data.data.data[0].status).toBe('PAID')
      expect(data.data.data[0].id).toBe(invoice2.id)
    })

    it('should filter invoices by customer ID', async () => {
      // Create test invoice
      const invoice = await prisma.invoice.create({
        data: {
          bookingId: testBooking.id,
          total: 50000,
          paid: 0,
          status: 'PENDING'
        }
      })

      const request = new NextRequest(`http://localhost:3000/api/invoices?customerId=${testCustomer.id}`)
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toHaveLength(1)
      expect(data.data.data[0].booking.customerId).toBe(testCustomer.id)
    })

    it('should filter invoices by booking ID', async () => {
      // Create test invoice
      const invoice = await prisma.invoice.create({
        data: {
          bookingId: testBooking.id,
          total: 50000,
          paid: 0,
          status: 'PENDING'
        }
      })

      const request = new NextRequest(`http://localhost:3000/api/invoices?bookingId=${testBooking.id}`)
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toHaveLength(1)
      expect(data.data.data[0].bookingId).toBe(testBooking.id)
    })

    it('should handle invalid query parameters gracefully', async () => {
      const request = new NextRequest('http://localhost:3000/api/invoices?page=invalid&limit=abc')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid query parameters')
    })

    it('should include comprehensive invoice relationships', async () => {
      // Create test invoice with line items and payments
      const invoice = await prisma.invoice.create({
        data: {
          bookingId: testBooking.id,
          total: 75000,
          paid: 25000,
          status: 'PARTIALLY_PAID'
        }
      })

      await prisma.lineItem.createMany({
        data: [
          {
            invoiceId: invoice.id,
            description: 'Conference Room A',
            amount: 50000,
            qty: 1,
            isCatering: false
          },
          {
            invoiceId: invoice.id,
            description: 'Premium Lunch Package',
            amount: 25000,
            qty: 1,
            isCatering: true,
            cateringId: testCatering.id
          }
        ]
      })

      await prisma.payment.create({
        data: {
          invoiceId: invoice.id,
          amount: 25000,
          method: 'CASH',
          status: 'COMPLETED'
        }
      })

      const request = new NextRequest('http://localhost:3000/api/invoices')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toHaveLength(1)
      
      const invoiceData = data.data.data[0]
      expect(invoiceData.booking).toBeDefined()
      expect(invoiceData.booking.customer).toBeDefined()
      expect(invoiceData.booking.resources).toBeDefined()
      expect(invoiceData.lineItems).toHaveLength(2)
      expect(invoiceData.payments).toHaveLength(1)
      expect(invoiceData.lineItems[1].catering).toBeDefined()
      expect(invoiceData.lineItems[1].catering.offerName).toBe(mockCateringData.offerName)
    })
  })

  describe('POST /api/invoices', () => {
    it('should create an invoice with valid data', async () => {
      const invoiceData = {
        bookingId: testBooking.id,
        lineItems: [
          {
            description: 'Conference Room A',
            amount: 50000,
            quantity: 1,
            isCatering: false
          },
          {
            description: 'Premium Lunch Package',
            amount: 25000,
            quantity: 10,
            isCatering: true,
            cateringId: testCatering.id
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: JSON.stringify(invoiceData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.bookingId).toBe(testBooking.id)
      expect(data.data.status).toBe('PENDING')
      expect(data.data.total).toBe(300000) // 50000 + (25000 * 10)
      expect(data.data.paid).toBe(0)
      expect(data.data.lineItems).toHaveLength(2)
      expect(data.data.lineItems[0].description).toBe('Conference Room A')
      expect(data.data.lineItems[1].description).toBe('Premium Lunch Package')
      expect(data.data.lineItems[1].catering).toBeDefined()

      // Verify invoice was actually created in database
      const createdInvoice = await prisma.invoice.findUnique({
        where: { id: data.data.id },
        include: { lineItems: true }
      })
      expect(createdInvoice).toBeTruthy()
      expect(createdInvoice?.bookingId).toBe(testBooking.id)
      expect(createdInvoice?.lineItems).toHaveLength(2)
    })

    it('should create an invoice with only regular line items', async () => {
      const invoiceData = {
        bookingId: testBooking.id,
        lineItems: [
          {
            description: 'Conference Room A',
            amount: 50000,
            quantity: 1,
            isCatering: false
          },
          {
            description: 'Audio/Visual Equipment',
            amount: 25000,
            quantity: 1,
            isCatering: false
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: JSON.stringify(invoiceData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.total).toBe(75000) // 50000 + 25000
      expect(data.data.lineItems).toHaveLength(2)
      expect(data.data.lineItems.every((item: any) => !item.isCatering)).toBe(true)
    })

    it('should reject invoice creation for non-existent booking', async () => {
      const invoiceData = {
        bookingId: 99999, // Non-existent booking
        lineItems: [
          {
            description: 'Test Item',
            amount: 50000,
            quantity: 1,
            isCatering: false
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: JSON.stringify(invoiceData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Booking not found')
    })

    it('should reject invoice creation for booking that already has an invoice', async () => {
      // Create first invoice
      await prisma.invoice.create({
        data: {
          bookingId: testBooking.id,
          total: 50000,
          paid: 0,
          status: 'PENDING'
        }
      })

      // Try to create second invoice for same booking
      const invoiceData = {
        bookingId: testBooking.id,
        lineItems: [
          {
            description: 'Test Item',
            amount: 50000,
            quantity: 1,
            isCatering: false
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: JSON.stringify(invoiceData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(409)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invoice already exists')
    })

    it('should validate catering line items', async () => {
      const invoiceData = {
        bookingId: testBooking.id,
        lineItems: [
          {
            description: 'Invalid Catering',
            amount: 30000, // Wrong price - should be 25000
            quantity: 10,
            isCatering: true,
            cateringId: testCatering.id
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: JSON.stringify(invoiceData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid catering line item')
      expect(data.message).toContain('must match the price per person')
    })

    it('should reject catering line items with non-existent catering ID', async () => {
      const invoiceData = {
        bookingId: testBooking.id,
        lineItems: [
          {
            description: 'Non-existent Catering',
            amount: 25000,
            quantity: 10,
            isCatering: true,
            cateringId: 99999 // Non-existent
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: JSON.stringify(invoiceData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid catering line item')
      expect(data.message).toContain('not found')
    })

    it('should validate line item total formula', async () => {
      const invoiceData = {
        bookingId: testBooking.id,
        lineItems: [
          {
            description: 'Invalid Item',
            amount: 0, // Invalid - must be > 0
            quantity: 1,
            isCatering: false
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: JSON.stringify(invoiceData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid line item calculation')
    })

    it('should reject invoice with empty line items', async () => {
      const invoiceData = {
        bookingId: testBooking.id,
        lineItems: [] // Empty array
      }

      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: JSON.stringify(invoiceData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid input data')
    })

    it('should reject invoice with invalid line item data', async () => {
      const invoiceData = {
        bookingId: testBooking.id,
        lineItems: [
          {
            description: '', // Empty description
            amount: 50000,
            quantity: 1,
            isCatering: false
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: JSON.stringify(invoiceData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid input data')
    })

    it('should handle malformed JSON gracefully', async () => {
      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: 'invalid json'
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Internal server error')
    })

    it('should validate required fields', async () => {
      const incompleteData = {
        // Missing bookingId
        lineItems: [
          {
            description: 'Test Item',
            amount: 50000,
            quantity: 1,
            isCatering: false
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: JSON.stringify(incompleteData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid input data')
    })

    it('should calculate correct totals for complex invoices', async () => {
      const invoiceData = {
        bookingId: testBooking.id,
        lineItems: [
          {
            description: 'Conference Room A',
            amount: 50000,
            quantity: 1,
            isCatering: false
          },
          {
            description: 'Audio Equipment',
            amount: 25000,
            quantity: 2,
            isCatering: false
          },
          {
            description: 'Premium Lunch',
            amount: 25000,
            quantity: 15,
            isCatering: true,
            cateringId: testCatering.id
          },
          {
            description: 'Coffee Service',
            amount: 25000,
            quantity: 5,
            isCatering: true,
            cateringId: testCatering.id
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: JSON.stringify(invoiceData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      // Total: 50000 + (25000*2) + (25000*15) + (25000*5) = 50000 + 50000 + 375000 + 125000 = 600000
      expect(data.data.total).toBe(600000)
      expect(data.data.lineItems).toHaveLength(4)
    })

    it('should handle transaction rollback on failure', async () => {
      // This should fail due to invalid catering validation
      const invoiceData = {
        bookingId: testBooking.id,
        lineItems: [
          {
            description: 'Valid Item',
            amount: 50000,
            quantity: 1,
            isCatering: false
          },
          {
            description: 'Invalid Catering',
            amount: 99999, // Wrong price
            quantity: 10,
            isCatering: true,
            cateringId: testCatering.id
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: JSON.stringify(invoiceData)
      })

      const response = await POST(request)
      expect(response.status).toBe(400)

      // Verify no invoice was created
      const invoiceCount = await prisma.invoice.count({
        where: { bookingId: testBooking.id }
      })
      expect(invoiceCount).toBe(0)

      // Verify no line items were created
      const lineItemCount = await prisma.lineItem.count()
      expect(lineItemCount).toBe(0)
    })
  })

  describe('Database Relationships and Constraints', () => {
    it('should maintain referential integrity with bookings and line items', async () => {
      const invoiceData = {
        bookingId: testBooking.id,
        lineItems: [
          {
            description: 'Test Item',
            amount: 50000,
            quantity: 1,
            isCatering: false
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: JSON.stringify(invoiceData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)

      // Verify relationships in database
      const invoice = await prisma.invoice.findUnique({
        where: { id: data.data.id },
        include: {
          booking: {
            include: { customer: true }
          },
          lineItems: true
        }
      })

      expect(invoice).toBeTruthy()
      expect(invoice?.booking.id).toBe(testBooking.id)
      expect(invoice?.booking.customer.id).toBe(testCustomer.id)
      expect(invoice?.lineItems).toHaveLength(1)
      expect(invoice?.lineItems[0].invoiceId).toBe(invoice.id)
    })

    it('should handle catering relationships correctly', async () => {
      const invoiceData = {
        bookingId: testBooking.id,
        lineItems: [
          {
            description: 'Premium Lunch Package',
            amount: 25000,
            quantity: 12,
            isCatering: true,
            cateringId: testCatering.id
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: JSON.stringify(invoiceData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)

      // Verify catering relationship in database
      const lineItem = await prisma.lineItem.findFirst({
        where: { invoiceId: data.data.id },
        include: { catering: true }
      })

      expect(lineItem).toBeTruthy()
      expect(lineItem?.cateringId).toBe(testCatering.id)
      expect(lineItem?.catering?.offerName).toBe(mockCateringData.offerName)
      expect(lineItem?.isCatering).toBe(true)
    })
  })

  describe('Error Handling', () => {
    it('should return consistent error response format', async () => {
      const invalidData = {
        bookingId: 'invalid', // Should be number
        lineItems: [
          {
            description: 'Test Item',
            amount: 'invalid', // Should be number
            quantity: 1,
            isCatering: false
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/invoices', {
        method: 'POST',
        body: JSON.stringify(invalidData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(data).toHaveProperty('success')
      expect(data).toHaveProperty('error')
      expect(data).toHaveProperty('message')
      expect(data.success).toBe(false)
      expect(typeof data.error).toBe('string')
      expect(typeof data.message).toBe('string')
    })

    it('should handle database connection errors gracefully', async () => {
      // This test would require mocking the database connection
      // For now, we'll test that the error handling structure is in place
      const request = new NextRequest('http://localhost:3000/api/invoices')
      const response = await GET(request)
      
      // Should not throw unhandled errors
      expect(response).toBeDefined()
      expect(response.status).toBeGreaterThanOrEqual(200)
    })
  })
})