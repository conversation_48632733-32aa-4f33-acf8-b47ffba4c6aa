import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { 
  invoiceSearchSchema, 
  invoiceCreateSchema,
  calculateInvoiceTotal,
  calculateInvoiceStatus,
  validateLineItemCatering
} from '@/lib/validations/invoice';
import { Prisma } from '@prisma/client';
import { z } from 'zod';

export async function GET(request: NextRequest) {
  console.log('GET /api/invoices - Starting request');
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse and validate query parameters
    const rawParams = {
      search: searchParams.get('search') || undefined,
      status: searchParams.get('status') || undefined,
      customerId: searchParams.get('customerId') || undefined,
      bookingId: searchParams.get('bookingId') || undefined,
      page: searchParams.get('page') || '1',
      limit: searchParams.get('limit') || '10'
    }>;

    console.log('Raw query params:', rawParams);

    const validatedParams = invoiceSearchSchema.parse(rawParams);
    console.log('Validated params:', validatedParams);

    // Build where clause for search and filtering
    const where: Prisma.InvoiceWhereInput = {};
    
    if (validatedParams.search) {
      where.OR = [
        { 
          booking: {
            customer: {
              name: { contains: validatedParams.search, mode: 'insensitive' }
            }
          }
        },
        {
          id: isNaN(Number(validatedParams.search)) ? undefined : Number(validatedParams.search)
        }
      ].filter(Boolean);
    }

    if (validatedParams.status) {
      where.status = validatedParams.status;
    }

    if (validatedParams.customerId) {
      where.booking = {
        customerId: validatedParams.customerId
      }>;
    }

    if (validatedParams.bookingId) {
      where.bookingId = validatedParams.bookingId;
    }

    console.log('Where clause:', JSON.stringify(where, null, 2));

    // Calculate pagination
    const skip = (validatedParams.page - 1) * validatedParams.limit;
    console.log('Pagination - skip:', skip, 'take:', validatedParams.limit);

    // Get total count for pagination metadata
    console.log('Getting total count...');
    const totalCount = await prisma.invoice.count({ where });
    console.log('Total count:', totalCount);

    // Fetch invoices with comprehensive relationships
    console.log('Fetching invoices...');
    const invoices = await prisma.invoice.findMany({
      where,
      include: {
        booking: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                email: true,
                companyName: true
              }
            },
            resources: {
              select: {
                id: true,
                name: true,
                type: true,
                basePrice: true
              }
            }
          }
        },
        lineItems: {
          include: {
            catering: {
              select: {
                id: true,
                offerName: true,
                pricePerPerson: true,
                firstPartyShare: true,
                vendorShare: true
              }
            }
          },
          orderBy: {
            createdAt: 'asc'
          }
        },
        payments: {
          select: {
            id: true,
            amount: true,
            method: true,
            status: true,
            reference: true,
            paidAt: true,
            createdAt: true
          },
          orderBy: {
            createdAt: 'desc'
          }
        }
      },
      orderBy: [
        { createdAt: 'desc' },
        { id: 'desc' }
      ],
      skip,
      take: validatedParams.limit
    });
    console.log('Fetched invoices:', invoices.length);

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / validatedParams.limit);
    const hasNextPage = validatedParams.page < totalPages;
    const hasPreviousPage = validatedParams.page > 1;

    return NextResponse.json({
      success: true,
      data: {
        data: invoices,
        pagination: {
          page: validatedParams.page,
          limit: validatedParams.limit,
          total: totalCount,
          totalPages,
          hasNext: hasNextPage,
          hasPrev: hasPreviousPage
        }
      }
    });
  } catch (error) {
    console.error('Error fetching invoices:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: 'GET /api/invoices'
    });
    
    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid query parameters',
          message: 'Please check your search parameters and try again.',
          details: error.errors
        },
        { status: 400 }
      );
    }

    // Handle database connection errors
    if (error instanceof Error && error.message.includes('database')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    // Handle timeout errors
    if (error instanceof Error && error.message.includes('timeout')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Request timeout',
          message: 'The request took too long to complete. Please try again.'
        },
        { status: 408 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching invoices. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  console.log('POST /api/invoices - Starting invoice creation');
  let body: any = null;
  
  try {
    body = await request.json();
    console.log('Request body:', JSON.stringify(body, null, 2));
    
    // Validate input data
    console.log('Validating input data...');
    const validatedData = invoiceCreateSchema.parse(body);
    console.log('Validation successful:', JSON.stringify(validatedData, null, 2));

    // Verify booking exists and doesn't already have an invoice
    console.log('Verifying booking...');
    const existingBooking = await prisma.booking.findUnique({
      where: { id: validatedData.bookingId },
      include: {
        invoice: true,
        customer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    if (!existingBooking) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Booking not found',
          message: 'The specified booking does not exist.'
        },
        { status: 404 }
      );
    }

    if (existingBooking.invoice) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invoice already exists',
          message: 'This booking already has an associated invoice.'
        },
        { status: 409 }
      );
    }

    // Validate catering line items if any
    console.log('Validating catering line items...');
    const cateringLineItems = validatedData.lineItems.filter(item => item.isCatering);
    if (cateringLineItems.length > 0) {
      const cateringIds = cateringLineItems
        .map(item => item.cateringId)
        .filter((id): id is number => id !== undefined);
      const availableCaterings = await prisma.catering.findMany({
        where: { id: { in: cateringIds } },
        select: {
          id: true,
          offerName: true,
          pricePerPerson: true
        }
      });

      for (const lineItem of cateringLineItems) {
        const validation = validateLineItemCatering(lineItem, availableCaterings);
        if (!validation.isValid) {
          return NextResponse.json(
            { 
              success: false,
              error: 'Invalid catering line item',
              message: validation.error
            },
            { status: 400 }
          );
        }
      }
    }

    // Calculate invoice total
    const calculatedTotal = calculateInvoiceTotal(validatedData.lineItems);
    console.log('Calculated total:', calculatedTotal);

    // Create invoice with line items in a transaction
    console.log('Creating invoice in database...');
    const invoice = await prisma.$transaction(async (tx) => {
      // Create the invoice
      const newInvoice = await tx.invoice.create({
        data: {
          bookingId: validatedData.bookingId,
          total: calculatedTotal,
          paid: 0,
          status: 'PENDING',
          // TODO: Add createdById from authenticated user session
        }
      });

      // Create line items
      const lineItemsData = validatedData.lineItems.map(item => ({
        invoiceId: newInvoice.id,
        description: item.description,
        amount: item.amount,
        qty: item.quantity,
        isCatering: item.isCatering,
        cateringId: item.cateringId || null,
        // TODO: Add createdById from authenticated user session
      }));

      await tx.lineItem.createMany({
        data: lineItemsData
      });

      // Return the complete invoice with relationships
      return await tx.invoice.findUnique({
        where: { id: newInvoice.id },
        include: {
          booking: {
            include: {
              customer: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  companyName: true
                }
              },
              resources: {
                select: {
                  id: true,
                  name: true,
                  type: true,
                  basePrice: true
                }
              }
            }
          },
          lineItems: {
            include: {
              catering: {
                select: {
                  id: true,
                  offerName: true,
                  pricePerPerson: true,
                  firstPartyShare: true,
                  vendorShare: true
                }
              }
            },
            orderBy: {
              createdAt: 'asc'
            }
          },
          payments: {
            select: {
              id: true,
              amount: true,
              method: true,
              status: true,
              reference: true,
              paidAt: true,
              createdAt: true
            },
            orderBy: {
              createdAt: 'desc'
            }
          }
        }
      });
    });

    console.log('Invoice created successfully:', JSON.stringify(invoice, null, 2));

    return NextResponse.json({
      success: true,
      data: invoice
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating invoice:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: 'POST /api/invoices',
      requestBody: body
    });
    
    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid input data',
          message: 'Please check the invoice information and try again.',
          details: error.errors
        },
        { status: 400 }
      );
    }

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2002':
          return NextResponse.json(
            { 
              success: false,
              error: 'Duplicate invoice',
              message: 'An invoice for this booking already exists.'
            },
            { status: 409 }
          );
        case 'P2003':
          return NextResponse.json(
            { 
              success: false,
              error: 'Invalid reference',
              message: 'Invalid booking or catering reference. Please check your input.'
            },
            { status: 400 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Record not found',
              message: 'Required booking or catering record not found in database.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    // Handle timeout errors
    if (error instanceof Error && error.message.includes('timeout')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Request timeout',
          message: 'The request took too long to complete. Please try again.'
        },
        { status: 408 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while creating the invoice. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}