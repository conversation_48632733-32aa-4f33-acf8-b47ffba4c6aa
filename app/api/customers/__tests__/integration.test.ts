import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { NextRequest } from 'next/server'
import { GET, POST } from '../route'
import { prisma } from '@/lib/db'

// Mock data for testing
const mockCustomerData = {
  name: '<PERSON>',
  email: '<EMAIL>',
  phoneNumber: '+964-************',
  companyName: 'Tech Solutions Inc',
  specialization: 'Software Development',
  industry: 'Technology',
  website: 'https://techsolutions.com',
  linkedIn: 'https://linkedin.com/in/johndoe',
  socialMedia: '@johndoe',
  notes: 'Important client for Q4 projects'
}

const mockCustomerData2 = {
  name: '<PERSON>',
  email: '<EMAIL>',
  phoneNumber: '+964-************',
  companyName: 'Marketing Pro',
  industry: 'Marketing'
}

describe('Customers API Integration Tests', () => {
  // Clean up database before and after each test
  beforeEach(async () => {
    await prisma.customer.deleteMany({
      where: {
        email: {
          in: [mockCustomerData.email, mockCustomerData2.email, '<EMAIL>']
        }
      }
    })
  })

  afterEach(async () => {
    await prisma.customer.deleteMany({
      where: {
        email: {
          in: [mockCustomerData.email, mockCustomerData2.email, '<EMAIL>']
        }
      }
    })
  })

  describe('GET /api/customers', () => {
    it('should return empty list when no customers exist', async () => {
      const request = new NextRequest('http://localhost:3000/api/customers')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toEqual([])
      expect(data.data.pagination.total).toBe(0)
      expect(data.data.pagination.totalPages).toBe(0)
    })

    it('should return customers with default pagination', async () => {
      // Create test customers
      await prisma.customer.createMany({
        data: [mockCustomerData, mockCustomerData2]
      })

      const request = new NextRequest('http://localhost:3000/api/customers')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toHaveLength(2)
      expect(data.data.pagination.total).toBe(2)
      expect(data.data.pagination.page).toBe(1)
      expect(data.data.pagination.limit).toBe(10)
      expect(data.data.pagination.totalPages).toBe(1)
    })

    it('should return customers with custom pagination', async () => {
      // Create test customers
      await prisma.customer.createMany({
        data: [mockCustomerData, mockCustomerData2]
      })

      const request = new NextRequest('http://localhost:3000/api/customers?page=1&limit=1')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toHaveLength(1)
      expect(data.data.pagination.total).toBe(2)
      expect(data.data.pagination.page).toBe(1)
      expect(data.data.pagination.limit).toBe(1)
      expect(data.data.pagination.totalPages).toBe(2)
      expect(data.data.pagination.hasNext).toBe(true)
      expect(data.data.pagination.hasPrev).toBe(false)
    })

    it('should search customers by name', async () => {
      // Create test customers
      await prisma.customer.createMany({
        data: [mockCustomerData, mockCustomerData2]
      })

      const request = new NextRequest('http://localhost:3000/api/customers?search=John')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toHaveLength(1)
      expect(data.data.data[0].name).toBe('John Doe')
    })

    it('should search customers by email', async () => {
      // Create test customers
      await prisma.customer.createMany({
        data: [mockCustomerData, mockCustomerData2]
      })

      const request = new NextRequest('http://localhost:3000/api/customers?search=jane.smith')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toHaveLength(1)
      expect(data.data.data[0].email).toBe('<EMAIL>')
    })

    it('should search customers by company name', async () => {
      // Create test customers
      await prisma.customer.createMany({
        data: [mockCustomerData, mockCustomerData2]
      })

      const request = new NextRequest('http://localhost:3000/api/customers?search=Marketing')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toHaveLength(1)
      expect(data.data.data[0].companyName).toBe('Marketing Pro')
    })

    it('should return empty results for non-matching search', async () => {
      // Create test customers
      await prisma.customer.createMany({
        data: [mockCustomerData, mockCustomerData2]
      })

      const request = new NextRequest('http://localhost:3000/api/customers?search=NonExistent')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toHaveLength(0)
      expect(data.data.pagination.total).toBe(0)
    })

    it('should handle invalid pagination parameters gracefully', async () => {
      const request = new NextRequest('http://localhost:3000/api/customers?page=0&limit=0')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      // Should default to page=1, limit=10
      expect(data.data.pagination.page).toBe(1)
      expect(data.data.pagination.limit).toBe(10)
    })

    it('should include booking count for each customer', async () => {
      // Create test customer
      const customer = await prisma.customer.create({
        data: mockCustomerData
      })

      const request = new NextRequest('http://localhost:3000/api/customers')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data[0]).toHaveProperty('_count')
      expect(data.data.data[0]._count).toHaveProperty('bookings')
      expect(typeof data.data.data[0]._count.bookings).toBe('number')
    })
  })

  describe('POST /api/customers', () => {
    it('should create a customer with valid data', async () => {
      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        body: JSON.stringify(mockCustomerData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.name).toBe(mockCustomerData.name)
      expect(data.data.email).toBe(mockCustomerData.email)
      expect(data.data.phoneNumber).toBe(mockCustomerData.phoneNumber)
      expect(data.data.companyName).toBe(mockCustomerData.companyName)
      expect(data.data.specialization).toBe(mockCustomerData.specialization)
      expect(data.data.industry).toBe(mockCustomerData.industry)
      expect(data.data.website).toBe(mockCustomerData.website)
      expect(data.data.linkedIn).toBe(mockCustomerData.linkedIn)
      expect(data.data.socialMedia).toBe(mockCustomerData.socialMedia)
      expect(data.data.notes).toBe(mockCustomerData.notes)
      expect(data.data.id).toBeDefined()
      expect(data.data.createdAt).toBeDefined()
      expect(data.data.updatedAt).toBeDefined()

      // Verify customer was actually created in database
      const createdCustomer = await prisma.customer.findUnique({
        where: { id: data.data.id }
      })
      expect(createdCustomer).toBeTruthy()
      expect(createdCustomer?.email).toBe(mockCustomerData.email)
    })

    it('should create a customer with minimal required data', async () => {
      const minimalData = {
        name: 'Minimal Customer',
        email: '<EMAIL>'
      }

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        body: JSON.stringify(minimalData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.name).toBe(minimalData.name)
      expect(data.data.email).toBe(minimalData.email)
      expect(data.data.phoneNumber).toBeNull()
      expect(data.data.companyName).toBeNull()
    })

    it('should reject customer creation with invalid email', async () => {
      const invalidData = {
        ...mockCustomerData,
        email: 'invalid-email'
      }

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        body: JSON.stringify(invalidData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid input data')
      expect(data.message).toContain('check the customer information')
    })

    it('should reject customer creation with missing required fields', async () => {
      const invalidData = {
        email: '<EMAIL>'
        // Missing name
      }

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        body: JSON.stringify(invalidData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid input data')
    })

    it('should reject customer creation with duplicate email', async () => {
      // Create first customer
      await prisma.customer.create({
        data: mockCustomerData
      })

      // Try to create second customer with same email
      const duplicateData = {
        ...mockCustomerData2,
        email: mockCustomerData.email // Same email
      }

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        body: JSON.stringify(duplicateData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(409)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Email already exists')
      expect(data.message).toContain('already registered')
      expect(data.field).toBe('email')
    })

    it('should reject customer with company name but no specialization or industry', async () => {
      const invalidData = {
        name: 'Test Customer',
        email: '<EMAIL>',
        companyName: 'Test Company'
        // Missing specialization and industry
      }

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        body: JSON.stringify(invalidData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid input data')
    })

    it('should accept customer with company name and specialization', async () => {
      const validData = {
        name: 'Test Customer',
        email: '<EMAIL>',
        companyName: 'Test Company',
        specialization: 'Software Development'
      }

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        body: JSON.stringify(validData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.companyName).toBe(validData.companyName)
      expect(data.data.specialization).toBe(validData.specialization)
    })

    it('should accept customer with company name and industry', async () => {
      const validData = {
        name: 'Test Customer',
        email: '<EMAIL>',
        companyName: 'Test Company',
        industry: 'Technology'
      }

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        body: JSON.stringify(validData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.companyName).toBe(validData.companyName)
      expect(data.data.industry).toBe(validData.industry)
    })

    it('should handle malformed JSON gracefully', async () => {
      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        body: 'invalid json'
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Internal server error')
    })

    it('should trim and transform input data correctly', async () => {
      const dataWithSpaces = {
        name: '  John Doe  ',
        email: '  <EMAIL>  ',
        phoneNumber: '  +964-************  ',
        companyName: '  Tech Solutions  ',
        specialization: '  Software Development  '
      }

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        body: JSON.stringify(dataWithSpaces)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.name).toBe('John Doe')
      expect(data.data.email).toBe('<EMAIL>')
      expect(data.data.phoneNumber).toBe('+964-************')
      expect(data.data.companyName).toBe('Tech Solutions')
      expect(data.data.specialization).toBe('Software Development')
    })

    it('should validate website URL format', async () => {
      const invalidWebsiteData = {
        ...mockCustomerData,
        website: 'not-a-valid-url'
      }

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        body: JSON.stringify(invalidWebsiteData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid input data')
    })

    it('should validate LinkedIn URL format', async () => {
      const invalidLinkedInData = {
        ...mockCustomerData,
        linkedIn: 'https://facebook.com/johndoe'
      }

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        body: JSON.stringify(invalidLinkedInData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid input data')
    })

    it('should validate phone number format', async () => {
      const invalidPhoneData = {
        ...mockCustomerData,
        phoneNumber: 'invalid-phone-123abc'
      }

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        body: JSON.stringify(invalidPhoneData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid input data')
    })

    it('should validate field length limits', async () => {
      const longFieldData = {
        name: 'a'.repeat(101), // Exceeds 100 character limit
        email: '<EMAIL>'
      }

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        body: JSON.stringify(longFieldData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid input data')
    })
  })

  describe('Database Relationships', () => {
    it('should maintain referential integrity with bookings', async () => {
      // Create customer
      const customer = await prisma.customer.create({
        data: mockCustomerData
      })

      // Verify customer exists and has no bookings initially
      const customerWithBookings = await prisma.customer.findUnique({
        where: { id: customer.id },
        include: {
          _count: {
            select: {
              bookings: true
            }
          }
        }
      })

      expect(customerWithBookings).toBeTruthy()
      expect(customerWithBookings?._count.bookings).toBe(0)
    })

    it('should handle concurrent customer creation attempts', async () => {
      const customerData1 = { ...mockCustomerData, email: '<EMAIL>' }
      const customerData2 = { ...mockCustomerData, email: '<EMAIL>' }

      const request1 = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        body: JSON.stringify(customerData1)
      })

      const request2 = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        body: JSON.stringify(customerData2)
      })

      // Execute both requests concurrently
      const [response1, response2] = await Promise.all([
        POST(request1),
        POST(request2)
      ])

      const data1 = await response1.json()
      const data2 = await response2.json()

      expect(response1.status).toBe(201)
      expect(response2.status).toBe(201)
      expect(data1.success).toBe(true)
      expect(data2.success).toBe(true)
      expect(data1.data.email).toBe(customerData1.email)
      expect(data2.data.email).toBe(customerData2.email)

      // Clean up
      await prisma.customer.deleteMany({
        where: {
          email: {
            in: [customerData1.email, customerData2.email]
          }
        }
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle database connection errors gracefully', async () => {
      // This test would require mocking the database connection
      // For now, we'll test that the error handling structure is in place
      const request = new NextRequest('http://localhost:3000/api/customers')
      const response = await GET(request)
      
      // Should not throw unhandled errors
      expect(response).toBeDefined()
      expect(response.status).toBeGreaterThanOrEqual(200)
    })

    it('should return consistent error response format', async () => {
      const invalidData = {
        name: '', // Invalid empty name
        email: 'invalid-email'
      }

      const request = new NextRequest('http://localhost:3000/api/customers', {
        method: 'POST',
        body: JSON.stringify(invalidData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(data).toHaveProperty('success')
      expect(data).toHaveProperty('error')
      expect(data).toHaveProperty('message')
      expect(data.success).toBe(false)
      expect(typeof data.error).toBe('string')
      expect(typeof data.message).toBe('string')
    })
  })
})