import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { NextRequest } from 'next/server'
import { GET, POST } from '../route'
import { prisma } from '@/lib/db'

// Mock data for testing
const mockCustomerData = {
  name: '<PERSON>',
  email: '<EMAIL>',
  phoneNumber: '+964-************'
}

const mockResourceData = {
  name: 'Conference Room A',
  type: 'MEETING_ROOM',
  basePrice: 50000,
  capacity: 10,
  description: 'Large conference room with projector'
}

const mockCateringData = {
  offerName: 'Premium Lunch Package',
  pricePerPerson: 25000,
  firstPartyShare: 15000,
  vendorShare: 10000,
  description: 'Premium catering service'
}

describe('Bookings API Integration Tests', () => {
  let testCustomer: any
  let testResource: any
  let testCatering: any

  // Set up test data before each test
  beforeEach(async () => {
    // Clean up any existing test data
    await prisma.booking.deleteMany({
      where: {
        customer: {
          email: mockCustomerData.email
        }
      }
    })
    await prisma.customer.deleteMany({
      where: { email: mockCustomerData.email }
    })
    await prisma.resource.deleteMany({
      where: { name: mockResourceData.name }
    })
    await prisma.catering.deleteMany({
      where: { offerName: mockCateringData.offerName }
    })

    // Create test customer
    testCustomer = await prisma.customer.create({
      data: mockCustomerData
    })

    // Create test resource
    testResource = await prisma.resource.create({
      data: mockResourceData
    })

    // Create test catering
    testCatering = await prisma.catering.create({
      data: mockCateringData
    })
  })

  afterEach(async () => {
    // Clean up test data
    await prisma.booking.deleteMany({
      where: {
        customer: {
          email: mockCustomerData.email
        }
      }
    })
    await prisma.customer.deleteMany({
      where: { email: mockCustomerData.email }
    })
    await prisma.resource.deleteMany({
      where: { name: mockResourceData.name }
    })
    await prisma.catering.deleteMany({
      where: { offerName: mockCateringData.offerName }
    })
  })

  describe('GET /api/bookings', () => {
    it('should return empty list when no bookings exist', async () => {
      const request = new NextRequest('http://localhost:3000/api/bookings')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toEqual([])
      expect(data.data.pagination.total).toBe(0)
      expect(data.data.pagination.totalPages).toBe(0)
    })

    it('should return bookings with default pagination', async () => {
      // Create test booking
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7) // 7 days from now
      const endDate = new Date(futureDate)
      endDate.setHours(futureDate.getHours() + 2) // 2 hours later

      await prisma.booking.create({
        data: {
          customerId: testCustomer.id,
          status: 'PENDING',
          start: futureDate,
          end: endDate,
          resources: {
            connect: [{ id: testResource.id }]
          }
        }
      })

      const request = new NextRequest('http://localhost:3000/api/bookings')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toHaveLength(1)
      expect(data.data.pagination.total).toBe(1)
      expect(data.data.pagination.page).toBe(1)
      expect(data.data.pagination.limit).toBe(10)
      expect(data.data.data[0].customer.name).toBe(mockCustomerData.name)
      expect(data.data.data[0].resources).toHaveLength(1)
      expect(data.data.data[0].resources[0].name).toBe(mockResourceData.name)
    })

    it('should return bookings in calendar format', async () => {
      // Create test booking
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      const endDate = new Date(futureDate)
      endDate.setHours(futureDate.getHours() + 2)

      await prisma.booking.create({
        data: {
          customerId: testCustomer.id,
          status: 'CONFIRMED',
          start: futureDate,
          end: endDate,
          resources: {
            connect: [{ id: testResource.id }]
          }
        }
      })

      const request = new NextRequest('http://localhost:3000/api/bookings?format=calendar')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(Array.isArray(data.data)).toBe(true)
      expect(data.data).toHaveLength(1)
      
      const calendarEvent = data.data[0]
      expect(calendarEvent).toHaveProperty('id')
      expect(calendarEvent).toHaveProperty('title')
      expect(calendarEvent).toHaveProperty('start')
      expect(calendarEvent).toHaveProperty('end')
      expect(calendarEvent).toHaveProperty('backgroundColor')
      expect(calendarEvent).toHaveProperty('extendedProps')
      expect(calendarEvent.extendedProps).toHaveProperty('bookingId')
      expect(calendarEvent.extendedProps).toHaveProperty('customerName')
      expect(calendarEvent.extendedProps).toHaveProperty('status')
      expect(calendarEvent.extendedProps).toHaveProperty('resourceNames')
    })

    it('should search bookings by customer name', async () => {
      // Create test booking
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      const endDate = new Date(futureDate)
      endDate.setHours(futureDate.getHours() + 2)

      await prisma.booking.create({
        data: {
          customerId: testCustomer.id,
          status: 'PENDING',
          start: futureDate,
          end: endDate,
          resources: {
            connect: [{ id: testResource.id }]
          }
        }
      })

      const request = new NextRequest('http://localhost:3000/api/bookings?search=John')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toHaveLength(1)
      expect(data.data.data[0].customer.name).toBe('John Doe')
    })

    it('should filter bookings by customer ID', async () => {
      // Create test booking
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      const endDate = new Date(futureDate)
      endDate.setHours(futureDate.getHours() + 2)

      await prisma.booking.create({
        data: {
          customerId: testCustomer.id,
          status: 'PENDING',
          start: futureDate,
          end: endDate,
          resources: {
            connect: [{ id: testResource.id }]
          }
        }
      })

      const request = new NextRequest(`http://localhost:3000/api/bookings?customerId=${testCustomer.id}`)
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toHaveLength(1)
      expect(data.data.data[0].customerId).toBe(testCustomer.id)
    })

    it('should filter bookings by status', async () => {
      // Create test bookings with different statuses
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      const endDate = new Date(futureDate)
      endDate.setHours(futureDate.getHours() + 2)

      await prisma.booking.createMany({
        data: [
          {
            customerId: testCustomer.id,
            status: 'PENDING',
            start: futureDate,
            end: endDate
          },
          {
            customerId: testCustomer.id,
            status: 'CONFIRMED',
            start: new Date(futureDate.getTime() + 24 * 60 * 60 * 1000), // Next day
            end: new Date(endDate.getTime() + 24 * 60 * 60 * 1000)
          }
        ]
      })

      const request = new NextRequest('http://localhost:3000/api/bookings?status=CONFIRMED')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toHaveLength(1)
      expect(data.data.data[0].status).toBe('CONFIRMED')
    })

    it('should filter bookings by date range', async () => {
      const today = new Date()
      const tomorrow = new Date(today)
      tomorrow.setDate(today.getDate() + 1)
      const dayAfter = new Date(today)
      dayAfter.setDate(today.getDate() + 2)
      const nextWeek = new Date(today)
      nextWeek.setDate(today.getDate() + 7)

      // Create bookings on different dates
      await prisma.booking.createMany({
        data: [
          {
            customerId: testCustomer.id,
            status: 'PENDING',
            start: tomorrow,
            end: new Date(tomorrow.getTime() + 2 * 60 * 60 * 1000)
          },
          {
            customerId: testCustomer.id,
            status: 'PENDING',
            start: nextWeek,
            end: new Date(nextWeek.getTime() + 2 * 60 * 60 * 1000)
          }
        ]
      })

      // Filter for bookings within first 3 days
      const endFilter = new Date(today)
      endFilter.setDate(today.getDate() + 3)

      const request = new NextRequest(`http://localhost:3000/api/bookings?startDate=${today.toISOString()}&endDate=${endFilter.toISOString()}`)
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toHaveLength(1) // Should only return the tomorrow booking
    })

    it('should handle invalid query parameters gracefully', async () => {
      const request = new NextRequest('http://localhost:3000/api/bookings?page=invalid&limit=abc')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid query parameters')
    })
  })

  describe('POST /api/bookings', () => {
    it('should create a booking with valid data', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      const endDate = new Date(futureDate)
      endDate.setHours(futureDate.getHours() + 2)

      const bookingData = {
        customerId: testCustomer.id,
        resourceIds: [testResource.id],
        status: 'PENDING',
        start: futureDate.toISOString(),
        end: endDate.toISOString(),
        caterings: []
      }

      const request = new NextRequest('http://localhost:3000/api/bookings', {
        method: 'POST',
        body: JSON.stringify(bookingData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.customerId).toBe(testCustomer.id)
      expect(data.data.status).toBe('PENDING')
      expect(data.data.resources).toHaveLength(1)
      expect(data.data.resources[0].id).toBe(testResource.id)
      expect(data.data.customer.name).toBe(mockCustomerData.name)
      expect(new Date(data.data.start)).toEqual(futureDate)
      expect(new Date(data.data.end)).toEqual(endDate)

      // Verify booking was actually created in database
      const createdBooking = await prisma.booking.findUnique({
        where: { id: data.data.id },
        include: { resources: true }
      })
      expect(createdBooking).toBeTruthy()
      expect(createdBooking?.customerId).toBe(testCustomer.id)
      expect(createdBooking?.resources).toHaveLength(1)
    })

    it('should create a booking with catering for confirmed status', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      const endDate = new Date(futureDate)
      endDate.setHours(futureDate.getHours() + 2)

      const bookingData = {
        customerId: testCustomer.id,
        resourceIds: [testResource.id],
        status: 'CONFIRMED',
        start: futureDate.toISOString(),
        end: endDate.toISOString(),
        caterings: [
          {
            cateringId: testCatering.id,
            quantity: 10
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/bookings', {
        method: 'POST',
        body: JSON.stringify(bookingData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.status).toBe('CONFIRMED')
      expect(data.data.caterings).toHaveLength(1)
      expect(data.data.caterings[0].quantity).toBe(10)
      expect(data.data.caterings[0].catering.offerName).toBe(mockCateringData.offerName)
    })

    it('should reject booking with non-existent customer', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      const endDate = new Date(futureDate)
      endDate.setHours(futureDate.getHours() + 2)

      const bookingData = {
        customerId: 99999, // Non-existent customer
        resourceIds: [testResource.id],
        status: 'PENDING',
        start: futureDate.toISOString(),
        end: endDate.toISOString(),
        caterings: []
      }

      const request = new NextRequest('http://localhost:3000/api/bookings', {
        method: 'POST',
        body: JSON.stringify(bookingData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Customer not found')
    })

    it('should reject booking with non-existent resources', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      const endDate = new Date(futureDate)
      endDate.setHours(futureDate.getHours() + 2)

      const bookingData = {
        customerId: testCustomer.id,
        resourceIds: [99999], // Non-existent resource
        status: 'PENDING',
        start: futureDate.toISOString(),
        end: endDate.toISOString(),
        caterings: []
      }

      const request = new NextRequest('http://localhost:3000/api/bookings', {
        method: 'POST',
        body: JSON.stringify(bookingData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Resources not found')
      expect(data.message).toContain('99999')
    })

    it('should reject booking with non-existent catering', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      const endDate = new Date(futureDate)
      endDate.setHours(futureDate.getHours() + 2)

      const bookingData = {
        customerId: testCustomer.id,
        resourceIds: [testResource.id],
        status: 'CONFIRMED',
        start: futureDate.toISOString(),
        end: endDate.toISOString(),
        caterings: [
          {
            cateringId: 99999, // Non-existent catering
            quantity: 10
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/bookings', {
        method: 'POST',
        body: JSON.stringify(bookingData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Catering items not found')
    })

    it('should detect resource conflicts', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      const endDate = new Date(futureDate)
      endDate.setHours(futureDate.getHours() + 2)

      // Create first booking
      await prisma.booking.create({
        data: {
          customerId: testCustomer.id,
          status: 'CONFIRMED',
          start: futureDate,
          end: endDate,
          resources: {
            connect: [{ id: testResource.id }]
          }
        }
      })

      // Try to create overlapping booking
      const overlappingStart = new Date(futureDate)
      overlappingStart.setHours(futureDate.getHours() + 1) // 1 hour overlap
      const overlappingEnd = new Date(overlappingStart)
      overlappingEnd.setHours(overlappingStart.getHours() + 2)

      const conflictingBookingData = {
        customerId: testCustomer.id,
        resourceIds: [testResource.id],
        status: 'PENDING',
        start: overlappingStart.toISOString(),
        end: overlappingEnd.toISOString(),
        caterings: []
      }

      const request = new NextRequest('http://localhost:3000/api/bookings', {
        method: 'POST',
        body: JSON.stringify(conflictingBookingData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(409)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Resource conflict')
      expect(data.message).toContain('already booked')
      expect(data.conflicts).toBeDefined()
      expect(Array.isArray(data.conflicts)).toBe(true)
    })

    it('should reject booking with invalid date range', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      const pastDate = new Date(futureDate)
      pastDate.setHours(futureDate.getHours() - 2) // End before start

      const bookingData = {
        customerId: testCustomer.id,
        resourceIds: [testResource.id],
        status: 'PENDING',
        start: futureDate.toISOString(),
        end: pastDate.toISOString(), // Invalid: end before start
        caterings: []
      }

      const request = new NextRequest('http://localhost:3000/api/bookings', {
        method: 'POST',
        body: JSON.stringify(bookingData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid input data')
    })

    it('should reject booking with past start date', async () => {
      const pastDate = new Date()
      pastDate.setDate(pastDate.getDate() - 1) // Yesterday
      const endDate = new Date(pastDate)
      endDate.setHours(pastDate.getHours() + 2)

      const bookingData = {
        customerId: testCustomer.id,
        resourceIds: [testResource.id],
        status: 'PENDING',
        start: pastDate.toISOString(),
        end: endDate.toISOString(),
        caterings: []
      }

      const request = new NextRequest('http://localhost:3000/api/bookings', {
        method: 'POST',
        body: JSON.stringify(bookingData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid input data')
    })

    it('should reject booking with duration less than 15 minutes', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      const endDate = new Date(futureDate)
      endDate.setMinutes(futureDate.getMinutes() + 10) // Only 10 minutes

      const bookingData = {
        customerId: testCustomer.id,
        resourceIds: [testResource.id],
        status: 'PENDING',
        start: futureDate.toISOString(),
        end: endDate.toISOString(),
        caterings: []
      }

      const request = new NextRequest('http://localhost:3000/api/bookings', {
        method: 'POST',
        body: JSON.stringify(bookingData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid input data')
    })

    it('should reject catering for non-confirmed bookings', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      const endDate = new Date(futureDate)
      endDate.setHours(futureDate.getHours() + 2)

      const bookingData = {
        customerId: testCustomer.id,
        resourceIds: [testResource.id],
        status: 'PENDING', // Not confirmed
        start: futureDate.toISOString(),
        end: endDate.toISOString(),
        caterings: [
          {
            cateringId: testCatering.id,
            quantity: 10
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/bookings', {
        method: 'POST',
        body: JSON.stringify(bookingData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid input data')
    })

    it('should handle malformed JSON gracefully', async () => {
      const request = new NextRequest('http://localhost:3000/api/bookings', {
        method: 'POST',
        body: 'invalid json'
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Internal server error')
    })

    it('should validate required fields', async () => {
      const incompleteData = {
        customerId: testCustomer.id,
        // Missing resourceIds, start, end
        status: 'PENDING',
        caterings: []
      }

      const request = new NextRequest('http://localhost:3000/api/bookings', {
        method: 'POST',
        body: JSON.stringify(incompleteData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid input data')
    })

    it('should validate resource ID array constraints', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      const endDate = new Date(futureDate)
      endDate.setHours(futureDate.getHours() + 2)

      const bookingData = {
        customerId: testCustomer.id,
        resourceIds: [], // Empty array - should be rejected
        status: 'PENDING',
        start: futureDate.toISOString(),
        end: endDate.toISOString(),
        caterings: []
      }

      const request = new NextRequest('http://localhost:3000/api/bookings', {
        method: 'POST',
        body: JSON.stringify(bookingData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid input data')
    })
  })

  describe('Database Relationships and Constraints', () => {
    it('should maintain referential integrity with customers and resources', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      const endDate = new Date(futureDate)
      endDate.setHours(futureDate.getHours() + 2)

      const booking = await prisma.booking.create({
        data: {
          customerId: testCustomer.id,
          status: 'PENDING',
          start: futureDate,
          end: endDate,
          resources: {
            connect: [{ id: testResource.id }]
          }
        },
        include: {
          customer: true,
          resources: true
        }
      })

      expect(booking.customer.id).toBe(testCustomer.id)
      expect(booking.resources).toHaveLength(1)
      expect(booking.resources[0].id).toBe(testResource.id)
    })

    it('should handle catering relationships correctly', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      const endDate = new Date(futureDate)
      endDate.setHours(futureDate.getHours() + 2)

      const bookingData = {
        customerId: testCustomer.id,
        resourceIds: [testResource.id],
        status: 'CONFIRMED',
        start: futureDate.toISOString(),
        end: endDate.toISOString(),
        caterings: [
          {
            cateringId: testCatering.id,
            quantity: 15
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/bookings', {
        method: 'POST',
        body: JSON.stringify(bookingData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)

      // Verify catering relationship in database
      const cateringOnBooking = await prisma.cateringOnBooking.findFirst({
        where: { bookingId: data.data.id },
        include: { catering: true }
      })

      expect(cateringOnBooking).toBeTruthy()
      expect(cateringOnBooking?.cateringId).toBe(testCatering.id)
      expect(cateringOnBooking?.quantity).toBe(15)
    })

    it('should handle transaction rollback on failure', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      const endDate = new Date(futureDate)
      endDate.setHours(futureDate.getHours() + 2)

      // This should fail due to non-existent catering
      const bookingData = {
        customerId: testCustomer.id,
        resourceIds: [testResource.id],
        status: 'CONFIRMED',
        start: futureDate.toISOString(),
        end: endDate.toISOString(),
        caterings: [
          {
            cateringId: 99999, // Non-existent
            quantity: 10
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/bookings', {
        method: 'POST',
        body: JSON.stringify(bookingData)
      })

      const response = await POST(request)
      expect(response.status).toBe(404)

      // Verify no booking was created
      const bookingCount = await prisma.booking.count({
        where: { customerId: testCustomer.id }
      })
      expect(bookingCount).toBe(0)
    })
  })

  describe('Error Handling', () => {
    it('should return consistent error response format', async () => {
      const invalidData = {
        customerId: 'invalid', // Should be number
        resourceIds: [testResource.id],
        status: 'PENDING',
        start: 'invalid-date',
        end: 'invalid-date',
        caterings: []
      }

      const request = new NextRequest('http://localhost:3000/api/bookings', {
        method: 'POST',
        body: JSON.stringify(invalidData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(data).toHaveProperty('success')
      expect(data).toHaveProperty('error')
      expect(data).toHaveProperty('message')
      expect(data.success).toBe(false)
      expect(typeof data.error).toBe('string')
      expect(typeof data.message).toBe('string')
    })

    it('should handle database connection errors gracefully', async () => {
      // This test would require mocking the database connection
      // For now, we'll test that the error handling structure is in place
      const request = new NextRequest('http://localhost:3000/api/bookings')
      const response = await GET(request)
      
      // Should not throw unhandled errors
      expect(response).toBeDefined()
      expect(response.status).toBeGreaterThanOrEqual(200)
    })
  })
})