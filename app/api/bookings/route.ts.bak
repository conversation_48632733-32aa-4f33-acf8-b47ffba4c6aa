import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { bookingSearchSchema, bookingCreateSchema, formatBookingForCalendar } from '@/lib/validations/booking';
import { Prisma } from '@prisma/client';

export async function GET(request: NextRequest) {
  console.log('GET /api/bookings - Starting request');
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters with defaults and validation
    const searchQuery = searchParams.get('search') || '';
    const page = Math.max(1, parseInt(searchParams.get('page') || '1') || 1);
    const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '10') || 10));
    const customerId = searchParams.get('customerId') ? parseInt(searchParams.get('customerId')!) : undefined;
    const status = searchParams.get('status') || undefined;
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined;
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined;
    const format = searchParams.get('format') || 'default'; // 'calendar' for calendar events

    // Validate parameters using Zod schema
    const validatedParams = bookingSearchSchema.parse({
      search: searchQuery,
      page,
      limit,
      customerId,
      status,
      startDate,
      endDate
    });

    console.log('Validated params:', validatedParams);

    // Build where clause for search and filtering
    const where: Prisma.BookingWhereInput = {};
    
    if (validatedParams.search) {
      where.OR = [
        { customer: { name: { contains: validatedParams.search } } },
        { customer: { email: { contains: validatedParams.search } } },
        { customer: { companyName: { contains: validatedParams.search } } },
        { resources: { some: { name: { contains: validatedParams.search } } } }
      ];
    }

    if (validatedParams.customerId) {
      where.customerId = validatedParams.customerId;
    }

    if (validatedParams.status) {
      where.status = validatedParams.status as any;
    }

    // Date range filtering
    if (validatedParams.startDate || validatedParams.endDate) {
      where.AND = [];
      
      if (validatedParams.startDate) {
        where.AND.push({
          end: { gte: validatedParams.startDate }
        });
      }
      
      if (validatedParams.endDate) {
        where.AND.push({
          start: { lte: validatedParams.endDate }
        });
      }
    }

    console.log('Where clause:', JSON.stringify(where, null, 2));

    // Calculate pagination
    const skip = (validatedParams.page - 1) * validatedParams.limit;
    console.log('Pagination - skip:', skip, 'take:', validatedParams.limit);

    // Get total count for pagination metadata
    console.log('Getting total count...');
    const totalCount = await prisma.booking.count({ where });
    console.log('Total count:', totalCount);

    // Fetch bookings with comprehensive includes
    console.log('Fetching bookings...');
    const bookings = await prisma.booking.findMany({
      where,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            companyName: true
          }
        },
        resources: {
          select: {
            id: true,
            name: true,
            type: true,
            basePrice: true
          }
        },
        caterings: {
          include: {
            catering: {
              select: {
                id: true,
                offerName: true,
                pricePerPerson: true
              }
            }
          }
        },
        invoice: {
          select: {
            id: true,
            status: true,
            total: true,
            paid: true
          }
        }
      },
      orderBy: [
        { start: 'desc' },
        { id: 'desc' }
      ],
      skip,
      take: validatedParams.limit
    });
    console.log('Fetched bookings:', bookings.length);

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / validatedParams.limit);
    const hasNextPage = validatedParams.page < totalPages;
    const hasPreviousPage = validatedParams.page > 1;

    // Format response based on requested format
    if (format === 'calendar') {
      // Format bookings for calendar display
      const calendarEvents = bookings.map(booking => formatBookingForCalendar(booking));
      
      return NextResponse.json({
        success: true,
        data: calendarEvents
      });
    }

    // Default format with pagination
    return NextResponse.json({
      success: true,
      data: {
        data: bookings,
        pagination: {
          page: validatedParams.page,
          limit: validatedParams.limit,
          total: totalCount,
          totalPages,
          hasNext: hasNextPage,
          hasPrev: hasPreviousPage
        }
      }
    });
  } catch (error) {
    console.error('Error fetching bookings:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: 'GET /api/bookings'
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid query parameters',
          message: 'Please check your search parameters and try again.',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Handle database connection errors
    if (error instanceof Error && error.message.includes('database')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    // Handle timeout errors
    if (error instanceof Error && error.message.includes('timeout')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Request timeout',
          message: 'The request took too long to complete. Please try again.'
        },
        { status: 408 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching bookings. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  console.log('POST /api/bookings - Starting booking creation');
  let body: any = null;
  
  try {
    body = await request.json();
    console.log('Request body:', JSON.stringify(body, null, 2));
    
    // Convert date strings to Date objects if needed
    if (body.start && typeof body.start === 'string') {
      body.start = new Date(body.start);
    }
    if (body.end && typeof body.end === 'string') {
      body.end = new Date(body.end);
    }
    
    // Validate input data
    console.log('Validating input data...');
    const validatedData = bookingCreateSchema.parse(body);
    console.log('Validation successful:', JSON.stringify(validatedData, null, 2));

    // Check if customer exists
    console.log('Checking if customer exists...');
    const customer = await prisma.customer.findUnique({
      where: { id: validatedData.customerId }
    });
    
    if (!customer) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Customer not found',
          message: 'The selected customer does not exist.'
        },
        { status: 404 }
      );
    }

    // Check if all resources exist
    console.log('Checking if resources exist...');
    const resources = await prisma.resource.findMany({
      where: { id: { in: validatedData.resourceIds } }
    });
    
    if (resources.length !== validatedData.resourceIds.length) {
      const foundIds = resources.map(r => r.id);
      const missingIds = validatedData.resourceIds.filter(id => !foundIds.includes(id));
      return NextResponse.json(
        { 
          success: false,
          error: 'Resources not found',
          message: `The following resource IDs do not exist: ${missingIds.join(', ')}`
        },
        { status: 404 }
      );
    }

    // Check if all catering items exist (if any)
    if (validatedData.caterings.length > 0) {
      console.log('Checking if catering items exist...');
      const cateringIds = validatedData.caterings.map(c => c.cateringId);
      const caterings = await prisma.catering.findMany({
        where: { id: { in: cateringIds } }
      });
      
      if (caterings.length !== cateringIds.length) {
        const foundIds = caterings.map(c => c.id);
        const missingIds = cateringIds.filter(id => !foundIds.includes(id));
        return NextResponse.json(
          { 
            success: false,
            error: 'Catering items not found',
            message: `The following catering IDs do not exist: ${missingIds.join(', ')}`
          },
          { status: 404 }
        );
      }
    }

    // Check for resource conflicts (basic overlap check)
    console.log('Checking for resource conflicts...');
    const conflictingBookings = await prisma.booking.findMany({
      where: {
        AND: [
          { status: { not: 'CANCELLED' } },
          { resources: { some: { id: { in: validatedData.resourceIds } } } },
          {
            OR: [
              // New booking starts during existing booking
              {
                AND: [
                  { start: { lte: validatedData.start } },
                  { end: { gt: validatedData.start } }
                ]
              },
              // New booking ends during existing booking
              {
                AND: [
                  { start: { lt: validatedData.end } },
                  { end: { gte: validatedData.end } }
                ]
              },
              // New booking completely contains existing booking
              {
                AND: [
                  { start: { gte: validatedData.start } },
                  { end: { lte: validatedData.end } }
                ]
              }
            ]
          }
        ]
      },
      include: {
        resources: { select: { name: true } },
        customer: { select: { name: true } }
      }
    });

    if (conflictingBookings.length > 0) {
      const conflictDetails = conflictingBookings.map(booking => ({
        id: booking.id,
        customer: booking.customer.name,
        resources: booking.resources.map(r => r.name),
        start: booking.start,
        end: booking.end
      }));

      return NextResponse.json(
        { 
          success: false,
          error: 'Resource conflict',
          message: 'One or more selected resources are already booked for the specified time period.',
          conflicts: conflictDetails
        },
        { status: 409 }
      );
    }

    // Create the booking with transaction to ensure data consistency
    console.log('Creating booking in database...');
    const booking = await prisma.$transaction(async (tx) => {
      // Create the booking
      const newBooking = await tx.booking.create({
        data: {
          customerId: validatedData.customerId,
          status: validatedData.status,
          start: validatedData.start,
          end: validatedData.end,
          resources: {
            connect: validatedData.resourceIds.map(id => ({ id }))
          }
          // TODO: Add createdById from authenticated user session
        }
      });

      // Create catering relationships if any
      if (validatedData.caterings.length > 0) {
        await tx.cateringOnBooking.createMany({
          data: validatedData.caterings.map(catering => ({
            bookingId: newBooking.id,
            cateringId: catering.cateringId,
            quantity: catering.quantity
            // TODO: Add createdById from authenticated user session
          }))
        });
      }

      // Fetch the complete booking with all relationships
      return await tx.booking.findUnique({
        where: { id: newBooking.id },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              companyName: true
            }
          },
          resources: {
            select: {
              id: true,
              name: true,
              type: true,
              basePrice: true
            }
          },
          caterings: {
            include: {
              catering: {
                select: {
                  id: true,
                  offerName: true,
                  pricePerPerson: true
                }
              }
            }
          },
          invoice: {
            select: {
              id: true,
              status: true,
              total: true,
              paid: true
            }
          }
        }
      });
    });

    console.log('Booking created successfully:', JSON.stringify(booking, null, 2));

    return NextResponse.json({
      success: true,
      data: booking
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating booking:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: 'POST /api/bookings',
      requestBody: body
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid input data',
          message: 'Please check the booking information and try again.',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2002':
          return NextResponse.json(
            { 
              success: false,
              error: 'Duplicate booking',
              message: 'A booking with similar details already exists.'
            },
            { status: 409 }
          );
        case 'P2003':
          return NextResponse.json(
            { 
              success: false,
              error: 'Invalid reference',
              message: 'Invalid customer, resource, or catering reference. Please check your input.'
            },
            { status: 400 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Record not found',
              message: 'Required record not found in database.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    // Handle timeout errors
    if (error instanceof Error && error.message.includes('timeout')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Request timeout',
          message: 'The request took too long to complete. Please try again.'
        },
        { status: 408 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while creating the booking. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}