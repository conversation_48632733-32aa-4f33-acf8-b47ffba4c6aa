import { describe, it, expect, vi, beforeEach } from 'vitest';
import { NextRequest } from 'next/server';

// Mock Prisma
const mockPrisma = {
  booking: {
    findUnique: vi.fn(),
  },
  $transaction: vi.fn(),
};

vi.mock('@/lib/db', () => ({
  prisma: mockPrisma,
}));

describe('POST /api/bookings/[id]/invoice', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return 400 for invalid booking ID', async () => {
    const { POST } = await import('../route');
    
    const request = new NextRequest('http://localhost/api/bookings/invalid/invoice', {
      method: 'POST',
    });

    const response = await POST(request, { params: { id: 'invalid' } });
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.success).toBe(false);
    expect(data.error).toBe('Invalid booking ID');
  });

  it('should return 404 for non-existent booking', async () => {
    const { POST } = await import('../route');
    
    mockPrisma.booking.findUnique.mockResolvedValue(null);

    const request = new NextRequest('http://localhost/api/bookings/999/invoice', {
      method: 'POST',
    });

    const response = await POST(request, { params: { id: '999' } });
    const data = await response.json();

    expect(response.status).toBe(404);
    expect(data.success).toBe(false);
    expect(data.error).toBe('Booking not found');
  });

  it('should return 409 if invoice already exists', async () => {
    const { POST } = await import('../route');
    
    const mockBooking = {
      id: 1,
      customer: { id: 1, name: 'Test Customer', email: '<EMAIL>', companyName: null },
      resources: [],
      caterings: [],
      invoice: { id: 1, status: 'PENDING', total: 100 }, // Invoice already exists
    };

    mockPrisma.booking.findUnique.mockResolvedValue(mockBooking);

    const request = new NextRequest('http://localhost/api/bookings/1/invoice', {
      method: 'POST',
    });

    const response = await POST(request, { params: { id: '1' } });
    const data = await response.json();

    expect(response.status).toBe(409);
    expect(data.success).toBe(false);
    expect(data.error).toBe('Invoice already exists');
  });

  it('should return 400 for booking with no billable items', async () => {
    const { POST } = await import('../route');
    
    const mockBooking = {
      id: 1,
      customer: { id: 1, name: 'Test Customer', email: '<EMAIL>', companyName: null },
      resources: [], // No resources
      caterings: [], // No catering
      invoice: null,
    };

    mockPrisma.booking.findUnique.mockResolvedValue(mockBooking);

    const request = new NextRequest('http://localhost/api/bookings/1/invoice', {
      method: 'POST',
    });

    const response = await POST(request, { params: { id: '1' } });
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.success).toBe(false);
    expect(data.error).toBe('No billable items');
  });
});