import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { Prisma } from '@prisma/client';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  console.log('POST /api/bookings/[id]/invoice - Starting invoice generation');
  
  try {
    const { id } = await params;
    const bookingId = parseInt(id);
    
    if (isNaN(bookingId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid booking ID',
          message: 'Booking ID must be a valid number'
        },
        { status: 400 }
      );
    }

    console.log('Fetching booking details for ID:', bookingId);

    // Fetch booking with all necessary relationships
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            companyName: true
          }
        },
        resources: {
          select: {
            id: true,
            name: true,
            type: true,
            basePrice: true
          }
        },
        caterings: {
          include: {
            catering: {
              select: {
                id: true,
                offerName: true,
                pricePerPerson: true,
                firstPartyShare: true,
                vendorShare: true
              }
            }
          }
        },
        invoice: {
          select: {
            id: true,
            status: true,
            total: true
          }
        }
      }
    });

    if (!booking) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Booking not found',
          message: 'The specified booking does not exist'
        },
        { status: 404 }
      );
    }

    // Check if booking already has an invoice
    if (booking.invoice) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invoice already exists',
          message: 'This booking already has an associated invoice'
        },
        { status: 409 }
      );
    }

    console.log('Generating line items for booking:', bookingId);

    // Generate line items from booking resources and catering
    const lineItems: Array<{
      description: string;
      amount: number;
      qty: number;
      isCatering: boolean;
      cateringId?: number;
    }> = [];

    // Add resource line items
    for (const resource of booking.resources) {
      lineItems.push({
        description: `${resource.name} (${resource.type.replace(/_/g, ' ').toLowerCase()})`,
        amount: resource.basePrice,
        qty: 1,
        isCatering: false
      });
    }

    // Add catering line items with proper offer references
    for (const cateringBooking of booking.caterings) {
      const catering = cateringBooking.catering;
      const totalAmount = catering.pricePerPerson * cateringBooking.quantity;
      
      lineItems.push({
        description: `${catering.offerName} (${cateringBooking.quantity} people)`,
        amount: totalAmount,
        qty: 1, // Quantity is already calculated in the amount
        isCatering: true,
        cateringId: catering.id
      });
    }

    // Validate that we have at least one line item
    if (lineItems.length === 0) {
      return NextResponse.json(
        { 
          success: false,
          error: 'No billable items',
          message: 'This booking has no resources or catering items to invoice'
        },
        { status: 400 }
      );
    }

    // Calculate total amount
    const totalAmount = lineItems.reduce((sum, item) => sum + item.amount, 0);

    console.log('Creating invoice with line items:', {
      bookingId,
      lineItemsCount: lineItems.length,
      totalAmount
    });

    // Create invoice with line items in a transaction
    const invoice = await prisma.$transaction(async (tx) => {
      // Create the invoice
      const newInvoice = await tx.invoice.create({
        data: {
          bookingId: bookingId,
          total: totalAmount,
          paid: 0,
          status: 'PENDING',
          // TODO: Add createdById from authenticated user session
        }
      });

      // Create line items
      const lineItemsData = lineItems.map(item => ({
        invoiceId: newInvoice.id,
        description: item.description,
        amount: item.amount,
        qty: item.qty,
        isCatering: item.isCatering,
        cateringId: item.cateringId || null,
        // TODO: Add createdById from authenticated user session
      }));

      await tx.lineItem.createMany({
        data: lineItemsData
      });

      // Return the complete invoice with relationships
      return await tx.invoice.findUnique({
        where: { id: newInvoice.id },
        include: {
          booking: {
            include: {
              customer: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  companyName: true
                }
              },
              resources: {
                select: {
                  id: true,
                  name: true,
                  type: true,
                  basePrice: true
                }
              }
            }
          },
          lineItems: {
            include: {
              catering: {
                select: {
                  id: true,
                  offerName: true,
                  pricePerPerson: true,
                  firstPartyShare: true,
                  vendorShare: true
                }
              }
            },
            orderBy: {
              createdAt: 'asc'
            }
          },
          payments: {
            select: {
              id: true,
              amount: true,
              method: true,
              status: true,
              reference: true,
              paidAt: true,
              createdAt: true
            },
            orderBy: {
              createdAt: 'desc'
            }
          }
        }
      });
    });

    console.log('Invoice generated successfully:', {
      invoiceId: invoice?.id,
      bookingId,
      totalAmount,
      lineItemsCount: invoice?.lineItems.length
    });

    return NextResponse.json({
      success: true,
      data: invoice,
      message: 'Invoice generated successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error generating invoice:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `POST /api/bookings/${(await params).id}/invoice`,
      bookingId: (await params).id
    });
    
    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2002':
          return NextResponse.json(
            { 
              success: false,
              error: 'Invoice already exists',
              message: 'An invoice for this booking already exists'
            },
            { status: 409 }
          );
        case 'P2003':
          return NextResponse.json(
            { 
              success: false,
              error: 'Invalid reference',
              message: 'Invalid booking or catering reference. Please check your data.'
            },
            { status: 400 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Booking not found',
              message: 'The specified booking does not exist'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    // Handle timeout errors
    if (error instanceof Error && error.message.includes('timeout')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Request timeout',
          message: 'The request took too long to complete. Please try again.'
        },
        { status: 408 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while generating the invoice. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}