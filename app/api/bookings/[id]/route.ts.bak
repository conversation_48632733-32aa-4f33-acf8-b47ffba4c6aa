import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { bookingUpdateSchema } from '@/lib/validations/booking';
import { Prisma } from '@prisma/client';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;
    const bookingId = parseInt(id);
    
    if (isNaN(bookingId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid booking ID',
          message: 'Booking ID must be a valid number'
        },
        { status: 400 }
      );
    }

    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      select: {
        id: true,
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phoneNumber: true,
            companyName: true
          }
        },
        customerId: true,
        resources: {
          select: {
            id: true,
            name: true,
            type: true,
            basePrice: true
          }
        },
        status: true,
        start: true,
        end: true,
        invoice: {
          select: {
            id: true,
            status: true,
            total: true,
            paid: true
          }
        },
        caterings: {
          select: {
            id: true,
            quantity: true,
            catering: {
              select: {
                id: true,
                offerName: true,
                pricePerPerson: true
              }
            }
          }
        },
        createdAt: true,
        updatedAt: true,
        createdById: true,
        updatedById: true
      }
    });

    if (!booking) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Booking not found',
          message: 'The requested booking does not exist'
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: booking
    });
  } catch (error) {
    const resolvedParams = await params;
    console.error('Error fetching booking:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `GET /api/bookings/${resolvedParams.id}`,
      bookingId: resolvedParams.id
    });

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching the booking. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  let body: any = null;
  
  try {
    const { id } = await params;
    const bookingId = parseInt(id);
    
    if (isNaN(bookingId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid booking ID',
          message: 'Booking ID must be a valid number'
        },
        { status: 400 }
      );
    }

    body = await request.json();
    
    // Convert date strings to Date objects if needed
    if (body.start && typeof body.start === 'string') {
      body.start = new Date(body.start);
    }
    if (body.end && typeof body.end === 'string') {
      body.end = new Date(body.end);
    }
    
    // Validate input data
    const validatedData = bookingUpdateSchema.parse(body);

    // Check if booking exists
    const existingBooking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        resources: true,
        caterings: true
      }
    });

    if (!existingBooking) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Booking not found',
          message: 'The requested booking does not exist'
        },
        { status: 404 }
      );
    }

    // Update the booking with transaction to handle resources and caterings
    const updatedBooking = await prisma.$transaction(async (tx) => {
      // Update the booking basic fields
      const booking = await tx.booking.update({
        where: { id: bookingId },
        data: {
          customerId: validatedData.customerId,
          status: validatedData.status,
          start: validatedData.start,
          end: validatedData.end,
          updatedAt: new Date(),
          // TODO: Add updatedById from authenticated user session
        }
      });

      // Update resources associations
      // First disconnect all existing resources
      await tx.booking.update({
        where: { id: bookingId },
        data: {
          resources: {
            set: [] // Disconnect all
          }
        }
      });

      // Then connect the new resources
      if (validatedData.resourceIds && validatedData.resourceIds.length > 0) {
        await tx.booking.update({
          where: { id: bookingId },
          data: {
            resources: {
              connect: validatedData.resourceIds.map(id => ({ id }))
            }
          }
        });
      }

      // Update caterings
      // First delete all existing catering associations
      await tx.cateringOnBooking.deleteMany({
        where: { bookingId: bookingId }
      });

      // Then create new catering associations if provided
      if (validatedData.caterings && validatedData.caterings.length > 0) {
        await tx.cateringOnBooking.createMany({
          data: validatedData.caterings.map(catering => ({
            bookingId: bookingId,
            cateringId: catering.cateringId,
            quantity: catering.quantity,
            // TODO: Add createdById from authenticated user session
          }))
        });
      }

      // Return the complete updated booking with relations
      return await tx.booking.findUnique({
        where: { id: bookingId },
        select: {
          id: true,
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              phoneNumber: true,
              companyName: true
            }
          },
          customerId: true,
          resources: {
            select: {
              id: true,
              name: true,
              type: true,
              basePrice: true
            }
          },
          status: true,
          start: true,
          end: true,
          invoice: {
            select: {
              id: true,
              status: true,
              total: true,
              paid: true
            }
          },
          caterings: {
            select: {
              id: true,
              quantity: true,
              catering: {
                select: {
                  id: true,
                  offerName: true,
                  pricePerPerson: true
                }
              }
            }
          },
          createdAt: true,
          updatedAt: true,
          createdById: true,
          updatedById: true
        }
      });
    });

    return NextResponse.json({
      success: true,
      data: updatedBooking
    });
  } catch (error) {
    const resolvedParams = await params;
    console.error('Error updating booking:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `PUT /api/bookings/${resolvedParams.id}`,
      bookingId: resolvedParams.id,
      requestBody: body
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid input data',
          message: 'Please check the booking information and try again.',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2003':
          return NextResponse.json(
            { 
              success: false,
              error: 'Invalid reference',
              message: 'One or more selected resources, customer, or catering items do not exist. Please refresh and try again.'
            },
            { status: 400 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Booking not found',
              message: 'The booking you are trying to update no longer exists.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while updating the booking. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;
    const bookingId = parseInt(id);
    
    if (isNaN(bookingId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid booking ID',
          message: 'Booking ID must be a valid number'
        },
        { status: 400 }
      );
    }

    // Check if booking exists and has an invoice
    const existingBooking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        invoice: true,
        caterings: true
      }
    });

    if (!existingBooking) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Booking not found',
          message: 'The requested booking does not exist'
        },
        { status: 404 }
      );
    }

    // Check if booking has an associated invoice (referential integrity)
    if (existingBooking.invoice) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Cannot delete booking: booking has an associated invoice',
          message: 'This booking cannot be deleted because it has an associated invoice. Please delete the invoice first.'
        },
        { status: 409 }
      );
    }

    // Delete the booking with transaction to handle related data
    await prisma.$transaction(async (tx) => {
      // Delete catering associations first
      await tx.cateringOnBooking.deleteMany({
        where: { bookingId: bookingId }
      });

      // Disconnect resources (many-to-many relationship)
      await tx.booking.update({
        where: { id: bookingId },
        data: {
          resources: {
            set: [] // Disconnect all resources
          }
        }
      });

      // Finally delete the booking
      await tx.booking.delete({
        where: { id: bookingId }
      });
    });

    return NextResponse.json({
      success: true,
      message: 'Booking deleted successfully'
    });
  } catch (error) {
    const resolvedParams = await params;
    console.error('Error deleting booking:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `DELETE /api/bookings/${resolvedParams.id}`,
      bookingId: resolvedParams.id
    });
    
    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2003':
          return NextResponse.json(
            { 
              success: false,
              error: 'Cannot delete booking: booking has an associated invoice',
              message: 'This booking cannot be deleted because it has an associated invoice. Please delete the invoice first.'
            },
            { status: 409 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Booking not found',
              message: 'The booking you are trying to delete no longer exists.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while deleting the booking. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}