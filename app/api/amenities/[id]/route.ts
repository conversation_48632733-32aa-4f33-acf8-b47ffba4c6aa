import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { amenityUpdateSchema } from '@/lib/validations/amenity';
import { Prisma } from '@prisma/client';

interface RouteParams {
  params: Promise<{
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;
    const amenityId = parseInt(id);
    
    if (isNaN(amenityId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid amenity ID',
          message: 'Amenity ID must be a valid number'
        },
        { status: 400 }
      );
    }

    const amenity = await prisma.amenity.findUnique({
      where: { id: amenityId },
      select: {
        id: true,
        name: true,
        icon: true,
        createdAt: true,
        updatedAt: true,
        createdById: true,
        updatedById: true
      }
    });

    if (!amenity) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Amenity not found',
          message: 'The requested amenity does not exist'
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: amenity
    });
  } catch (error) {
    const resolvedParams = await params;
    console.error('Error fetching amenity:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `GET /api/amenities/${resolvedParams.id}`,
      amenityId: resolvedParams.id
    });

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching the amenity. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  let body: any = null;
  
  try {
    const { id } = await params;
    const amenityId = parseInt(id);
    
    if (isNaN(amenityId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid amenity ID',
          message: 'Amenity ID must be a valid number'
        },
        { status: 400 }
      );
    }

    body = await request.json();
    
    // Validate input data
    const validatedData = amenityUpdateSchema.parse(body);

    // Check if amenity exists
    const existingAmenity = await prisma.amenity.findUnique({
      where: { id: amenityId }
    });

    if (!existingAmenity) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Amenity not found',
          message: 'The requested amenity does not exist'
        },
        { status: 404 }
      );
    }

    // Update the amenity
    const updatedAmenity = await prisma.amenity.update({
      where: { id: amenityId },
      data: {
        name: validatedData.name,
        icon: validatedData.icon,
        updatedAt: new Date(),
        // TODO: Add updatedById from authenticated user session
      },
      select: {
        id: true,
        name: true,
        icon: true,
        createdAt: true,
        updatedAt: true,
        createdById: true,
        updatedById: true
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedAmenity
    });
  } catch (error) {
    const resolvedParams = await params;
    console.error('Error updating amenity:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `PUT /api/amenities/${resolvedParams.id}`,
      amenityId: resolvedParams.id,
      requestBody: body
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid input data',
          message: 'Please check the amenity information and try again.',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2002':
          return NextResponse.json(
            { 
              success: false,
              error: 'Amenity name already exists',
              message: 'This amenity name is already taken. Please choose a different one.',
              field: 'name'
            },
            { status: 409 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Amenity not found',
              message: 'The amenity you are trying to update no longer exists.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while updating the amenity. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;
    const amenityId = parseInt(id);
    
    if (isNaN(amenityId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid amenity ID',
          message: 'Amenity ID must be a valid number'
        },
        { status: 400 }
      );
    }

    // Check if amenity exists
    const existingAmenity = await prisma.amenity.findUnique({
      where: { id: amenityId }
    });

    if (!existingAmenity) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Amenity not found',
          message: 'The requested amenity does not exist'
        },
        { status: 404 }
      );
    }

    // Delete the amenity
    await prisma.amenity.delete({
      where: { id: amenityId }
    });

    return NextResponse.json({
      success: true,
      message: 'Amenity deleted successfully'
    });
  } catch (error) {
    const resolvedParams = await params;
    console.error('Error deleting amenity:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `DELETE /api/amenities/${resolvedParams.id}`,
      amenityId: resolvedParams.id
    });
    
    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2003':
          return NextResponse.json(
            { 
              success: false,
              error: 'Cannot delete amenity: amenity has associated resources',
              message: 'This amenity cannot be deleted because it is currently associated with one or more resources. Please remove these associations first.'
            },
            { status: 409 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Amenity not found',
              message: 'The amenity you are trying to delete no longer exists.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while deleting the amenity. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}