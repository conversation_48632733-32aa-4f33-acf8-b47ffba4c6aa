import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { resourceSearchSchema, resourceCreateSchema } from '@/lib/validations/resource';
import { Prisma } from '@prisma/client';

export async function GET(request: NextRequest) {
  console.log('GET /api/resources - Starting request');
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters with defaults and basic validation
    const searchQuery = searchParams.get('search') || '';
    const typeFilter = searchParams.get('type') || '';
    const page = Math.max(1, parseInt(searchParams.get('page') || '1') || 1);
    const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '10') || 10));

    // Simple validation without Zod for now
    const validatedParams = {
      search: searchQuery,
      type: typeFilter,
      page,
      limit
    };

    console.log('Validated params:', validatedParams);

    // Build where clause for search and filtering
    const where: Prisma.ResourceWhereInput = {};
    
    if (validatedParams.search) {
      where.name = { contains: validatedParams.search };
    }

    if (validatedParams.type && validatedParams.type !== 'ALL') {
      where.type = validatedParams.type as any;
    }

    console.log('Where clause:', JSON.stringify(where, null, 2));

    // Calculate pagination
    const skip = (validatedParams.page - 1) * validatedParams.limit;
    console.log('Pagination - skip:', skip, 'take:', validatedParams.limit);

    // Get total count for pagination metadata
    console.log('Getting total count...');
    const totalCount = await prisma.resource.count({ where });
    console.log('Total count:', totalCount);

    // Fetch resources with pagination and include related data
    console.log('Fetching resources...');
    const resources = await prisma.resource.findMany({
      where,
      select: {
        id: true,
        name: true,
        type: true,
        basePrice: true,
        details: true,
        seatingStyle: true,
        numberOfAttendees: true,
        numberOfDesks: true,
        numberOfChairs: true,
        amenities: {
          select: {
            id: true,
            name: true,
            icon: true
          }
        },
        stageStyles: {
          select: {
            id: true,
            style: true
          }
        },
        createdAt: true,
        updatedAt: true,
        createdById: true,
        updatedById: true
      },
      orderBy: [
        { createdAt: 'desc' },
        { id: 'desc' }
      ],
      skip,
      take: validatedParams.limit
    });
    console.log('Fetched resources:', resources.length);

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / validatedParams.limit);
    const hasNextPage = validatedParams.page < totalPages;
    const hasPreviousPage = validatedParams.page > 1;

    return NextResponse.json({
      success: true,
      data: {
        data: resources,
        pagination: {
          page: validatedParams.page,
          limit: validatedParams.limit,
          total: totalCount,
          totalPages,
          hasNext: hasNextPage,
          hasPrev: hasPreviousPage
        }
      }
    });
  } catch (error) {
    console.error('Error fetching resources:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: 'GET /api/resources'
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid query parameters',
          message: 'Please check your search parameters and try again.',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Handle database connection errors
    if (error instanceof Error && error.message.includes('database')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    // Handle timeout errors
    if (error instanceof Error && error.message.includes('timeout')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Request timeout',
          message: 'The request took too long to complete. Please try again.'
        },
        { status: 408 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching resources. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  console.log('POST /api/resources - Starting resource creation');
  let body: any = null;
  
  try {
    body = await request.json();
    console.log('Request body:', JSON.stringify(body, null, 2));
    
    // Validate input data
    console.log('Validating input data...');
    const validatedData = resourceCreateSchema.parse(body);
    console.log('Validation successful:', JSON.stringify(validatedData, null, 2));

    // Create the resource with transaction to handle amenities and stage styles
    console.log('Creating resource in database...');
    const resource = await prisma.$transaction(async (tx) => {
      // Create the resource first
      const newResource = await tx.resource.create({
        data: {
          name: validatedData.name,
          type: validatedData.type,
          basePrice: validatedData.basePrice,
          details: validatedData.details,
          seatingStyle: validatedData.seatingStyle,
          numberOfAttendees: validatedData.numberOfAttendees,
          numberOfDesks: validatedData.numberOfDesks,
          numberOfChairs: validatedData.numberOfChairs,
          // TODO: Add createdById from authenticated user session
        }
      });

      // Connect amenities if provided
      if (validatedData.amenityIds && validatedData.amenityIds.length > 0) {
        await tx.resource.update({
          where: { id: newResource.id },
          data: {
            amenities: {
              connect: validatedData.amenityIds.map(id => ({ id }))
            }
          }
        });
      }

      // Create stage styles if provided
      if (validatedData.stageStyles && validatedData.stageStyles.length > 0) {
        await tx.resourceStageStyle.createMany({
          data: validatedData.stageStyles.map(style => ({
            resourceId: newResource.id,
            style: style,
            // TODO: Add createdById from authenticated user session
          }))
        });
      }

      // Return the complete resource with relations
      return await tx.resource.findUnique({
        where: { id: newResource.id },
        select: {
          id: true,
          name: true,
          type: true,
          basePrice: true,
          details: true,
          seatingStyle: true,
          numberOfAttendees: true,
          numberOfDesks: true,
          numberOfChairs: true,
          amenities: {
            select: {
              id: true,
              name: true,
              icon: true
            }
          },
          stageStyles: {
            select: {
              id: true,
              style: true
            }
          },
          createdAt: true,
          updatedAt: true,
          createdById: true,
          updatedById: true
        }
      });
    });

    console.log('Resource created successfully:', JSON.stringify(resource, null, 2));

    return NextResponse.json({
      success: true,
      data: resource
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating resource:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: 'POST /api/resources',
      requestBody: body
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid input data',
          message: 'Please check the resource information and try again.',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2002':
          return NextResponse.json(
            { 
              success: false,
              error: 'Resource name already exists',
              message: 'This resource name is already taken. Please choose a different one.',
              field: 'name'
            },
            { status: 409 }
          );
        case 'P2003':
          return NextResponse.json(
            { 
              success: false,
              error: 'Invalid reference',
              message: 'One or more selected amenities do not exist. Please refresh and try again.'
            },
            { status: 400 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Record not found',
              message: 'Required record not found in database.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    // Handle timeout errors
    if (error instanceof Error && error.message.includes('timeout')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Request timeout',
          message: 'The request took too long to complete. Please try again.'
        },
        { status: 408 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while creating the resource. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}