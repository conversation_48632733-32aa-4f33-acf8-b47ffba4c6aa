import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { resourceUpdateSchema } from '@/lib/validations/resource';
import { Prisma } from '@prisma/client';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const resourceId = parseInt((await params).id);
    
    if (isNaN(resourceId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid resource ID',
          message: 'Resource ID must be a valid number'
        },
        { status: 400 }
      );
    }

    const resource = await prisma.resource.findUnique({
      where: { id: resourceId },
      select: {
        id: true,
        name: true,
        type: true,
        basePrice: true,
        details: true,
        seatingStyle: true,
        numberOfAttendees: true,
        numberOfDesks: true,
        numberOfChairs: true,
        amenities: {
          select: {
            id: true,
            name: true,
            icon: true
          }
        },
        stageStyles: {
          select: {
            id: true,
            style: true
          }
        },
        createdAt: true,
        updatedAt: true,
        createdById: true,
        updatedById: true
      }
    });

    if (!resource) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Resource not found',
          message: 'The requested resource does not exist'
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: resource
    });
  } catch (error) {
    console.error('Error fetching resource:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `GET /api/resources/${(await params).id}`,
      resourceId: (await params).id
    });

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching the resource. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  let body: any = null;
  
  try {
    const resourceId = parseInt((await params).id);
    
    if (isNaN(resourceId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid resource ID',
          message: 'Resource ID must be a valid number'
        },
        { status: 400 }
      );
    }

    body = await request.json();
    
    // Validate input data
    const validatedData = resourceUpdateSchema.parse(body);

    // Check if resource exists
    const existingResource = await prisma.resource.findUnique({
      where: { id: resourceId },
      include: {
        amenities: true,
        stageStyles: true
      }
    });

    if (!existingResource) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Resource not found',
          message: 'The requested resource does not exist'
        },
        { status: 404 }
      );
    }

    // Update the resource with transaction to handle amenities and stage styles
    const updatedResource = await prisma.$transaction(async (tx) => {
      // Update the resource basic fields
      const resource = await tx.resource.update({
        where: { id: resourceId },
        data: {
          name: validatedData.name,
          type: validatedData.type,
          basePrice: validatedData.basePrice,
          details: validatedData.details,
          seatingStyle: validatedData.seatingStyle,
          numberOfAttendees: validatedData.numberOfAttendees,
          numberOfDesks: validatedData.numberOfDesks,
          numberOfChairs: validatedData.numberOfChairs,
          updatedAt: new Date(),
          // TODO: Add updatedById from authenticated user session
        }
      });

      // Update amenities associations
      // First disconnect all existing amenities
      await tx.resource.update({
        where: { id: resourceId },
        data: {
          amenities: {
            set: [] // Disconnect all
          }
        }
      });

      // Then connect the new amenities if provided
      if (validatedData.amenityIds && validatedData.amenityIds.length > 0) {
        await tx.resource.update({
          where: { id: resourceId },
          data: {
            amenities: {
              connect: validatedData.amenityIds.map(id => ({ id }))
            }
          }
        });
      }

      // Update stage styles
      // First delete all existing stage styles
      await tx.resourceStageStyle.deleteMany({
        where: { resourceId: resourceId }
      });

      // Then create new stage styles if provided
      if (validatedData.stageStyles && validatedData.stageStyles.length > 0) {
        await tx.resourceStageStyle.createMany({
          data: validatedData.stageStyles.map(style => ({
            resourceId: resourceId,
            style: style,
            // TODO: Add createdById from authenticated user session
          }))
        });
      }

      // Return the complete updated resource with relations
      return await tx.resource.findUnique({
        where: { id: resourceId },
        select: {
          id: true,
          name: true,
          type: true,
          basePrice: true,
          details: true,
          seatingStyle: true,
          numberOfAttendees: true,
          numberOfDesks: true,
          numberOfChairs: true,
          amenities: {
            select: {
              id: true,
              name: true,
              icon: true
            }
          },
          stageStyles: {
            select: {
              id: true,
              style: true
            }
          },
          createdAt: true,
          updatedAt: true,
          createdById: true,
          updatedById: true
        }
      });
    });

    return NextResponse.json({
      success: true,
      data: updatedResource
    });
  } catch (error) {
    console.error('Error updating resource:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `PUT /api/resources/${(await params).id}`,
      resourceId: (await params).id,
      requestBody: body
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid input data',
          message: 'Please check the resource information and try again.',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2002':
          return NextResponse.json(
            { 
              success: false,
              error: 'Resource name already exists',
              message: 'This resource name is already taken. Please choose a different one.',
              field: 'name'
            },
            { status: 409 }
          );
        case 'P2003':
          return NextResponse.json(
            { 
              success: false,
              error: 'Invalid reference',
              message: 'One or more selected amenities do not exist. Please refresh and try again.'
            },
            { status: 400 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Resource not found',
              message: 'The resource you are trying to update no longer exists.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while updating the resource. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const resourceId = parseInt((await params).id);
    
    if (isNaN(resourceId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid resource ID',
          message: 'Resource ID must be a valid number'
        },
        { status: 400 }
      );
    }

    // Check if resource exists
    const existingResource = await prisma.resource.findUnique({
      where: { id: resourceId },
      include: {
        bookings: true // Check for existing bookings
      }
    });

    if (!existingResource) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Resource not found',
          message: 'The requested resource does not exist'
        },
        { status: 404 }
      );
    }

    // Check if resource has existing bookings (foreign key constraint)
    if (existingResource.bookings && existingResource.bookings.length > 0) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Cannot delete resource: resource has existing bookings',
          message: 'This resource cannot be deleted because it has existing bookings. Please cancel or complete these bookings first.'
        },
        { status: 409 }
      );
    }

    // Delete the resource with transaction to handle related data
    await prisma.$transaction(async (tx) => {
      // Delete stage styles first
      await tx.resourceStageStyle.deleteMany({
        where: { resourceId: resourceId }
      });

      // Disconnect amenities (many-to-many relationship)
      await tx.resource.update({
        where: { id: resourceId },
        data: {
          amenities: {
            set: [] // Disconnect all amenities
          }
        }
      });

      // Finally delete the resource
      await tx.resource.delete({
        where: { id: resourceId }
      });
    });

    return NextResponse.json({
      success: true,
      message: 'Resource deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting resource:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `DELETE /api/resources/${(await params).id}`,
      resourceId: (await params).id
    });
    
    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2003':
          return NextResponse.json(
            { 
              success: false,
              error: 'Cannot delete resource: resource has existing bookings',
              message: 'This resource cannot be deleted because it has existing bookings. Please cancel or complete these bookings first.'
            },
            { status: 409 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Resource not found',
              message: 'The resource you are trying to delete no longer exists.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while deleting the resource. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}