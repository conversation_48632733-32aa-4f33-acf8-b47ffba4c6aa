import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cateringSearchSchema, cateringCreateSchema } from '@/lib/validations/catering';
import { Prisma } from '@prisma/client';

export async function GET(request: NextRequest) {
  console.log('GET /api/catering - Starting request');
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters with defaults and basic validation
    const searchQuery = searchParams.get('search') || '';
    const page = Math.max(1, parseInt(searchParams.get('page') || '1') || 1);
    const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '10') || 10));

    // Simple validation without Zod for now
    const validatedParams = {
      search: searchQuery,
      page,
      limit
    };

    console.log('Validated params:', validatedParams);

    // Build where clause for search
    const where: Prisma.CateringWhereInput = {};
    
    if (validatedParams.search) {
      // For SQLite, we'll use contains which is case-insensitive by default in most cases
      // For more robust case-insensitive search, we could use raw SQL, but contains should work fine
      where.offerName = { contains: validatedParams.search };
    }

    console.log('Where clause:', JSON.stringify(where, null, 2));

    // Calculate pagination
    const skip = (validatedParams.page - 1) * validatedParams.limit;
    console.log('Pagination - skip:', skip, 'take:', validatedParams.limit);

    // Get total count for pagination metadata
    console.log('Getting total count...');
    const totalCount = await prisma.catering.count({ where });
    console.log('Total count:', totalCount);

    // Fetch catering offers with pagination
    console.log('Fetching catering offers...');
    const catering = await prisma.catering.findMany({
      where,
      select: {
        id: true,
        offerName: true,
        pricePerPerson: true,
        firstPartyShare: true,
        vendorShare: true,
        createdAt: true,
        updatedAt: true,
        createdById: true,
        updatedById: true,
        _count: {
          select: {
            bookings: true
          }
        }
      },
      orderBy: [
        { createdAt: 'desc' },
        { id: 'desc' }
      ],
      skip,
      take: validatedParams.limit
    });
    console.log('Fetched catering offers:', catering.length);

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / validatedParams.limit);
    const hasNextPage = validatedParams.page < totalPages;
    const hasPreviousPage = validatedParams.page > 1;

    return NextResponse.json({
      success: true,
      data: {
        data: catering,
        pagination: {
          page: validatedParams.page,
          limit: validatedParams.limit,
          total: totalCount,
          totalPages,
          hasNext: hasNextPage,
          hasPrev: hasPreviousPage
        }
      }
    });
  } catch (error) {
    console.error('Error fetching catering offers:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: 'GET /api/catering'
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid query parameters',
          message: 'Please check your search parameters and try again.',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Handle database connection errors
    if (error instanceof Error && error.message.includes('database')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    // Handle timeout errors
    if (error instanceof Error && error.message.includes('timeout')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Request timeout',
          message: 'The request took too long to complete. Please try again.'
        },
        { status: 408 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching catering offers. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  console.log('POST /api/catering - Starting catering offer creation');
  let body: any = null;
  
  try {
    body = await request.json();
    console.log('Request body:', JSON.stringify(body, null, 2));
    
    // Validate input data
    console.log('Validating input data...');
    const validatedData = cateringCreateSchema.parse(body);
    console.log('Validation successful:', JSON.stringify(validatedData, null, 2));

    // Create the catering offer
    console.log('Creating catering offer in database...');
    const catering = await prisma.catering.create({
      data: {
        offerName: validatedData.offerName,
        pricePerPerson: validatedData.pricePerPerson,
        firstPartyShare: validatedData.firstPartyShare,
        vendorShare: validatedData.vendorShare,
        // TODO: Add createdById from authenticated user session
      },
      select: {
        id: true,
        offerName: true,
        pricePerPerson: true,
        firstPartyShare: true,
        vendorShare: true,
        createdAt: true,
        updatedAt: true,
        createdById: true,
        updatedById: true,
        _count: {
          select: {
            bookings: true
          }
        }
      }
    });
    console.log('Catering offer created successfully:', JSON.stringify(catering, null, 2));

    return NextResponse.json({
      success: true,
      data: catering
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating catering offer:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: 'POST /api/catering',
      requestBody: body
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid input data',
          message: 'Please check the catering offer information and try again.',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2002':
          return NextResponse.json(
            { 
              success: false,
              error: 'Catering offer name already exists',
              message: 'This catering offer name is already taken. Please choose a different one.',
              field: 'offerName'
            },
            { status: 409 }
          );
        case 'P2003':
          return NextResponse.json(
            { 
              success: false,
              error: 'Invalid reference',
              message: 'Invalid data reference. Please check your input.'
            },
            { status: 400 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Record not found',
              message: 'Required record not found in database.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    // Handle timeout errors
    if (error instanceof Error && error.message.includes('timeout')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Request timeout',
          message: 'The request took too long to complete. Please try again.'
        },
        { status: 408 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while creating the catering offer. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}