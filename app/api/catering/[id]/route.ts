import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cateringUpdateSchema } from '@/lib/validations/catering';
import { Prisma } from '@prisma/client';

interface RouteParams {
  params: Promise<{
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const cateringId = parseInt((await params).id);
    
    if (isNaN(cateringId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid catering ID',
          message: 'Catering ID must be a valid number'
        },
        { status: 400 }
      );
    }

    const catering = await prisma.catering.findUnique({
      where: { id: cateringId },
      select: {
        id: true,
        offerName: true,
        pricePerPerson: true,
        firstPartyShare: true,
        vendorShare: true,
        createdAt: true,
        updatedAt: true,
        createdById: true,
        updatedById: true,
        _count: {
          select: {
            bookings: true
          }
        }
      }
    });

    if (!catering) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Catering offer not found',
          message: 'The requested catering offer does not exist'
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: catering
    });
  } catch (error) {
    console.error('Error fetching catering offer:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `GET /api/catering/${(await params).id}`,
      cateringId: (await params).id
    });

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching the catering offer. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  let body: any = null;
  
  try {
    const cateringId = parseInt((await params).id);
    
    if (isNaN(cateringId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid catering ID',
          message: 'Catering ID must be a valid number'
        },
        { status: 400 }
      );
    }

    body = await request.json();
    
    // Validate input data
    const validatedData = cateringUpdateSchema.parse(body);

    // Check if catering offer exists
    const existingCatering = await prisma.catering.findUnique({
      where: { id: cateringId }
    });

    if (!existingCatering) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Catering offer not found',
          message: 'The requested catering offer does not exist'
        },
        { status: 404 }
      );
    }

    // Update the catering offer
    const updatedCatering = await prisma.catering.update({
      where: { id: cateringId },
      data: {
        offerName: validatedData.offerName,
        pricePerPerson: validatedData.pricePerPerson,
        firstPartyShare: validatedData.firstPartyShare,
        vendorShare: validatedData.vendorShare,
        updatedAt: new Date(),
        // TODO: Add updatedById from authenticated user session
      },
      select: {
        id: true,
        offerName: true,
        pricePerPerson: true,
        firstPartyShare: true,
        vendorShare: true,
        createdAt: true,
        updatedAt: true,
        createdById: true,
        updatedById: true,
        _count: {
          select: {
            bookings: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedCatering
    });
  } catch (error) {
    console.error('Error updating catering offer:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `PUT /api/catering/${(await params).id}`,
      cateringId: (await params).id,
      requestBody: body
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid input data',
          message: 'Please check the catering offer information and try again.',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2002':
          return NextResponse.json(
            { 
              success: false,
              error: 'Catering offer name already exists',
              message: 'This catering offer name is already taken. Please choose a different one.',
              field: 'offerName'
            },
            { status: 409 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Catering offer not found',
              message: 'The catering offer you are trying to update no longer exists.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while updating the catering offer. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const cateringId = parseInt((await params).id);
    
    if (isNaN(cateringId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid catering ID',
          message: 'Catering ID must be a valid number'
        },
        { status: 400 }
      );
    }

    // Check if catering offer exists
    const existingCatering = await prisma.catering.findUnique({
      where: { id: cateringId },
      include: {
        _count: {
          select: {
            bookings: true
          }
        }
      }
    });

    if (!existingCatering) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Catering offer not found',
          message: 'The requested catering offer does not exist'
        },
        { status: 404 }
      );
    }

    // Check for booking associations to prevent deletion
    if (existingCatering._count.bookings > 0) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Cannot delete catering offer: catering offer has associated bookings',
          message: `This catering offer cannot be deleted because it is currently associated with ${existingCatering._count.bookings} booking(s). Please remove these associations first.`,
          bookingCount: existingCatering._count.bookings
        },
        { status: 409 }
      );
    }

    // Delete the catering offer
    await prisma.catering.delete({
      where: { id: cateringId }
    });

    return NextResponse.json({
      success: true,
      message: 'Catering offer deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting catering offer:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `DELETE /api/catering/${(await params).id}`,
      cateringId: (await params).id
    });
    
    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2003':
          return NextResponse.json(
            { 
              success: false,
              error: 'Cannot delete catering offer: catering offer has associated bookings',
              message: 'This catering offer cannot be deleted because it is currently associated with one or more bookings. Please remove these associations first.'
            },
            { status: 409 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Catering offer not found',
              message: 'The catering offer you are trying to delete no longer exists.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while deleting the catering offer. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}