import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { userUpdateSchema } from '@/lib/validations/user';
import { Prisma } from '@prisma/client';

interface RouteParams {
  params: Promise<{
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;
    const userId = parseInt(id);
    
    if (isNaN(userId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid user ID',
          message: 'User ID must be a valid number'
        },
        { status: 400 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        username: true,
        role: true,
        isSuperUser: true,
        createdAt: true,
        updatedAt: true,
        createdById: true,
        updatedById: true
      }
    });

    if (!user) {
      return NextResponse.json(
        { 
          success: false,
          error: 'User not found',
          message: 'The requested user does not exist'
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching the user'
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;
    const userId = parseInt(id);
    
    if (isNaN(userId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid user ID',
          message: 'User ID must be a valid number'
        },
        { status: 400 }
      );
    }

    const body = await request.json();
    
    // Validate input data
    const validatedData = userUpdateSchema.parse(body);

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!existingUser) {
      return NextResponse.json(
        { 
          success: false,
          error: 'User not found',
          message: 'The requested user does not exist'
        },
        { status: 404 }
      );
    }

    // Update the user
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
        username: validatedData.username,
        role: validatedData.role,
        isSuperUser: validatedData.isSuperUser,
        updatedAt: new Date(),
        // TODO: Add updatedById from authenticated user session
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        username: true,
        role: true,
        isSuperUser: true,
        createdAt: true,
        updatedAt: true,
        createdById: true,
        updatedById: true
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedUser
    });
  } catch (error) {
    console.error('Error updating user:', error);
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid input data',
          message: error.message
        },
        { status: 400 }
      );
    }

    // Handle unique constraint violations
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === 'P2002') {
        return NextResponse.json(
          { 
            success: false,
            error: 'Username already exists',
            message: 'This username is already taken. Please choose a different one.'
          },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while updating the user'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const userId = parseInt((await params).id);
    
    if (isNaN(userId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid user ID',
          message: 'User ID must be a valid number'
        },
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!existingUser) {
      return NextResponse.json(
        { 
          success: false,
          error: 'User not found',
          message: 'The requested user does not exist'
        },
        { status: 404 }
      );
    }

    // Delete the user
    await prisma.user.delete({
      where: { id: userId }
    });

    return NextResponse.json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting user:', error);
    
    // Handle foreign key constraint violations
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === 'P2003') {
        return NextResponse.json(
          { 
            success: false,
            error: 'Cannot delete user: user has associated records',
            message: 'This user cannot be deleted because they have associated data in the system'
          },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while deleting the user'
      },
      { status: 500 }
    );
  }
}