import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { passwordChangeSchema } from '@/lib/validations/user';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const userId = parseInt((await params).id);
    
    if (isNaN(userId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid user ID',
          message: 'User ID must be a valid number'
        },
        { status: 400 }
      );
    }

    const body = await request.json();
    
    // For password change, we only need the password field
    const passwordData = { password: body.password };
    
    // Validate input data - just validate the password field
    if (!passwordData.password || typeof passwordData.password !== 'string') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid password',
          message: 'Password is required and must be a string'
        },
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, username: true }
    });

    if (!existingUser) {
      return NextResponse.json(
        { 
          success: false,
          error: 'User not found',
          message: 'The requested user does not exist'
        },
        { status: 404 }
      );
    }

    // Temporary workaround: use a simple hash instead of bcrypt
    // TODO: Replace with proper bcrypt hashing once bcrypt is fixed
    const crypto = await import('crypto');
    const hashedPassword = crypto.createHash('sha256').update(passwordData.password).digest('hex');

    // Update the user's password
    await prisma.user.update({
      where: { id: userId },
      data: {
        password: hashedPassword,
        updatedAt: new Date(),
        // TODO: Add updatedById from authenticated user session
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Password updated successfully',
      data: {
        username: existingUser.username
      }
    });
  } catch (error) {
    console.error('Error updating password:', error);
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid input data',
          message: error.message
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while updating the password'
      },
      { status: 500 }
    );
  }
}