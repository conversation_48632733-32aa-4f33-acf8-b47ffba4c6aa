import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    console.log('Testing database connection...');
    
    // Simple database connection test
    const userCount = await prisma.user.count();
    console.log('User count:', userCount);
    
    // Test basic user query
    const users = await prisma.user.findMany({
      take: 1,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        username: true,
        role: true,
        isSuperUser: true,
        createdAt: true,
        updatedAt: true
      }
    });
    
    console.log('Sample users:', users);

    // Test user creation with minimal data
    console.log('Testing user creation...');
    try {
      const crypto = await import('crypto');
      const testUser = await prisma.user.create({
        data: {
          firstName: 'Test',
          lastName: 'User',
          username: `test_${Date.now()}`,
          password: crypto.createHash('sha256').update('testpass').digest('hex'),
          role: 'ADMIN',
          isSuperUser: false
        },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          username: true,
          role: true,
          isSuperUser: true,
          createdAt: true,
          updatedAt: true
        }
      });
      console.log('Test user created:', testUser);
      
      // Clean up test user
      await prisma.user.delete({ where: { id: testUser.id } });
      console.log('Test user deleted');
      
      return NextResponse.json({
        success: true,
        data: {
          userCount,
          sampleUsers: users,
          testUserCreation: 'Success',
          message: 'Database connection and user creation successful'
        }
      });
    } catch (createError) {
      console.error('User creation test failed:', createError);
      return NextResponse.json({
        success: false,
        data: {
          userCount,
          sampleUsers: users,
          testUserCreation: 'Failed',
          createError: createError instanceof Error ? createError.message : String(createError)
        },
        error: 'User creation test failed',
        message: 'Database connection works but user creation failed'
      });
    }
  } catch (error) {
    console.error('Database test error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Database connection failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}