import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

/**
 * End-to-end workflow test API endpoint
 * Tests the complete customer creation to invoice payment workflow
 */
export async function POST(request: NextRequest) {
  try {
    const testResults = {
      customerCreation: false,
      bookingCreation: false,
      invoiceGeneration: false,
      paymentRecording: false,
      calendarIntegration: false,
      errors: [] as string[]
    };

    // Step 1: Create a test customer
    console.log('Step 1: Creating test customer...');
    let testCustomer;
    try {
      const timestamp = Date.now();
      testCustomer = await prisma.customer.create({
        data: {
          name: 'E2E Test Customer',
          email: `e2e-test-${timestamp}@example.com`,
          phoneNumber: '+1234567890',
          companyName: 'E2E Test Company',
          specialization: 'Software Testing',
          industry: 'Technology',
          website: 'https://e2e-test.com',
          notes: 'Created for end-to-end workflow testing'
        }
      });
      testResults.customerCreation = true;
      console.log('✓ Customer created successfully:', testCustomer.id);
    } catch (error) {
      testResults.errors.push(`Customer creation failed: ${error}`);
      console.error('✗ Customer creation failed:', error);
    }

    if (!testCustomer) {
      return NextResponse.json({
        success: false,
        error: 'Failed to create test customer',
        results: testResults
      }, { status: 500 });
    }

    // Step 2: Get available resources and catering for booking
    const resources = await prisma.resource.findMany({
      take: 2 // Get first 2 resources for testing
    });

    const caterings = await prisma.catering.findMany({
      take: 1 // Get first catering for testing
    });

    if (resources.length === 0) {
      testResults.errors.push('No resources available for booking test');
    }

    // Step 3: Create a test booking
    console.log('Step 2: Creating test booking...');
    let testBooking;
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() + 7); // Book for next week
      const endDate = new Date(startDate);
      endDate.setHours(endDate.getHours() + 2); // 2-hour booking

      testBooking = await prisma.booking.create({
        data: {
          customerId: testCustomer.id,
          status: 'CONFIRMED',
          start: startDate,
          end: endDate,
          resources: {
            connect: resources.map(r => ({ id: r.id }))
          },
          caterings: caterings.length > 0 ? {
            create: [{
              cateringId: caterings[0].id,
              quantity: 10
            }]
          } : undefined
        },
        include: {
          customer: true,
          resources: true,
          caterings: {
            include: {
              catering: true
            }
          }
        }
      });
      testResults.bookingCreation = true;
      console.log('✓ Booking created successfully:', testBooking.id);
    } catch (error) {
      testResults.errors.push(`Booking creation failed: ${error}`);
      console.error('✗ Booking creation failed:', error);
    }

    if (!testBooking) {
      return NextResponse.json({
        success: false,
        error: 'Failed to create test booking',
        results: testResults
      }, { status: 500 });
    }

    // Step 4: Test one-click invoice generation
    console.log('Step 3: Testing one-click invoice generation...');
    let testInvoice;
    try {
      // Create line items from booking resources
      const lineItems = [];
      
      // Add resource line items
      for (const resource of testBooking.resources) {
        lineItems.push({
          description: `Resource: ${resource.name}`,
          amount: resource.basePrice || 100, // Use basePrice instead of pricePerHour
          qty: 1,
          isCatering: false
        });
      }

      // Add catering line items
      for (const cateringBooking of testBooking.caterings) {
        lineItems.push({
          description: `Catering: ${cateringBooking.catering.offerName}`,
          amount: cateringBooking.catering.pricePerPerson || 25,
          qty: cateringBooking.quantity,
          isCatering: true,
          cateringId: cateringBooking.catering.id
        });
      }

      const total = lineItems.reduce((sum, item) => sum + (item.amount * item.qty), 0);

      testInvoice = await prisma.invoice.create({
        data: {
          bookingId: testBooking.id,
          status: 'PENDING',
          total: total,
          paid: 0,
          lineItems: {
            create: lineItems
          }
        },
        include: {
          booking: {
            include: {
              customer: true
            }
          },
          lineItems: true,
          payments: true
        }
      });
      testResults.invoiceGeneration = true;
      console.log('✓ Invoice generated successfully:', testInvoice.id);
    } catch (error) {
      testResults.errors.push(`Invoice generation failed: ${error}`);
      console.error('✗ Invoice generation failed:', error);
    }

    if (!testInvoice) {
      return NextResponse.json({
        success: false,
        error: 'Failed to generate test invoice',
        results: testResults
      }, { status: 500 });
    }

    // Step 5: Test payment recording
    console.log('Step 4: Testing payment recording...');
    try {
      const paymentAmount = testInvoice.total / 2; // Partial payment

      const payment = await prisma.payment.create({
        data: {
          invoiceId: testInvoice.id,
          amount: paymentAmount,
          method: 'CREDIT_CARD',
          status: 'COMPLETED',
          reference: 'E2E-TEST-PAYMENT-001',
          notes: 'Test payment for E2E workflow',
          paidAt: new Date()
        }
      });

      // Update invoice paid amount and status
      const updatedInvoice = await prisma.invoice.update({
        where: { id: testInvoice.id },
        data: {
          paid: paymentAmount,
          status: paymentAmount >= testInvoice.total ? 'PAID' : 'PARTIALLY_PAID'
        }
      });

      testResults.paymentRecording = true;
      console.log('✓ Payment recorded successfully:', payment.id);
    } catch (error) {
      testResults.errors.push(`Payment recording failed: ${error}`);
      console.error('✗ Payment recording failed:', error);
    }

    // Step 6: Test calendar integration data format
    console.log('Step 5: Testing calendar integration...');
    try {
      // Verify booking can be formatted for calendar display
      const calendarEvent = {
        id: testBooking.id.toString(),
        title: `${testBooking.customer.name} - ${testBooking.resources.map(r => r.name).join(', ')}`,
        start: testBooking.start,
        end: testBooking.end,
        backgroundColor: testBooking.status === 'CONFIRMED' ? '#10b981' : '#f59e0b',
        extendedProps: {
          bookingId: testBooking.id,
          customerName: testBooking.customer.name,
          status: testBooking.status,
          resourceNames: testBooking.resources.map(r => r.name)
        }
      };

      // Validate calendar event structure
      if (calendarEvent.id && calendarEvent.title && calendarEvent.start && calendarEvent.end) {
        testResults.calendarIntegration = true;
        console.log('✓ Calendar integration format validated');
      } else {
        testResults.errors.push('Calendar event format validation failed');
      }
    } catch (error) {
      testResults.errors.push(`Calendar integration test failed: ${error}`);
      console.error('✗ Calendar integration test failed:', error);
    }

    // Cleanup: Remove test data
    console.log('Cleaning up test data...');
    try {
      // Delete in reverse order due to foreign key constraints
      await prisma.payment.deleteMany({
        where: { invoiceId: testInvoice.id }
      });

      await prisma.lineItem.deleteMany({
        where: { invoiceId: testInvoice.id }
      });

      await prisma.invoice.delete({
        where: { id: testInvoice.id }
      });

      await prisma.cateringOnBooking.deleteMany({
        where: { bookingId: testBooking.id }
      });

      await prisma.booking.delete({
        where: { id: testBooking.id }
      });

      await prisma.customer.delete({
        where: { id: testCustomer.id }
      });

      console.log('✓ Test data cleaned up successfully');
    } catch (error) {
      console.error('⚠ Warning: Failed to clean up test data:', error);
      testResults.errors.push(`Cleanup failed: ${error}`);
    }

    // Calculate overall success
    const allTestsPassed = testResults.customerCreation && 
                          testResults.bookingCreation && 
                          testResults.invoiceGeneration && 
                          testResults.paymentRecording && 
                          testResults.calendarIntegration;

    return NextResponse.json({
      success: allTestsPassed,
      message: allTestsPassed ? 'All end-to-end workflow tests passed!' : 'Some tests failed',
      results: testResults,
      summary: {
        passed: Object.values(testResults).filter(v => v === true).length - 1, // -1 for errors array
        total: 5,
        errors: testResults.errors.length
      }
    });

  } catch (error) {
    console.error('E2E workflow test failed:', error);
    return NextResponse.json({
      success: false,
      error: 'End-to-end workflow test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}