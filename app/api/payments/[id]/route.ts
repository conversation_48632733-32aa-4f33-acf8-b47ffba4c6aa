import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { paymentUpdateSchema, calculateInvoiceStatus } from '@/lib/validations/invoice';
import { Prisma } from '@prisma/client';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const paymentId = parseInt((await params).id);
    
    if (isNaN(paymentId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid payment ID',
          message: 'Payment ID must be a valid number'
        },
        { status: 400 }
      );
    }

    const payment = await prisma.payment.findUnique({
      where: { id: paymentId },
      select: {
        id: true,
        invoiceId: true,
        amount: true,
        method: true,
        status: true,
        reference: true,
        notes: true,
        paidAt: true,
        createdAt: true,
        updatedAt: true,
        createdById: true,
        updatedById: true,
        invoice: {
          select: {
            id: true,
            total: true,
            paid: true,
            status: true,
            booking: {
              select: {
                customer: {
                  select: {
                    name: true
                  }
                }
              }
            }
          }
        }
      }
    });

    if (!payment) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Payment not found',
          message: 'The requested payment does not exist'
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: payment
    });
  } catch (error) {
    console.error('Error fetching payment:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `GET /api/payments/${(await params).id}`,
      paymentId: (await params).id
    });

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching the payment. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  let body: any = null;
  
  try {
    const paymentId = parseInt((await params).id);
    
    if (isNaN(paymentId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid payment ID',
          message: 'Payment ID must be a valid number'
        },
        { status: 400 }
      );
    }

    body = await request.json();
    
    // Validate input data
    const validatedData = paymentUpdateSchema.parse(body);

    // Get existing payment with invoice information
    const existingPayment = await prisma.payment.findUnique({
      where: { id: paymentId },
      select: {
        id: true,
        invoiceId: true,
        amount: true,
        method: true,
        status: true,
        invoice: {
          select: {
            id: true,
            total: true,
            paid: true,
            payments: {
              where: {
                status: 'COMPLETED',
                id: { not: paymentId } // Exclude current payment from calculation
              },
              select: {
                amount: true
              }
            }
          }
        }
      }
    });

    if (!existingPayment) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Payment not found',
          message: 'The requested payment does not exist'
        },
        { status: 404 }
      );
    }

    // Calculate current paid amount excluding this payment
    const otherPaymentsTotal = existingPayment.invoice.payments.reduce((sum, payment) => {
      return sum + Number(payment.amount);
    }, 0);

    // Validate new payment amount doesn't exceed invoice total
    const newTotalPaid = otherPaymentsTotal + validatedData.amount;
    if (newTotalPaid > Number(existingPayment.invoice.total)) {
      const maxAllowed = Number(existingPayment.invoice.total) - otherPaymentsTotal;
      return NextResponse.json(
        { 
          success: false,
          error: 'Payment amount too high',
          message: `Payment amount cannot exceed ${maxAllowed.toFixed(2)} IQD (remaining invoice balance)`
        },
        { status: 400 }
      );
    }

    // Update payment and recalculate invoice status in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update the payment
      const updatedPayment = await tx.payment.update({
        where: { id: paymentId },
        data: {
          amount: validatedData.amount,
          method: validatedData.method,
          status: validatedData.status,
          reference: validatedData.reference,
          notes: validatedData.notes,
          paidAt: validatedData.paidAt,
          updatedAt: new Date(),
          // TODO: Add updatedById from authenticated user session
        },
        select: {
          id: true,
          amount: true,
          method: true,
          status: true,
          reference: true,
          notes: true,
          paidAt: true,
          createdAt: true,
          updatedAt: true,
          createdById: true,
          updatedById: true
        }
      });

      // Recalculate invoice paid amount and status
      const completedPayments = await tx.payment.findMany({
        where: {
          invoiceId: existingPayment.invoiceId,
          status: 'COMPLETED'
        },
        select: {
          amount: true
        }
      });

      const totalPaid = completedPayments.reduce((sum, payment) => {
        return sum + Number(payment.amount);
      }, 0);

      const newStatus = calculateInvoiceStatus(Number(existingPayment.invoice.total), totalPaid);

      // Update invoice with recalculated values
      await tx.invoice.update({
        where: { id: existingPayment.invoiceId },
        data: {
          paid: totalPaid,
          status: newStatus,
          updatedAt: new Date(),
          // TODO: Add updatedById from authenticated user session
        }
      });

      return updatedPayment;
    });

    return NextResponse.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Error updating payment:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `PUT /api/payments/${(await params).id}`,
      paymentId: (await params).id,
      requestBody: body
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError' || (error && typeof error === 'object' && 'name' in error && error.name === 'ZodError')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid input data',
          message: 'Please check the payment information and try again.',
          details: 'errors' in error ? error.errors : []
        },
        { status: 400 }
      );
    }

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Payment not found',
              message: 'The payment you are trying to update no longer exists.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while updating the payment. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const paymentId = parseInt((await params).id);
    
    if (isNaN(paymentId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid payment ID',
          message: 'Payment ID must be a valid number'
        },
        { status: 400 }
      );
    }

    // Get payment with invoice information
    const payment = await prisma.payment.findUnique({
      where: { id: paymentId },
      select: {
        id: true,
        invoiceId: true,
        amount: true,
        invoice: {
          select: {
            id: true,
            total: true,
            paid: true
          }
        }
      }
    });

    if (!payment) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Payment not found',
          message: 'The requested payment does not exist'
        },
        { status: 404 }
      );
    }

    // Delete payment and update invoice in a transaction
    await prisma.$transaction(async (tx) => {
      // Delete the payment
      await tx.payment.delete({
        where: { id: paymentId }
      });

      // Recalculate invoice paid amount and status
      const remainingPayments = await tx.payment.findMany({
        where: {
          invoiceId: payment.invoiceId,
          status: 'COMPLETED'
        },
        select: {
          amount: true
        }
      });

      const totalPaid = remainingPayments.reduce((sum, p) => {
        return sum + Number(p.amount);
      }, 0);

      const newStatus = calculateInvoiceStatus(Number(payment.invoice.total), totalPaid);

      // Update invoice with recalculated values
      await tx.invoice.update({
        where: { id: payment.invoiceId },
        data: {
          paid: totalPaid,
          status: newStatus,
          updatedAt: new Date(),
          // TODO: Add updatedById from authenticated user session
        }
      });
    });

    return NextResponse.json({
      success: true,
      message: 'Payment deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting payment:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `DELETE /api/payments/${(await params).id}`,
      paymentId: (await params).id
    });

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Payment not found',
              message: 'The payment you are trying to delete no longer exists.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while deleting the payment. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}