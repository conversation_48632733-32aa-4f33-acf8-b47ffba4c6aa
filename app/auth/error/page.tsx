'use client'

import { Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { <PERSON>ert<PERSON>riangle, ArrowLeft, Home } from 'lucide-react'
import Link from 'next/link'

const errorMessages: Record<string, { title: string; description: string }> = {
  Configuration: {
    title: 'Server Configuration Error',
    description: 'There is a problem with the server configuration. Please contact support.'
  },
  AccessDenied: {
    title: 'Access Denied',
    description: 'You do not have permission to sign in with this account.'
  },
  Verification: {
    title: 'Verification Error',
    description: 'The verification token has expired or is invalid. Please try signing in again.'
  },
  Default: {
    title: 'Authentication Error',
    description: 'An error occurred during authentication. Please try again.'
  },
  Signin: {
    title: 'Sign In Error',
    description: 'There was a problem signing you in. Please check your credentials and try again.'
  },
  OAuthSignin: {
    title: 'OAuth Sign In Error',
    description: 'There was a problem with the OAuth provider. Please try again.'
  },
  OAuthCallback: {
    title: 'OAuth Callback Error',
    description: 'There was a problem processing the OAuth callback. Please try signing in again.'
  },
  OAuthCreateAccount: {
    title: 'OAuth Account Creation Error',
    description: 'Could not create an account with the OAuth provider. Please try again.'
  },
  EmailCreateAccount: {
    title: 'Email Account Creation Error',
    description: 'Could not create an account with this email. Please try again.'
  },
  Callback: {
    title: 'Callback Error',
    description: 'There was a problem with the authentication callback. Please try again.'
  },
  OAuthAccountNotLinked: {
    title: 'Account Not Linked',
    description: 'This account is not linked to your profile. Please sign in with your original method.'
  },
  EmailSignin: {
    title: 'Email Sign In Error',
    description: 'There was a problem sending the sign in email. Please try again.'
  },
  CredentialsSignin: {
    title: 'Invalid Credentials',
    description: 'The credentials you provided are incorrect. Please check and try again.'
  },
  SessionRequired: {
    title: 'Session Required',
    description: 'You must be signed in to access this page.'
  }
}

function AuthErrorContent() {
  const searchParams = useSearchParams()
  const error = searchParams.get('error') || 'Default'
  
  const errorInfo = errorMessages[error] || errorMessages.Default

  return (
    <Card>
        <CardHeader className="space-y-1 text-center">
          <div className="flex justify-center mb-4">
            <AlertTriangle className="h-12 w-12 text-red-500" />
          </div>
          <CardTitle className="text-2xl font-bold text-red-600">
            {errorInfo.title}
          </CardTitle>
          <CardDescription>
            {errorInfo.description}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Error Code: {error}
            </AlertDescription>
          </Alert>
          
          <div className="flex flex-col space-y-3">
            <Button asChild className="w-full">
              <Link href="/auth/login">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Try Again
              </Link>
            </Button>
            
            <Button variant="outline" asChild className="w-full">
              <Link href="/">
                <Home className="mr-2 h-4 w-4" />
                Go Home
              </Link>
            </Button>
          </div>
          
          <div className="text-sm text-center text-gray-600">
            If this problem persists, please{' '}
            <Link href="/contact" className="text-blue-600 hover:underline">
              contact support
            </Link>
          </div>
        </CardContent>
      </Card>
  )
}

export default function AuthErrorPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Suspense fallback={
        <Card>
          <CardHeader className="space-y-1 text-center">
            <div className="flex justify-center mb-4">
              <AlertTriangle className="h-12 w-12 text-red-500" />
            </div>
            <CardTitle className="text-2xl font-bold text-red-600">
              Loading...
            </CardTitle>
          </CardHeader>
        </Card>
      }>
        <AuthErrorContent />
      </Suspense>
    </div>
  )
}