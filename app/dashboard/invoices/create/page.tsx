"use client";

import { useState, useCallback, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Receipt, CreditCard } from "lucide-react";
import { InvoiceInformationForm } from "../components/invoice-information-form";
import { useInvoices } from "@/hooks/use-invoices";
import { useBookings } from "@/hooks/use-bookings";
import { useCatering } from "@/hooks/use-catering";
import { InvoiceFormData } from "@/lib/types";
import { toast } from "@/hooks/use-toast";

export default function CreateInvoicePage() {
  const router = useRouter();
  const { createInvoice } = useInvoices();
  const { bookings, fetchBookings } = useBookings();
  const { catering: caterings, fetchCatering } = useCatering();
  const [loading, setLoading] = useState(false);

  // Fetch required data on mount
  useEffect(() => {
    fetchBookings();
    fetchCatering();
  }, [fetchBookings, fetchCatering]);

  const handleSubmit = useCallback(async (data: InvoiceFormData) => {
    setLoading(true);
    try {
      await createInvoice(data);
      toast({
        title: "Success",
        description: "Invoice created successfully.",
      });
      router.push("/dashboard/invoices");
    } catch (error) {
      console.error("Error creating invoice:", error);
      // Error is already handled by the form component
    } finally {
      setLoading(false);
    }
  }, [createInvoice, router]);

  const handleCancel = useCallback(() => {
    router.push("/dashboard/invoices");
  }, [router]);

  return (
    <div className="h-full bg-gray-50 p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 space-y-4 sm:space-y-0">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
            className="min-h-[44px] sm:min-h-auto"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Invoices
          </Button>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Create Invoice</h1>
            <p className="text-gray-600 mt-1 text-sm sm:text-base">Generate a new invoice for a booking</p>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Receipt className="mr-2 h-5 w-5" />
            Invoice Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="invoice-details" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="invoice-details" className="flex items-center">
                <Receipt className="mr-2 h-4 w-4" />
                Invoice Details
              </TabsTrigger>
              <TabsTrigger value="payments" className="flex items-center" disabled>
                <CreditCard className="mr-2 h-4 w-4" />
                Payments
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="invoice-details" className="mt-6">
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Fill in the invoice details below. The payments tab will be available after the invoice is created.
                </p>
                <InvoiceInformationForm
                  onSubmit={handleSubmit}
                  onCancel={handleCancel}
                  loading={loading}
                  bookings={bookings}
                  caterings={caterings}
                  submitButtonText="Create Invoice"
                />
              </div>
            </TabsContent>
            
            <TabsContent value="payments" className="mt-6">
              <div className="text-center py-8 text-muted-foreground">
                <CreditCard className="mx-auto h-12 w-12 mb-4 opacity-50" />
                <p>Payments will be available after the invoice is created.</p>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}