"use client";

import { useState, useCallback, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowLeft, AlertCircle, Receipt, CreditCard } from "lucide-react";
import { InvoiceInformationForm } from "../components/invoice-information-form";
import { InvoicePaymentsTab } from "../components/invoice-payments-tab";
import { CateringRevenueBreakdown } from "../components/catering-revenue-breakdown";
import { useInvoices } from "@/hooks/use-invoices";
import { useBookings } from "@/hooks/use-bookings";
import { useCatering } from "@/hooks/use-catering";
import { Invoice, InvoiceFormData } from "@/lib/types";
import { toast } from "@/hooks/use-toast";

export default function EditInvoicePage() {
  const router = useRouter();
  const params = useParams();
  const invoiceId = parseInt(params.id as string);
  const { updateInvoice } = useInvoices();
  const { bookings, fetchBookings } = useBookings();
  const { catering: caterings, fetchCatering } = useCatering();
  
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string>("");
  const [activeTab, setActiveTab] = useState("invoice-details");

  // Fetch invoice data
  const fetchInvoice = useCallback(async () => {
    try {
      setLoading(true);
      setError("");
      
      const response = await fetch(`/api/invoices/${invoiceId}`);
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Invoice not found");
        }
        throw new Error("Failed to fetch invoice");
      }
      
      const result = await response.json();
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to fetch invoice");
      }
      
      setInvoice(result.data);
    } catch (error) {
      console.error("Error fetching invoice:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch invoice";
      setError(errorMessage);
      
      if (errorMessage === "Invoice not found") {
        toast({
          title: "Invoice Not Found",
          description: "The invoice you're looking for doesn't exist or may have been deleted.",
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  }, [invoiceId]);

  // Fetch required data on mount
  useEffect(() => {
    if (invoiceId && !isNaN(invoiceId)) {
      fetchInvoice();
      fetchBookings();
      fetchCatering();
    } else {
      setError("Invalid invoice ID");
      setLoading(false);
    }
  }, [invoiceId, fetchInvoice, fetchBookings, fetchCatering]);

  const handleSubmit = useCallback(async (data: InvoiceFormData) => {
    if (!invoice) return;
    
    setUpdating(true);
    try {
      const updatedInvoice = await updateInvoice(invoice.id, data);
      setInvoice(updatedInvoice);
      toast({
        title: "Success",
        description: "Invoice updated successfully.",
      });
    } catch (error) {
      console.error("Error updating invoice:", error);
      // Error is already handled by the form component
    } finally {
      setUpdating(false);
    }
  }, [invoice, updateInvoice]);

  const handleCancel = useCallback(() => {
    router.push("/dashboard/invoices");
  }, [router]);

  const handleRetry = useCallback(() => {
    fetchInvoice();
  }, [fetchInvoice]);

  const handlePaymentChange = useCallback(() => {
    // Refresh invoice data when payments change
    fetchInvoice();
  }, [fetchInvoice]);

  if (loading) {
    return (
      <div className="h-full bg-gray-50 p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            <Skeleton className="h-10 w-32" />
            <div>
              <Skeleton className="h-8 w-48 mb-2" />
              <Skeleton className="h-4 w-64" />
            </div>
          </div>
        </div>

        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-40" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-20 w-full" />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-20 w-full" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full bg-gray-50 p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={handleCancel}
              className="min-h-[44px] sm:min-h-auto"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Invoices
            </Button>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Edit Invoice</h1>
              <p className="text-gray-600 mt-1 text-sm sm:text-base">Invoice not found</p>
            </div>
          </div>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRetry}
              className="ml-2"
            >
              Try Again
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!invoice) {
    return null;
  }

  return (
    <div className="h-full bg-gray-50 p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 space-y-4 sm:space-y-0">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={updating}
            className="min-h-[44px] sm:min-h-auto"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Invoices
          </Button>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Edit Invoice</h1>
            <p className="text-gray-600 mt-1 text-sm sm:text-base">
              Invoice #{invoice.id} for {invoice.booking.customer.name}
            </p>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Receipt className="mr-2 h-5 w-5" />
            Invoice Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="invoice-details" className="flex items-center">
                <Receipt className="mr-2 h-4 w-4" />
                Invoice Details
              </TabsTrigger>
              <TabsTrigger value="payments" className="flex items-center">
                <CreditCard className="mr-2 h-4 w-4" />
                Payments
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="invoice-details" className="mt-6">
              <div className="space-y-6">
                <InvoiceInformationForm
                  invoice={invoice}
                  onSubmit={handleSubmit}
                  onCancel={handleCancel}
                  loading={updating}
                  bookings={bookings}
                  caterings={caterings}
                  submitButtonText="Update Invoice"
                />
                
                {/* Catering Revenue Breakdown */}
                <CateringRevenueBreakdown
                  lineItems={invoice.lineItems}
                  caterings={caterings}
                />
              </div>
            </TabsContent>
            
            <TabsContent value="payments" className="mt-6">
              <InvoicePaymentsTab
                invoiceId={invoice.id}
                invoiceTotal={invoice.total}
                invoicePaid={invoice.paid}
                onPaymentChange={handlePaymentChange}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}