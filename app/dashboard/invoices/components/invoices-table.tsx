"use client"

import { useState, memo, useMemo, useCallback } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import {
  MoreHorizontal,
  Edit,
  Trash2,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Receipt,
  User,
  Calendar,
  DollarSign,
  CreditCard,
} from "lucide-react"
import { Invoice, InvoiceStatus, INVOICE_STATUS_LABELS } from "@/lib/types"
import { format } from "date-fns"
import { formatCurrency } from "@/lib/utils/catering"
import { InvoicesTableSkeleton } from "./invoices-table-skeleton"

interface InvoicesTableProps {
  invoices: Invoice[]
  loading: boolean
  onEdit: (invoice: Invoice) => void
  onDelete: (invoice: Invoice) => void
  onRefresh: () => void
}

type SortField = 'id' | 'customerName' | 'bookingDate' | 'total' | 'paid' | 'status' | 'createdAt'
type SortDirection = 'asc' | 'desc'

interface SortConfig {
  field: SortField
  direction: SortDirection
}

const getStatusBadgeVariant = (status: InvoiceStatus) => {
  switch (status) {
    case 'PAID':
      return 'default'
    case 'PARTIALLY_PAID':
      return 'secondary'
    case 'PENDING':
      return 'outline'
    case 'CANCELLED':
      return 'destructive'
    default:
      return 'outline'
  }
}

// Mobile card component for individual invoices - memoized for performance
const InvoiceCard = memo(({ 
  invoice, 
  onEdit, 
  onDelete 
}: { 
  invoice: Invoice
  onEdit: (invoice: Invoice) => void
  onDelete: (invoice: Invoice) => void
}) => {
  const balance = Number(invoice.total) - Number(invoice.paid)
  
  return (
    <Card className="mb-4">
      <CardContent className="mobile-spacing">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-3">
              <div 
                className="flex items-center justify-center w-10 h-10 rounded-md bg-muted"
                aria-hidden="true"
              >
                <Receipt className="h-5 w-5" />
              </div>
              <div>
                <h3 className="font-medium text-base" id={`invoice-${invoice.id}`}>
                  Invoice #{invoice.id}
                </h3>
                <Badge variant={getStatusBadgeVariant(invoice.status)} className="text-xs">
                  {INVOICE_STATUS_LABELS[invoice.status]}
                </Badge>
              </div>
            </div>
            
            <div className="space-y-1 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <User className="h-3 w-3" aria-hidden="true" />
                <span className="break-all">{invoice.booking.customer.name}</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <Calendar className="h-3 w-3" aria-hidden="true" />
                <span>{format(new Date(invoice.booking.start), 'MMM dd, yyyy')}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-3 w-3" aria-hidden="true" />
                  <span className="font-medium">Total:</span>
                  <span className="font-semibold text-foreground">
                    {formatCurrency(invoice.total)}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <CreditCard className="h-3 w-3" aria-hidden="true" />
                  <span className="font-medium">Paid:</span>
                  <span className="font-semibold text-green-600">
                    {formatCurrency(invoice.paid)}
                  </span>
                </div>
              </div>
              
              {balance > 0 && (
                <div className="flex items-center space-x-2">
                  <span className="font-medium">Balance:</span>
                  <span className="font-semibold text-orange-600">
                    {formatCurrency(balance)}
                  </span>
                </div>
              )}
              
              <div className="flex items-center space-x-2">
                <span className="font-medium">Created:</span>
                <span>{format(new Date(invoice.createdAt), 'MMM dd, yyyy')}</span>
              </div>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                className="h-11 w-11 p-0 touch-target focus-enhanced"
                aria-label={`Actions for Invoice #${invoice.id}`}
              >
                <span className="sr-only">Open actions menu for Invoice #{invoice.id}</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem 
                onClick={() => onEdit(invoice)}
                className="cursor-pointer touch-target focus-enhanced"
                role="menuitem"
              >
                <Edit className="mr-2 h-4 w-4" aria-hidden="true" />
                Edit Invoice #{invoice.id}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDelete(invoice)}
                className="text-destructive cursor-pointer touch-target focus-enhanced"
                role="menuitem"
              >
                <Trash2 className="mr-2 h-4 w-4" aria-hidden="true" />
                Delete Invoice #{invoice.id}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  )
})

export const InvoicesTable = memo(({
  invoices,
  loading,
  onEdit,
  onDelete,
  onRefresh,
}: InvoicesTableProps) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    field: 'createdAt',
    direction: 'desc',
  })

  const handleSort = useCallback((field: SortField) => {
    setSortConfig(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }))
  }, [])

  const getSortIcon = useCallback((field: SortField) => {
    if (sortConfig.field !== field) {
      return <ArrowUpDown className="ml-2 h-4 w-4" />
    }
    return sortConfig.direction === 'asc' ? (
      <ArrowUp className="ml-2 h-4 w-4" />
    ) : (
      <ArrowDown className="ml-2 h-4 w-4" />
    )
  }, [sortConfig.field, sortConfig.direction])

  const sortedInvoices = useMemo(() => {
    return [...invoices].sort((a, b) => {
      const { field, direction } = sortConfig
      let aValue: any
      let bValue: any

      switch (field) {
        case 'id':
          aValue = a.id
          bValue = b.id
          break
        case 'customerName':
          aValue = a.booking.customer.name.toLowerCase()
          bValue = b.booking.customer.name.toLowerCase()
          break
        case 'bookingDate':
          aValue = new Date(a.booking.start).getTime()
          bValue = new Date(b.booking.start).getTime()
          break
        case 'total':
          aValue = a.total
          bValue = b.total
          break
        case 'paid':
          aValue = a.paid
          bValue = b.paid
          break
        case 'status':
          aValue = a.status
          bValue = b.status
          break
        case 'createdAt':
          aValue = new Date(a.createdAt).getTime()
          bValue = new Date(b.createdAt).getTime()
          break
        default:
          aValue = a.id
          bValue = b.id
      }

      // Handle null/undefined values
      if (aValue == null && bValue == null) return 0
      if (aValue == null) return direction === 'asc' ? -1 : 1
      if (bValue == null) return direction === 'asc' ? 1 : -1

      if (aValue < bValue) {
        return direction === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return direction === 'asc' ? 1 : -1
      }
      return 0
    })
  }, [invoices, sortConfig])

  if (loading) {
    return <InvoicesTableSkeleton />
  }

  if (invoices.length === 0) {
    return (
      <>
        {/* Mobile empty state */}
        <div className="block md:hidden">
          <Card>
            <CardContent className="p-8 text-center">
              <div className="flex flex-col items-center justify-center space-y-4">
                <Receipt className="h-12 w-12 text-muted-foreground" />
                <div className="space-y-2">
                  <p className="text-lg font-medium">No invoices found</p>
                  <p className="text-sm text-muted-foreground">
                    Try adjusting your search or create a new invoice
                  </p>
                </div>
                <Button 
                  variant="outline" 
                  onClick={onRefresh} 
                  className="touch-target focus-enhanced px-6"
                  aria-label="Refresh invoices list"
                >
                  Refresh
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Desktop empty state */}
        <div className="hidden md:block rounded-md border">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="min-w-[100px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('id')}
                      className="h-auto p-0 font-semibold"
                    >
                      Invoice ID
                      {getSortIcon('id')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[200px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('customerName')}
                      className="h-auto p-0 font-semibold"
                    >
                      Customer
                      {getSortIcon('customerName')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[120px] hidden lg:table-cell">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('bookingDate')}
                      className="h-auto p-0 font-semibold"
                    >
                      Booking Date
                      {getSortIcon('bookingDate')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[120px] text-right">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('total')}
                      className="h-auto p-0 font-semibold"
                    >
                      Total Amount
                      {getSortIcon('total')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[120px] text-right">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('paid')}
                      className="h-auto p-0 font-semibold"
                    >
                      Paid Amount
                      {getSortIcon('paid')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[100px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('status')}
                      className="h-auto p-0 font-semibold"
                    >
                      Status
                      {getSortIcon('status')}
                    </Button>
                  </TableHead>
                  <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell colSpan={7} className="h-24 text-center">
                    <div className="flex flex-col items-center justify-center space-y-2">
                      <p className="text-muted-foreground">No invoices found</p>
                      <Button 
                        variant="outline" 
                        onClick={onRefresh} 
                        className="touch-target focus-enhanced px-6"
                        aria-label="Refresh invoices list"
                      >
                        Refresh
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>
      </>
    )
  }

  return (
    <>
      {/* Mobile card view */}
      <div className="block md:hidden">
        {/* Mobile sort controls */}
        <div className="flex items-center justify-between mb-4 p-4 bg-muted/50 rounded-lg mobile-spacing">
          <span className="text-sm font-medium" id="sort-label">Sort by:</span>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="outline" 
                size="sm" 
                className="touch-target focus-enhanced"
                aria-labelledby="sort-label"
                aria-expanded="false"
              >
                {sortConfig.field === 'id' && 'Invoice ID'}
                {sortConfig.field === 'customerName' && 'Customer'}
                {sortConfig.field === 'bookingDate' && 'Booking Date'}
                {sortConfig.field === 'total' && 'Total'}
                {sortConfig.field === 'paid' && 'Paid'}
                {sortConfig.field === 'status' && 'Status'}
                {sortConfig.field === 'createdAt' && 'Created'}
                {sortConfig.direction === 'asc' ? (
                  <ArrowUp className="ml-2 h-4 w-4" aria-hidden="true" />
                ) : (
                  <ArrowDown className="ml-2 h-4 w-4" aria-hidden="true" />
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-40" role="menu">
              <DropdownMenuItem 
                onClick={() => handleSort('id')}
                className="touch-target focus-enhanced"
                role="menuitem"
              >
                Invoice ID {getSortIcon('id')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('customerName')}
                className="touch-target focus-enhanced"
                role="menuitem"
              >
                Customer {getSortIcon('customerName')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('bookingDate')}
                className="touch-target focus-enhanced"
                role="menuitem"
              >
                Booking Date {getSortIcon('bookingDate')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('total')}
                className="touch-target focus-enhanced"
                role="menuitem"
              >
                Total {getSortIcon('total')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('paid')}
                className="touch-target focus-enhanced"
                role="menuitem"
              >
                Paid {getSortIcon('paid')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('status')}
                className="touch-target focus-enhanced"
                role="menuitem"
              >
                Status {getSortIcon('status')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('createdAt')}
                className="touch-target focus-enhanced"
                role="menuitem"
              >
                Created {getSortIcon('createdAt')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="space-y-4">
          {sortedInvoices.map((invoice) => (
            <InvoiceCard
              key={invoice.id}
              invoice={invoice}
              onEdit={onEdit}
              onDelete={onDelete}
            />
          ))}
        </div>
      </div>

      {/* Desktop table view */}
      <div className="hidden md:block rounded-md border">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="min-w-[100px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('id')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by invoice ID ${sortConfig.field === 'id' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Invoice ID
                    {getSortIcon('id')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[200px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('customerName')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by customer ${sortConfig.field === 'customerName' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Customer
                    {getSortIcon('customerName')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[120px] hidden lg:table-cell">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('bookingDate')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by booking date ${sortConfig.field === 'bookingDate' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Booking Date
                    {getSortIcon('bookingDate')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[120px] text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('total')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by total amount ${sortConfig.field === 'total' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Total Amount
                    {getSortIcon('total')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[120px] text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('paid')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by paid amount ${sortConfig.field === 'paid' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Paid Amount
                    {getSortIcon('paid')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[100px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('status')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by status ${sortConfig.field === 'status' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Status
                    {getSortIcon('status')}
                  </Button>
                </TableHead>
                <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedInvoices.map((invoice) => {
                const balance = Number(invoice.total) - Number(invoice.paid)
                
                return (
                  <TableRow key={invoice.id}>
                    <TableCell className="font-medium">
                      #{invoice.id}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <User className="h-3 w-3 text-muted-foreground" aria-hidden="true" />
                        <div>
                          <div className="font-medium">{invoice.booking.customer.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {invoice.booking.customer.email}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="hidden lg:table-cell">
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-3 w-3 text-muted-foreground" aria-hidden="true" />
                        <span>{format(new Date(invoice.booking.start), 'MMM dd, yyyy')}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right font-medium">
                      {formatCurrency(invoice.total)}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="space-y-1">
                        <div className="font-medium text-green-600">
                          {formatCurrency(invoice.paid)}
                        </div>
                        {balance > 0 && (
                          <div className="text-xs text-orange-600">
                            Balance: {formatCurrency(balance)}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getStatusBadgeVariant(invoice.status)}>
                        {INVOICE_STATUS_LABELS[invoice.status]}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button 
                            variant="ghost" 
                            className="h-10 w-10 p-0 focus-enhanced"
                            aria-label={`Actions for Invoice #${invoice.id}`}
                          >
                            <span className="sr-only">Open actions menu for Invoice #{invoice.id}</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48" role="menu">
                          <DropdownMenuItem 
                            onClick={() => onEdit(invoice)}
                            className="cursor-pointer focus-enhanced py-3"
                            role="menuitem"
                          >
                            <Edit className="mr-2 h-4 w-4" aria-hidden="true" />
                            Edit Invoice #{invoice.id}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => onDelete(invoice)}
                            className="text-destructive cursor-pointer focus-enhanced py-3"
                            role="menuitem"
                          >
                            <Trash2 className="mr-2 h-4 w-4" aria-hidden="true" />
                            Delete Invoice #{invoice.id}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </div>
      </div>
    </>
  )
})

InvoiceCard.displayName = "InvoiceCard"
InvoicesTable.displayName = "InvoicesTable"