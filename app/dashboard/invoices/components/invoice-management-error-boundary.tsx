"use client";

import React, { useState, useCallback } from "react";
import { ErrorBoundary } from "@/components/ui/error-boundary";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { FileText, RefreshCw, AlertTriangle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useInvoiceErrorMonitor } from "@/lib/utils/invoice-error-monitoring";

interface InvoiceManagementErrorBoundaryProps {
  children: React.ReactNode;
  onRetry?: () => void;
}

interface ErrorState {
  errorCount: number;
  lastErrorTime: number;
  errorType: string;
}

export function InvoiceManagementErrorBoundary({ 
  children, 
  onRetry 
}: InvoiceManagementErrorBoundaryProps) {
  const { toast } = useToast();
  const { captureError, getStats } = useInvoiceErrorMonitor();
  const [errorState, setErrorState] = useState<ErrorState>({
    errorCount: 0,
    lastErrorTime: 0,
    errorType: 'unknown'
  });

  const handleError = useCallback((error: Error, errorInfo: React.ErrorInfo) => {
    const now = Date.now();
    const timeSinceLastError = now - errorState.lastErrorTime;
    const isRepeatedError = timeSinceLastError < 5000; // 5 seconds
    
    // Determine error type for better categorization
    const errorType = error.name || 'UnknownError';
    
    // Update error state
    setErrorState(prev => ({
      errorCount: isRepeatedError ? prev.errorCount + 1 : 1,
      lastErrorTime: now,
      errorType
    }));

    // Capture error with enhanced monitoring
    const errorId = captureError(error, 'error-boundary', {
      componentStack: errorInfo.componentStack,
      errorType,
      errorCount: errorState.errorCount + 1,
      component: 'invoice-management-error-boundary'
    });

    // Show toast notification for non-repeated errors
    if (!isRepeatedError) {
      toast({
        title: "Invoice Management Error",
        description: `An error occurred while managing invoices. Error ID: ${errorId}`,
        variant: "destructive",
      });
    }

    // Log invoice management specific errors
    console.error("Invoice Management Error:", {
      errorId,
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      context: "invoice-management",
      errorCount: errorState.errorCount + 1,
      isRepeatedError
    });
  }, [captureError, errorState, toast]);

  const handleRetryWithReset = useCallback(() => {
    setErrorState({
      errorCount: 0,
      lastErrorTime: 0,
      errorType: 'unknown'
    });
    if (onRetry) {
      onRetry();
    }
  }, [onRetry]);

  const fallbackComponent = (
    <Card className="w-full">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
          <AlertTriangle className="w-6 h-6 text-red-600" />
        </div>
        <CardTitle className="text-xl font-bold text-gray-900 flex items-center justify-center">
          <FileText className="mr-2 h-5 w-5" />
          Invoice Management Error
        </CardTitle>
        <CardDescription>
          There was a problem loading the invoice management interface.
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            The invoice management system encountered an unexpected error. 
            This could be due to a network issue, payment processing problem, or a temporary server issue.
            {errorState.errorCount > 1 && (
              <span className="block mt-1 text-sm">
                This error has occurred {errorState.errorCount} times.
              </span>
            )}
          </AlertDescription>
        </Alert>

        {process.env.NODE_ENV === 'development' && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              <strong>Debug Info:</strong> Error Type: {errorState.errorType}, 
              Count: {errorState.errorCount}, 
              Stats: {JSON.stringify(getStats(), null, 2)}
            </AlertDescription>
          </Alert>
        )}

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {onRetry && (
            <Button onClick={handleRetryWithReset} className="flex items-center">
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry Loading Invoices
            </Button>
          )}
          
          <Button 
            variant="outline" 
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh Page
          </Button>
        </div>

        <div className="text-center text-sm text-muted-foreground">
          <p>If this problem persists, please contact your system administrator.</p>
          <p className="mt-1">Financial data may be temporarily unavailable.</p>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <ErrorBoundary
      fallback={fallbackComponent}
      onError={handleError}
      maxRetries={2}
      showErrorDetails={process.env.NODE_ENV === "development"}
    >
      {children}
    </ErrorBoundary>
  );
}