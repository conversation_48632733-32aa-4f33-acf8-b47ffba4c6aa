"use client";

import { useState, useCallback, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  Plus, 
  CreditCard, 
  AlertCircle, 
  RefreshCw,
  Edit,
  Trash2,
  Calendar,
  Hash,
  FileText
} from "lucide-react";
import { PaymentFormDialog } from "./payment-form-dialog";
import { useInvoices } from "@/hooks/use-invoices";
import { 
  Payment, 
  PaymentFormData, 
  formatCurrency, 
  PAYMENT_METHOD_LABELS, 
  PAYMENT_STATUS_LABELS 
} from "@/lib/types";
import { toast } from "@/hooks/use-toast";

interface InvoicePaymentsTabProps {
  invoiceId: number;
  invoiceTotal: number;
  invoicePaid: number;
  onPaymentChange?: () => void; // Callback when payments change to refresh invoice data
}

export function InvoicePaymentsTab({ 
  invoiceId, 
  invoiceTotal, 
  invoicePaid,
  onPaymentChange 
}: InvoicePaymentsTabProps) {
  const { 
    payments, 
    paymentsLoading, 
    paymentsError, 
    fetchInvoicePayments, 
    addPayment, 
    updatePayment, 
    deletePayment 
  } = useInvoices();

  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [editingPayment, setEditingPayment] = useState<Payment | null>(null);
  const [deletingPaymentId, setDeletingPaymentId] = useState<number | null>(null);

  // Fetch payments when component mounts or invoice changes
  useEffect(() => {
    if (invoiceId) {
      fetchInvoicePayments(invoiceId);
    }
  }, [invoiceId, fetchInvoicePayments]);

  const remainingBalance = invoiceTotal - invoicePaid;

  const handleAddPayment = useCallback(async (data: PaymentFormData) => {
    try {
      await addPayment(invoiceId, data);
      await fetchInvoicePayments(invoiceId); // Refresh payments
      onPaymentChange?.(); // Notify parent to refresh invoice data
      toast({
        title: "Success",
        description: "Payment recorded successfully.",
      });
    } catch (error) {
      console.error("Error adding payment:", error);
      throw error; // Re-throw to let the form handle it
    }
  }, [invoiceId, addPayment, fetchInvoicePayments, onPaymentChange]);

  const handleEditPayment = useCallback(async (data: PaymentFormData) => {
    if (!editingPayment) return;
    
    try {
      await updatePayment(editingPayment.id, data);
      await fetchInvoicePayments(invoiceId); // Refresh payments
      onPaymentChange?.(); // Notify parent to refresh invoice data
      setEditingPayment(null);
      toast({
        title: "Success",
        description: "Payment updated successfully.",
      });
    } catch (error) {
      console.error("Error updating payment:", error);
      throw error; // Re-throw to let the form handle it
    }
  }, [editingPayment, updatePayment, fetchInvoicePayments, invoiceId, onPaymentChange]);

  const handleDeletePayment = useCallback(async (paymentId: number) => {
    setDeletingPaymentId(paymentId);
    try {
      await deletePayment(paymentId);
      await fetchInvoicePayments(invoiceId); // Refresh payments
      onPaymentChange?.(); // Notify parent to refresh invoice data
      toast({
        title: "Success",
        description: "Payment deleted successfully.",
      });
    } catch (error) {
      console.error("Error deleting payment:", error);
      toast({
        title: "Error",
        description: "Failed to delete payment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setDeletingPaymentId(null);
    }
  }, [deletePayment, fetchInvoicePayments, invoiceId, onPaymentChange]);

  const handleRefresh = useCallback(() => {
    fetchInvoicePayments(invoiceId);
  }, [fetchInvoicePayments, invoiceId]);

  const openAddPaymentDialog = useCallback(() => {
    setEditingPayment(null);
    setPaymentDialogOpen(true);
  }, []);

  const openEditPaymentDialog = useCallback((payment: Payment) => {
    setEditingPayment(payment);
    setPaymentDialogOpen(true);
  }, []);

  const closePaymentDialog = useCallback(() => {
    setPaymentDialogOpen(false);
    setEditingPayment(null);
  }, []);

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'default';
      case 'PENDING':
        return 'secondary';
      case 'FAILED':
        return 'destructive';
      case 'REFUNDED':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  if (paymentsError) {
    return (
      <div className="space-y-4">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {paymentsError}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              className="ml-2"
            >
              Try Again
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Payment Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">Total Amount</p>
              <p className="text-2xl font-bold">{formatCurrency(invoiceTotal)}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">Paid Amount</p>
              <p className="text-2xl font-bold text-green-600">{formatCurrency(invoicePaid)}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">Remaining Balance</p>
              <p className={`text-2xl font-bold ${remainingBalance > 0 ? 'text-red-600' : 'text-green-600'}`}>
                {formatCurrency(remainingBalance)}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payments Section */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
            <CardTitle className="flex items-center">
              <CreditCard className="mr-2 h-5 w-5" />
              Payment History
            </CardTitle>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={paymentsLoading}
              >
                <RefreshCw className={`mr-2 h-4 w-4 ${paymentsLoading ? "animate-spin" : ""}`} />
                Refresh
              </Button>
              {remainingBalance > 0 && (
                <Button
                  onClick={openAddPaymentDialog}
                  size="sm"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Record Payment
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {paymentsLoading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-3 w-48" />
                  </div>
                  <div className="text-right space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                </div>
              ))}
            </div>
          ) : payments.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <CreditCard className="mx-auto h-12 w-12 mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No payments recorded</p>
              <p className="text-sm mb-4">
                {remainingBalance > 0 
                  ? "Record the first payment for this invoice."
                  : "This invoice has no payment history."
                }
              </p>
              {remainingBalance > 0 && (
                <Button onClick={openAddPaymentDialog}>
                  <Plus className="mr-2 h-4 w-4" />
                  Record Payment
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {payments.map((payment) => (
                <div key={payment.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">{formatCurrency(payment.amount)}</span>
                      <Badge variant={getStatusBadgeVariant(payment.status)}>
                        {PAYMENT_STATUS_LABELS[payment.status]}
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        via {PAYMENT_METHOD_LABELS[payment.method]}
                      </span>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <Calendar className="mr-1 h-3 w-3" />
                        {payment.paidAt ? new Date(payment.paidAt).toLocaleDateString() : 'No date'}
                      </div>
                      {payment.reference && (
                        <div className="flex items-center">
                          <Hash className="mr-1 h-3 w-3" />
                          {payment.reference}
                        </div>
                      )}
                      {payment.notes && (
                        <div className="flex items-center">
                          <FileText className="mr-1 h-3 w-3" />
                          Notes
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openEditPaymentDialog(payment)}
                      disabled={deletingPaymentId === payment.id}
                    >
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeletePayment(payment.id)}
                      disabled={deletingPaymentId === payment.id}
                    >
                      {deletingPaymentId === payment.id ? (
                        <RefreshCw className="h-3 w-3 animate-spin" />
                      ) : (
                        <Trash2 className="h-3 w-3" />
                      )}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Payment Form Dialog */}
      <PaymentFormDialog
        open={paymentDialogOpen}
        onOpenChange={closePaymentDialog}
        payment={editingPayment}
        onSubmit={editingPayment ? handleEditPayment : handleAddPayment}
        invoiceTotal={invoiceTotal}
        invoicePaid={invoicePaid}
      />
    </div>
  );
}