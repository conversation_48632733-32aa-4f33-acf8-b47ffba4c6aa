"use client";

import { memo, useMemo } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  TrendingUp, 
  Users, 
  DollarSign,
  PieChart,
  Calculator
} from "lucide-react";
import { LineItem, Catering, formatCurrency } from "@/lib/types";
import { calculateBookingRevenueSplit } from "@/lib/utils/catering";

interface CateringRevenueBreakdownProps {
  lineItems: LineItem[];
  caterings: Catering[];
  className?: string;
}

interface CateringRevenueItem {
  lineItem: LineItem;
  catering: Catering;
  totalRevenue: number;
  firstPartyRevenue: number;
  vendorRevenue: number;
  firstPartyPercentage: number;
  vendorPercentage: number;
}

export const CateringRevenueBreakdown = memo(({
  lineItems,
  caterings,
  className = ""
}: CateringRevenueBreakdownProps) => {
  
  // Calculate catering revenue breakdown
  const cateringBreakdown = useMemo(() => {
    const cateringItems: CateringRevenueItem[] = [];
    let totalCateringRevenue = 0;
    let totalFirstPartyRevenue = 0;
    let totalVendorRevenue = 0;

    // Process each catering line item
    lineItems
      .filter(item => item.isCatering && item.cateringId)
      .forEach(lineItem => {
        const catering = caterings.find(c => c.id === lineItem.cateringId);
        
        if (catering) {
          // Calculate revenue split using the utility function
          const revenueSplit = calculateBookingRevenueSplit(
            {
              pricePerPerson: catering.pricePerPerson,
              firstPartyShare: catering.firstPartyShare,
              vendorShare: catering.vendorShare
            },
            lineItem.quantity
          );

          const totalRevenue = lineItem.amount * lineItem.quantity;
          const firstPartyPercentage = (catering.firstPartyShare / catering.pricePerPerson) * 100;
          const vendorPercentage = (catering.vendorShare / catering.pricePerPerson) * 100;

          cateringItems.push({
            lineItem,
            catering,
            totalRevenue,
            firstPartyRevenue: revenueSplit.firstPartyRevenue,
            vendorRevenue: revenueSplit.vendorRevenue,
            firstPartyPercentage,
            vendorPercentage
          });

          totalCateringRevenue += totalRevenue;
          totalFirstPartyRevenue += revenueSplit.firstPartyRevenue;
          totalVendorRevenue += revenueSplit.vendorRevenue;
        }
      });

    return {
      items: cateringItems,
      totals: {
        totalCateringRevenue,
        totalFirstPartyRevenue,
        totalVendorRevenue,
        overallFirstPartyPercentage: totalCateringRevenue > 0 
          ? (totalFirstPartyRevenue / totalCateringRevenue) * 100 
          : 0,
        overallVendorPercentage: totalCateringRevenue > 0 
          ? (totalVendorRevenue / totalCateringRevenue) * 100 
          : 0
      }
    };
  }, [lineItems, caterings]);

  // Don't render if no catering items
  if (cateringBreakdown.items.length === 0) {
    return null;
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <PieChart className="h-5 w-5" />
          Catering Revenue Breakdown
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-muted/50 rounded-lg">
            <div className="flex items-center justify-center gap-2 mb-2">
              <DollarSign className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">Total Catering Revenue</span>
            </div>
            <div className="text-2xl font-bold text-primary">
              {formatCurrency(cateringBreakdown.totals.totalCateringRevenue)}
            </div>
          </div>
          
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="flex items-center justify-center gap-2 mb-2">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">First Party Share</span>
            </div>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(cateringBreakdown.totals.totalFirstPartyRevenue)}
            </div>
            <div className="text-xs text-green-600 mt-1">
              {cateringBreakdown.totals.overallFirstPartyPercentage.toFixed(1)}%
            </div>
          </div>
          
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Users className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium">Vendor Share</span>
            </div>
            <div className="text-2xl font-bold text-blue-600">
              {formatCurrency(cateringBreakdown.totals.totalVendorRevenue)}
            </div>
            <div className="text-xs text-blue-600 mt-1">
              {cateringBreakdown.totals.overallVendorPercentage.toFixed(1)}%
            </div>
          </div>
        </div>

        <Separator />

        {/* Individual Catering Items */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium flex items-center gap-2">
            <Calculator className="h-4 w-4" />
            Breakdown by Catering Offer
          </h4>
          
          {cateringBreakdown.items.map((item, index) => (
            <Card key={index} className="border-l-4 border-l-primary/20">
              <CardContent className="pt-4">
                <div className="space-y-3">
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div>
                      <h5 className="font-medium">{item.catering.offerName}</h5>
                      <p className="text-sm text-muted-foreground">
                        {item.lineItem.quantity} people × {formatCurrency(item.catering.pricePerPerson)} per person
                      </p>
                    </div>
                    <Badge variant="outline" className="ml-2">
                      {formatCurrency(item.totalRevenue)}
                    </Badge>
                  </div>

                  {/* Revenue Split */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div>
                        <div className="text-sm font-medium text-green-800">First Party</div>
                        <div className="text-xs text-green-600">
                          {formatCurrency(item.catering.firstPartyShare)} per person
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-green-600">
                          {formatCurrency(item.firstPartyRevenue)}
                        </div>
                        <div className="text-xs text-green-600">
                          {item.firstPartyPercentage.toFixed(1)}%
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div>
                        <div className="text-sm font-medium text-blue-800">Vendor</div>
                        <div className="text-xs text-blue-600">
                          {formatCurrency(item.catering.vendorShare)} per person
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-blue-600">
                          {formatCurrency(item.vendorRevenue)}
                        </div>
                        <div className="text-xs text-blue-600">
                          {item.vendorPercentage.toFixed(1)}%
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Calculation Formula */}
                  <div className="text-xs text-muted-foreground bg-muted/30 p-2 rounded">
                    <strong>Calculation:</strong> {item.lineItem.quantity} people × (
                    First Party: {formatCurrency(item.catering.firstPartyShare)} + 
                    Vendor: {formatCurrency(item.catering.vendorShare)}) = 
                    {formatCurrency(item.firstPartyRevenue)} + {formatCurrency(item.vendorRevenue)}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Revenue Sharing Validation */}
        <div className="p-3 bg-muted/50 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Calculator className="h-4 w-4" />
            <span className="text-sm font-medium">Revenue Sharing Validation</span>
          </div>
          <div className="text-xs text-muted-foreground space-y-1">
            {cateringBreakdown.items.map((item, index) => {
              const shareTotal = item.catering.firstPartyShare + item.catering.vendorShare;
              const isValid = Math.abs(shareTotal - item.catering.pricePerPerson) < 0.01;
              
              return (
                <div key={index} className={`flex items-center gap-2 ${isValid ? 'text-green-600' : 'text-red-600'}`}>
                  <span className={`w-2 h-2 rounded-full ${isValid ? 'bg-green-500' : 'bg-red-500'}`}></span>
                  <span>
                    {item.catering.offerName}: {formatCurrency(item.catering.firstPartyShare)} + {formatCurrency(item.catering.vendorShare)} = {formatCurrency(shareTotal)}
                    {isValid ? ' ✓' : ` ≠ ${formatCurrency(item.catering.pricePerPerson)} ✗`}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

CateringRevenueBreakdown.displayName = "CateringRevenueBreakdown";