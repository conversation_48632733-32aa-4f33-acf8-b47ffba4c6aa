"use client";

import { useEffect, useState, memo, useCallback, useMemo } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Loader2, 
  AlertCircle, 
  CheckCircle2, 
  Receipt
} from "lucide-react";
import { Invoice, InvoiceFormData, Booking, Catering, LineItemFormData } from "@/lib/types";
import { 
  invoiceCreateSchema, 
  invoiceUpdateSchema,
  calculateInvoiceTotal,
  LineItemInput
} from "@/lib/validations/invoice";
import { 
  mapServerErrorsToForm, 
  getApiErrorMessage, 
  createErrorSummary,
  hasFormErrors 
} from "@/lib/utils/form-errors";
import { formatCurrency } from "@/lib/types";
import { LineItemManager } from "./line-item-manager";

interface InvoiceInformationFormProps {
  invoice?: Invoice | null; // null for create, Invoice object for edit
  onSubmit: (data: InvoiceFormData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  bookings?: Booking[]; // Available bookings for selection
  caterings?: Catering[]; // Available catering offers
  submitButtonText?: string;
}

export const InvoiceInformationForm = memo(({
  invoice,
  onSubmit,
  onCancel,
  loading = false,
  bookings = [],
  caterings = [],
  submitButtonText = "Create Invoice",
}: InvoiceInformationFormProps) => {
  const isEdit = !!invoice;
  const schema = isEdit ? invoiceUpdateSchema : invoiceCreateSchema;
  const [serverError, setServerError] = useState<string>("");

  const form = useForm<InvoiceFormData>({
    resolver: zodResolver(schema),
    mode: "onChange",
    defaultValues: {
      bookingId: 0,
      lineItems: [
        {
          description: "",
          amount: 0,
          quantity: 1,
          isCatering: false,
        }
      ],
    },
  });

  // Watch values for calculations
  const watchedBookingId = form.watch("bookingId");
  const watchedLineItems = form.watch("lineItems");

  // Debug: Monitor form values
  useEffect(() => {
    if (watchedLineItems && watchedLineItems.length > 0) {
      watchedLineItems.forEach((item, index) => {
        if (item.isCatering) {
          // Silent monitoring for debugging if needed
          // console.log(`Form watch - Line item ${index}:`, {...});
        }
      });
    }
  }, [watchedLineItems]);

  // Get selected booking details
  const selectedBooking = useMemo(() => {
    return bookings.find(b => b.id === watchedBookingId);
  }, [bookings, watchedBookingId]);

  // Calculate total amount
  const totalAmount = useMemo(() => {
    return calculateInvoiceTotal(watchedLineItems as LineItemInput[]);
  }, [watchedLineItems]);

  // Reset form when invoice changes
  useEffect(() => {
    setServerError("");
    if (isEdit && invoice) {
      form.reset({
        bookingId: invoice.bookingId,
        lineItems: invoice.lineItems.map(item => ({
          description: item.description,
          amount: item.amount,
          quantity: item.quantity,
          isCatering: item.isCatering,
          cateringId: item.cateringId || undefined,
        })),
      });
    } else {
      form.reset({
        bookingId: 0,
        lineItems: [
          {
            description: "",
            amount: 0,
            quantity: 1,
            isCatering: false,
          }
        ],
      });
    }
  }, [isEdit, invoice, form]);

  // Auto-populate line items when booking is selected (for create mode)
  useEffect(() => {
    if (!isEdit && selectedBooking && watchedBookingId) {
      const newLineItems: LineItemFormData[] = [];

      // Add resource line items
      selectedBooking.resources.forEach(resource => {
        newLineItems.push({
          description: `${resource.name} - Resource Booking`,
          amount: resource.basePrice,
          quantity: 1,
          isCatering: false,
        });
      });

      // Add catering line items
      selectedBooking.caterings.forEach(cateringBooking => {
        const catering = caterings.find(c => c.id === cateringBooking.cateringId);
        
        if (catering) {
          // For catering items: amount = pricePerPerson (unit price), quantity = numberOfPeople from booking
          const lineItem = {
            description: `${catering.offerName} - Catering Service`,
            amount: catering.pricePerPerson, // Price per person (unit price)
            quantity: cateringBooking.quantity, // Number of people from booking
            isCatering: true,
            cateringId: catering.id,
          };
          // Debug: console.log('Auto-populating catering line item:', {...});
          newLineItems.push(lineItem);
        }
      });

      // Always ensure at least one line item exists
      if (newLineItems.length === 0) {
        newLineItems.push({
          description: "",
          amount: 0,
          quantity: 1,
          isCatering: false,
        });
      }

      // Set the line items and trigger validation
      form.setValue("lineItems", newLineItems, { 
        shouldValidate: true, 
        shouldDirty: true,
        shouldTouch: true 
      });
    }
  }, [selectedBooking, watchedBookingId, isEdit, caterings, form]);

  // Auto-correct catering line items whenever form values change
  useEffect(() => {
    if (watchedLineItems && caterings.length > 0) {
      watchedLineItems.forEach((item, index) => {
        if (item.isCatering && item.cateringId) {
          const catering = caterings.find(c => c.id === item.cateringId);
          if (catering && selectedBooking) {
            // Get the original booking quantity for this catering
            const originalQuantity = selectedBooking.caterings.find(
              cb => cb.cateringId === item.cateringId
            )?.quantity || 1;
            
            // For catering items: amount should be pricePerPerson (unit price), quantity should be numberOfPeople from booking
            const isQuantityWrong = item.quantity !== originalQuantity;
            const isAmountWrong = Math.abs(item.amount - catering.pricePerPerson) > 0.01;
            
            if (isQuantityWrong || isAmountWrong) {
              // Debug: console.log('Auto-correcting catering line item in useEffect:', {...});
              
              // Correct the form values
              form.setValue(`lineItems.${index}.amount`, catering.pricePerPerson, { 
                shouldValidate: false,
                shouldDirty: true,
                shouldTouch: false 
              });
              form.setValue(`lineItems.${index}.quantity`, originalQuantity, { 
                shouldValidate: false,
                shouldDirty: true,
                shouldTouch: false 
              });
            }
          }
        }
      });
    }
  }, [watchedLineItems, caterings, selectedBooking, form]);

  const handleSubmit = useCallback(async (data: InvoiceFormData) => {
    setServerError("");
    
    try {
      await onSubmit(data);
    } catch (error) {
      console.error("Form submission error:", error);
      
      if (error && typeof error === 'object' && 'details' in error) {
        mapServerErrorsToForm(error as any, form.setError as any);
      }
      
      const errorMessage = getApiErrorMessage(error);
      setServerError(errorMessage);
    }
  }, [onSubmit, form]);

  // Handle catering selection for a line item
  const handleCateringSelection = useCallback((index: number, cateringId: number | undefined, isCatering: boolean) => {
    if (isCatering && cateringId) {
      const catering = caterings.find(c => c.id === cateringId);
      
      if (catering && selectedBooking) {
        // Get the original booking quantity for this catering
        const originalQuantity = selectedBooking.caterings.find(
          cb => cb.cateringId === cateringId
        )?.quantity || 1;
        
        // For catering items: amount = pricePerPerson (unit price), quantity = numberOfPeople from booking
        form.setValue(`lineItems.${index}.description`, `${catering.offerName} - Catering Service`, { 
          shouldValidate: true,
          shouldDirty: true,
          shouldTouch: true 
        });
        form.setValue(`lineItems.${index}.amount`, catering.pricePerPerson, { 
          shouldValidate: true,
          shouldDirty: true,
          shouldTouch: true 
        });
        form.setValue(`lineItems.${index}.quantity`, originalQuantity, { 
          shouldValidate: true,
          shouldDirty: true,
          shouldTouch: true 
        });
        form.setValue(`lineItems.${index}.cateringId`, cateringId, { 
          shouldValidate: true,
          shouldDirty: true,
          shouldTouch: true 
        });
      }
    } else {
      form.setValue(`lineItems.${index}.cateringId`, undefined, { 
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true 
      });
      
      // Reset to defaults for non-catering items
      if (!isCatering) {
        form.setValue(`lineItems.${index}.quantity`, 1, { 
          shouldValidate: true,
          shouldDirty: true,
          shouldTouch: true 
        });
      }
    }
  }, [caterings, form, selectedBooking]);

  // Get error summary
  const errorSummary = useMemo(() => 
    createErrorSummary(form.formState.errors, serverError), 
    [form.formState.errors, serverError]
  );
  
  const canSubmit = useMemo(() => {
    return  !loading && totalAmount > 0
  }, [loading, totalAmount]
  );

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className="space-y-6"
      >
        {/* Error Summary */}
        {errorSummary.hasErrors && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1">
                <p className="font-medium">{errorSummary.summary}</p>
                {errorSummary.messages.length > 1 && (
                  <ul className="list-disc list-inside text-sm space-y-1">
                    {errorSummary.messages.map((message, index) => (
                      <li key={index}>{message}</li>
                    ))}
                  </ul>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Booking Selection */}
        <FormField
          control={form.control}
          name="bookingId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Associated Booking *</FormLabel>
              <Select
                onValueChange={(value) => field.onChange(parseInt(value))}
                value={field.value?.toString() || ""}
                disabled={loading || isEdit} // Disable in edit mode
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a booking" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {bookings.map((booking) => (
                    <SelectItem key={booking.id} value={booking.id.toString()}>
                      <div className="flex flex-col">
                        <span className="font-medium">{booking.customer.name}</span>
                        <span className="text-sm text-muted-foreground">
                          {new Date(booking.start).toLocaleDateString()} - {booking.resources.map(r => r.name).join(", ")}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription>
                {isEdit 
                  ? "The associated booking cannot be changed after invoice creation."
                  : "Select the booking for which this invoice is being created."
                }
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Selected Booking Details */}
        {selectedBooking && (
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Booking Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="font-medium">Customer:</span> {selectedBooking.customer.name}
                </div>
                <div>
                  <span className="font-medium">Status:</span> {selectedBooking.status}
                </div>
                <div>
                  <span className="font-medium">Date:</span> {new Date(selectedBooking.start).toLocaleDateString()}
                </div>
                <div>
                  <span className="font-medium">Time:</span> {new Date(selectedBooking.start).toLocaleTimeString()} - {new Date(selectedBooking.end).toLocaleTimeString()}
                </div>
              </div>
              <div>
                <span className="font-medium">Resources:</span> {selectedBooking.resources.map(r => r.name).join(", ")}
              </div>
              {selectedBooking.caterings.length > 0 && (
                <div>
                  <span className="font-medium">Catering:</span> {selectedBooking.caterings.map(c => {
                    const catering = caterings.find(cat => cat.id === c.cateringId);
                    return catering ? `${catering.offerName} (${c.quantity})` : 'Unknown';
                  }).join(", ")}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Line Items */}
        <LineItemManager
          control={form.control}
          errors={form.formState.errors}
          loading={loading}
          caterings={caterings}
          watchedLineItems={watchedLineItems}
          onCateringSelection={handleCateringSelection}
        />

        {/* Total Amount Display */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-between items-center text-lg font-semibold">
              <span>Total Amount:</span>
              <span className="text-primary">{formatCurrency(totalAmount)}</span>
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
          {/* Form validation status */}
          <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-2 sm:mb-0">
            {canSubmit ? (
              <div className="flex items-center text-green-600">
                <CheckCircle2 className="h-4 w-4 mr-1" />
                Form is valid
              </div>
            ) : hasFormErrors(form.formState.errors) || serverError || totalAmount === 0 ? (
              <div className="flex items-center text-red-600">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errorSummary.errorCount + (totalAmount === 0 ? 1 : 0)} error{(errorSummary.errorCount + (totalAmount === 0 ? 1 : 0)) !== 1 ? 's' : ''}
              </div>
            ) : null}
          </div>
          
          <div className="flex space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={!canSubmit}
              className="min-w-[140px]"
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {submitButtonText}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
});

InvoiceInformationForm.displayName = "InvoiceInformationForm";