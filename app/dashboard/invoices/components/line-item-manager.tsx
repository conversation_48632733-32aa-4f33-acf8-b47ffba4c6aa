"use client";

import { memo, useCallback, useMemo } from "react";
import { useFieldArray, Control, FieldErrors } from "react-hook-form";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Plus, 
  Trash2, 
  DollarSign,
  Calculator,
  AlertTriangle
} from "lucide-react";
import { InvoiceFormData, Catering, LineItemFormData } from "@/lib/types";
import { calculateInvoiceTotal, LineItemInput } from "@/lib/validations/invoice";
import { formatCurrency } from "@/lib/types";

interface LineItemManagerProps {
  control: Control<InvoiceFormData>;
  errors: FieldErrors<InvoiceFormData>;
  loading?: boolean;
  caterings?: Catering[];
  watchedLineItems: LineItemFormData[];
  onCateringSelection: (index: number, cateringId: number | undefined, isCatering: boolean) => void;
}

export const LineItemManager = memo(({
  control,
  errors,
  loading = false,
  caterings = [],
  watchedLineItems,
  onCateringSelection,
}: LineItemManagerProps) => {
  const { fields, append, remove } = useFieldArray({
    control,
    name: "lineItems",
  });

  // Calculate total amount
  const totalAmount = useMemo(() => {
    return calculateInvoiceTotal(watchedLineItems as LineItemInput[]);
  }, [watchedLineItems]);

  // Add new line item
  const addLineItem = useCallback(() => {
    append({
      description: "",
      amount: 0,
      quantity: 1,
      isCatering: false,
    });
  }, [append]);

  // Remove line item
  const removeLineItem = useCallback((index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  }, [remove, fields.length]);

  // Calculate line total for a specific item
  // FORMULA: Line Total = Quantity × Unit Price (Amount)
  // This is consistently applied for all line items:
  // - Regular items: quantity × price per unit
  // - Catering items: number of people × price per person
  const calculateLineTotal = useCallback((item: LineItemFormData) => {
    const amount = Number(item.amount) || 0;
    const quantity = Number(item.quantity) || 1;
    const total = amount * quantity; // quantity × unit price
    return isNaN(total) ? 0 : total;
  }, []);

  // Validate line item
  const validateLineItem = useCallback((item: LineItemFormData, index: number) => {
    const issues: string[] = [];

    const amount = Number(item.amount) || 0;
    const quantity = Number(item.quantity) || 1;
    
    if (!item.description?.trim()) {
      issues.push("Description is required");
    }
    
    if (!amount || amount <= 0) {
      issues.push("Amount must be greater than 0");
    }
    
    if (!quantity || quantity <= 0) {
      issues.push("Quantity must be at least 1");
    }
    
    if (item.isCatering && !item.cateringId) {
      issues.push("Catering offer must be selected");
    }
    
    
    return issues;
  }, [caterings]);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium">Line Items</h4>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={addLineItem}
          disabled={loading}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Add Line Item
        </Button>
      </div>

      <div className="space-y-4">
        {fields.map((field, index) => {
          const item = watchedLineItems[index];
          const lineTotal = calculateLineTotal(item);
          const validationIssues = validateLineItem(item, index);
          const hasIssues = validationIssues.length > 0;

          return (
            <Card key={field.id} className={hasIssues ? "border-red-200" : ""}>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h5 className="text-sm font-medium flex items-center gap-2">
                      Line Item {index + 1}
                      {hasIssues && (
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                      )}
                    </h5>
                    {fields.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeLineItem(index)}
                        disabled={loading}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  {/* Validation Issues */}
                  {hasIssues && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                      <div className="flex items-start gap-2">
                        <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5" />
                        <div>
                          <p className="text-sm font-medium text-red-800">Issues with this line item:</p>
                          <ul className="text-sm text-red-700 list-disc list-inside mt-1">
                            {validationIssues.map((issue, issueIndex) => (
                              <li key={issueIndex}>{issue}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Is Catering Checkbox */}
                    <div className="md:col-span-2">
                      <FormField
                        control={control}
                        name={`lineItems.${index}.isCatering`}
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={(checked) => {
                                  field.onChange(checked);
                                  onCateringSelection(index, undefined, !!checked);
                                }}
                                disabled={loading}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>
                                This is a catering line item
                              </FormLabel>
                              <FormDescription>
                                Check this if this line item represents catering services
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Catering Selection */}
                    {item?.isCatering && (
                      <div className="md:col-span-2">
                        <FormField
                          control={control}
                          name={`lineItems.${index}.cateringId`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Catering Offer *</FormLabel>
                              <Select
                                onValueChange={(value) => {
                                  const cateringId = parseInt(value);
                                  field.onChange(cateringId);
                                  onCateringSelection(index, cateringId, true);
                                }}
                                value={field.value?.toString() || ""}
                                disabled={loading}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select catering offer" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {caterings.map((catering) => (
                                    <SelectItem key={catering.id} value={catering.id.toString()}>
                                      <div className="flex flex-col">
                                        <span className="font-medium">{catering.offerName}</span>
                                        <span className="text-sm text-muted-foreground">
                                          {formatCurrency(catering.pricePerPerson)} per person
                                        </span>
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                Selecting a catering offer will automatically set the description and amount
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}

                    {/* Description */}
                    <div className="md:col-span-2">
                      <FormField
                        control={control}
                        name={`lineItems.${index}.description`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Description *</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter line item description"
                                {...field}
                                disabled={loading}
                              />
                            </FormControl>
                            <FormDescription>
                              Brief description of the service or product
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Quantity */}
                    <FormField
                      control={control}
                      name={`lineItems.${index}.quantity`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Quantity *</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="1"
                              step="1"
                              placeholder="1"
                              {...field}
                              value={field.value || 1}
                              onChange={(e) => {
                                const value = e.target.value;
                                const numValue = value === '' ? 1 : parseInt(value, 10);
                                field.onChange(isNaN(numValue) ? 1 : Math.max(1, numValue));
                              }}
                              disabled={loading}
                            />
                          </FormControl>
                          <FormDescription>
                            {item?.isCatering ? "Number of people for catering" : "Number of units"}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Amount */}
                    <FormField
                      control={control}
                      name={`lineItems.${index}.amount`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Unit Amount (IQD) *</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                              <Input
                                type="number"
                                min="0.01"
                                step="0.01"
                                placeholder="0.00"
                                className="pl-10"
                                {...field}
                                value={field.value || ''}
                                onChange={(e) => {
                                  const value = e.target.value;
                                  const numValue = value === '' ? 0 : parseFloat(value);
                                  field.onChange(isNaN(numValue) ? 0 : Math.max(0, numValue));
                                }}
                                disabled={loading || (item?.isCatering && !!item?.cateringId)}
                              />
                            </div>
                          </FormControl>
                          <FormDescription>
                            {item?.isCatering ? "Price per person" : "Price per unit"}
                            {item?.isCatering && item?.cateringId && " (auto-filled from catering offer)"}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Line Total */}
                    <div className="md:col-span-2 pt-2 border-t">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium flex items-center gap-2">
                          <Calculator className="h-4 w-4" />
                          Line Total:
                        </span>
                        <span className="font-bold text-lg">
                          {formatCurrency(lineTotal)}
                        </span>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        Formula: {item?.quantity || 1} × {formatCurrency(item?.amount || 0)} = {formatCurrency(lineTotal)}
                      </div>
                      {item?.isCatering && item?.cateringId && (
                        <div className="mt-2 text-xs text-muted-foreground">
                          {(() => {
                            const catering = caterings.find(c => c.id === item.cateringId);
                            if (catering && item.amount) {
                              // Calculate number of people from total amount and price per person
                              const numberOfPeople = Math.round(item.amount / catering.pricePerPerson);
                              const firstPartyAmount = catering.firstPartyShare * numberOfPeople;
                              const vendorAmount = catering.vendorShare * numberOfPeople;
                              return (
                                <div className="space-y-1">
                                  <div>For {numberOfPeople} people: Revenue Split - First Party {formatCurrency(firstPartyAmount)}, Vendor {formatCurrency(vendorAmount)}</div>
                                </div>
                              );
                            }
                            return null;
                          })()}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Invoice Total */}
      <Card className="bg-muted/50">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              <span className="text-lg font-semibold">Invoice Total:</span>
            </div>
            <span className="text-2xl font-bold text-primary">
              {formatCurrency(totalAmount)}
            </span>
          </div>
          {totalAmount === 0 && (
            <div className="mt-2 text-sm text-red-600 flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              Invoice total must be greater than 0
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
});

LineItemManager.displayName = "LineItemManager";