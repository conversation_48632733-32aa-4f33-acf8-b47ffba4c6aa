"use client";

import { useEffect, useState, memo, useCallback, useMemo } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Loader2, 
  AlertCircle, 
  CheckCircle2, 
  CreditCard
} from "lucide-react";
import { Payment, PaymentFormData, PaymentMethod, PAYMENT_METHOD_LABELS, formatCurrency } from "@/lib/types";
import { paymentCreateSchema, paymentUpdateSchema } from "@/lib/validations/invoice";
import { 
  mapServerErrorsToForm, 
  getApiErrorMessage, 
  createErrorSummary,
  hasFormErrors 
} from "@/lib/utils/form-errors";

interface PaymentFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  payment?: Payment | null; // null for create, Payment object for edit
  onSubmit: (data: PaymentFormData) => Promise<void>;
  loading?: boolean;
  invoiceTotal?: number;
  invoicePaid?: number;
}

export const PaymentFormDialog = memo(({
  open,
  onOpenChange,
  payment,
  onSubmit,
  loading = false,
  invoiceTotal = 0,
  invoicePaid = 0,
}: PaymentFormDialogProps) => {
  const isEdit = !!payment;
  const schema = isEdit ? paymentUpdateSchema : paymentCreateSchema;
  const [serverError, setServerError] = useState<string>("");

  const remainingBalance = useMemo(() => {
    return Math.max(0, invoiceTotal - invoicePaid);
  }, [invoiceTotal, invoicePaid]);

  const form = useForm<PaymentFormData>({
    resolver: zodResolver(schema),
    mode: "onChange",
    defaultValues: {
      amount: 0,
      method: "CASH" as PaymentMethod,
      reference: "",
      notes: "",
      paidAt: new Date(),
    },
  });

  // Watch amount for validation
  const watchedAmount = form.watch("amount");

  // Reset form when dialog opens/closes or payment changes
  useEffect(() => {
    if (open) {
      setServerError("");
      if (isEdit && payment) {
        form.reset({
          amount: payment.amount,
          method: payment.method,
          reference: payment.reference || "",
          notes: payment.notes || "",
          paidAt: payment.paidAt ? new Date(payment.paidAt) : new Date(),
        });
      } else {
        form.reset({
          amount: remainingBalance > 0 ? remainingBalance : 0,
          method: "CASH" as PaymentMethod,
          reference: "",
          notes: "",
          paidAt: new Date(),
        });
      }
    }
  }, [open, isEdit, payment, form, remainingBalance]);

  const handleSubmit = useCallback(async (data: PaymentFormData) => {
    setServerError("");
    
    try {
      await onSubmit(data);
      onOpenChange(false);
    } catch (error) {
      console.error("Form submission error:", error);
      
      if (error && typeof error === 'object' && 'details' in error) {
        mapServerErrorsToForm(error as any, form.setError as any);
      }
      
      const errorMessage = getApiErrorMessage(error);
      setServerError(errorMessage);
    }
  }, [onSubmit, onOpenChange, form]);

  const handleCancel = useCallback(() => {
    form.reset();
    setServerError("");
    onOpenChange(false);
  }, [form, onOpenChange]);

  // Get error summary
  const errorSummary = useMemo(() => 
    createErrorSummary(form.formState.errors, serverError), 
    [form.formState.errors, serverError]
  );
  
  const canSubmit = useMemo(() => {
    const isValidAmount = watchedAmount > 0 && (!isEdit ? watchedAmount <= remainingBalance : true);
    return form.formState.isValid && !loading && isValidAmount;
  }, [form.formState.isValid, loading, watchedAmount, remainingBalance, isEdit]);

  // Amount validation message
  const amountValidationMessage = useMemo(() => {
    if (!isEdit && watchedAmount > remainingBalance) {
      return `Payment amount cannot exceed remaining balance of ${formatCurrency(remainingBalance)}`;
    }
    return null;
  }, [watchedAmount, remainingBalance, isEdit]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            {isEdit ? "Edit Payment" : "Record Payment"}
          </DialogTitle>
          <DialogDescription>
            {isEdit
              ? "Update the payment information below."
              : `Record a payment for this invoice. Remaining balance: ${formatCurrency(remainingBalance)}`}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-4"
          >
            {/* Error Summary */}
            {errorSummary.hasErrors && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-1">
                    <p className="font-medium">{errorSummary.summary}</p>
                    {errorSummary.messages.length > 1 && (
                      <ul className="list-disc list-inside text-sm space-y-1">
                        {errorSummary.messages.map((message, index) => (
                          <li key={index}>{message}</li>
                        ))}
                      </ul>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* Amount validation warning */}
            {amountValidationMessage && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{amountValidationMessage}</AlertDescription>
              </Alert>
            )}

            {/* Payment Amount */}
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Payment Amount (IQD) *</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      min="0.01"
                      max={isEdit ? undefined : remainingBalance}
                      placeholder="Enter payment amount"
                      {...field}
                      onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      disabled={loading}
                    />
                  </FormControl>
                  <FormDescription>
                    {!isEdit && `Maximum amount: ${formatCurrency(remainingBalance)}`}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Payment Method */}
            <FormField
              control={form.control}
              name="method"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Payment Method *</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={loading}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select payment method" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.entries(PAYMENT_METHOD_LABELS).map(([value, label]) => (
                        <SelectItem key={value} value={value}>
                          {label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Choose the method used for this payment
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Payment Reference */}
            <FormField
              control={form.control}
              name="reference"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Reference Number</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Transaction ID, check number, etc."
                      {...field}
                      disabled={loading}
                    />
                  </FormControl>
                  <FormDescription>
                    Optional reference number for tracking
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Payment Date */}
            <FormField
              control={form.control}
              name="paidAt"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Payment Date</FormLabel>
                  <FormControl>
                    <Input
                      type="datetime-local"
                      {...field}
                      value={field.value ? new Date(field.value.getTime() - field.value.getTimezoneOffset() * 60000).toISOString().slice(0, 16) : ""}
                      onChange={(e) => field.onChange(e.target.value ? new Date(e.target.value) : new Date())}
                      disabled={loading}
                    />
                  </FormControl>
                  <FormDescription>
                    When was this payment received?
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Payment Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Additional notes about this payment..."
                      rows={3}
                      {...field}
                      disabled={loading}
                    />
                  </FormControl>
                  <FormDescription>
                    Optional notes or comments
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
              {/* Form validation status */}
              <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-2 sm:mb-0">
                {canSubmit ? (
                  <div className="flex items-center text-green-600">
                    <CheckCircle2 className="h-4 w-4 mr-1" />
                    Form is valid
                  </div>
                ) : hasFormErrors(form.formState.errors) || serverError || amountValidationMessage ? (
                  <div className="flex items-center text-red-600">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {errorSummary.errorCount + (amountValidationMessage ? 1 : 0)} error{(errorSummary.errorCount + (amountValidationMessage ? 1 : 0)) !== 1 ? 's' : ''}
                  </div>
                ) : null}
              </div>
              
              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={!canSubmit}
                  className="min-w-[120px]"
                >
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {isEdit ? "Update Payment" : "Record Payment"}
                </Button>
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
});

PaymentFormDialog.displayName = "PaymentFormDialog";