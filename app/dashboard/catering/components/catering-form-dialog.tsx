"use client";

import { useEffect, useState, memo, useCallback, useMemo } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle, CheckCircle2, DollarSign } from "lucide-react";
import { Catering, CateringFormData } from "@/lib/types";
import { cateringCreateSchema, cateringUpdateSchema } from "@/lib/validations/catering";
import { 
  mapServerErrorsToForm, 
  getApiErrorMessage, 
  createErrorSummary,
  hasFormErrors 
} from "@/lib/utils/form-errors";
import { 
  formatCurrency, 
  validateRevenueSharing,
  calculatePercentageBreakdown,
  validateCateringFormData
} from "@/lib/utils/catering";
import { useCateringErrorMonitor } from "@/lib/utils/catering-error-monitoring";

interface CateringFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  catering?: Catering | null; // null for create, Catering object for edit
  onSubmit: (data: CateringFormData) => Promise<void>;
  loading?: boolean;
}

export const CateringFormDialog = memo(({
  open,
  onOpenChange,
  catering,
  onSubmit,
  loading = false,
}: CateringFormDialogProps) => {
  const isEdit = !!catering;
  const schema = isEdit ? cateringUpdateSchema : cateringCreateSchema;
  const [serverError, setServerError] = useState<string>("");
  const [validationErrors, setValidationErrors] = useState<Record<string, string[]>>({});
  const { captureError } = useCateringErrorMonitor();

  const form = useForm<CateringFormData>({
    resolver: zodResolver(schema),
    mode: "onChange", // Enable real-time validation
    defaultValues: {
      offerName: "",
      pricePerPerson: 0,
      firstPartyShare: 0,
      vendorShare: 0,
    },
  });

  // Watch values for real-time revenue sharing validation
  const watchedPricePerPerson = form.watch("pricePerPerson");
  const watchedFirstPartyShare = form.watch("firstPartyShare");
  const watchedVendorShare = form.watch("vendorShare");

  // Calculate revenue sharing validation
  const revenueShareValidation = useMemo(() => {
    const total = watchedFirstPartyShare + watchedVendorShare;
    const isValid = validateRevenueSharing(watchedFirstPartyShare, watchedVendorShare, watchedPricePerPerson);
    const remaining = watchedPricePerPerson - total;
    const { firstPartyPercentage, vendorPercentage } = calculatePercentageBreakdown(watchedFirstPartyShare, watchedVendorShare);
    
    return {
      isValid,
      total,
      remaining,
      firstPartyPercentage,
      vendorPercentage,
    };
  }, [watchedPricePerPerson, watchedFirstPartyShare, watchedVendorShare]);

  // Reset form when dialog opens/closes or catering changes
  useEffect(() => {
    if (open) {
      setServerError(""); // Clear server errors when opening
      if (isEdit && catering) {
        form.reset({
          offerName: catering.offerName,
          pricePerPerson: catering.pricePerPerson,
          firstPartyShare: catering.firstPartyShare,
          vendorShare: catering.vendorShare,
        });
      } else {
        form.reset({
          offerName: "",
          pricePerPerson: 0,
          firstPartyShare: 0,
          vendorShare: 0,
        });
      }
    }
  }, [open, isEdit, catering, form]);

  const handleSubmit = useCallback(async (data: CateringFormData) => {
    setServerError(""); // Clear previous server errors
    setValidationErrors({}); // Clear previous validation errors
    
    // Enhanced client-side validation before submission
    const clientValidation = validateCateringFormData(data);
    if (!clientValidation.isValid) {
      setValidationErrors(clientValidation.errors);
      setServerError(clientValidation.summary);
      
      // Map client validation errors to form fields
      Object.entries(clientValidation.errors).forEach(([field, messages]) => {
        if (messages.length > 0) {
          form.setError(field as keyof CateringFormData, {
            type: 'validation',
            message: messages[0]
          });
        }
      });
      
      return;
    }
    
    try {
      await onSubmit(data);
      
      // Clear all errors on successful submission
      setServerError("");
      setValidationErrors({});
      onOpenChange(false);
    } catch (error) {
      console.error("Form submission error:", error);
      
      // Capture error with enhanced monitoring
      const errorReport = captureError(
        error instanceof Error ? error : new Error(String(error)),
        {
          operation: isEdit ? 'update' : 'create',
          cateringId: isEdit ? catering?.id : undefined,
          cateringName: data.offerName,
          formData: data
        },
        {
          formMode: isEdit ? 'edit' : 'create',
          validationPassed: true,
          submissionAttempt: true
        }
      );
      
      // Handle server validation errors with enhanced feedback
      if (error && typeof error === 'object' && 'details' in error) {
        const serverValidationErrors = error as any;
        mapServerErrorsToForm(serverValidationErrors, form.setError as any);
        
        // Also store for display
        if (serverValidationErrors.details) {
          setValidationErrors(serverValidationErrors.details);
        }
      }
      
      // Set user-friendly error message
      const errorMessage = errorReport.userMessage || getApiErrorMessage(error);
      setServerError(errorMessage);
      
      // Show specific guidance based on error type
      if (errorReport.category === 'validation') {
        // Focus on first field with error
        const firstErrorField = Object.keys(form.formState.errors)[0];
        if (firstErrorField) {
          const fieldElement = document.querySelector(`[name="${firstErrorField}"]`) as HTMLElement;
          fieldElement?.focus();
        }
      }
    }
  }, [onSubmit, onOpenChange, form, captureError, isEdit, catering]);

  const handleCancel = useCallback(() => {
    form.reset();
    setServerError("");
    setValidationErrors({});
    onOpenChange(false);
  }, [form, onOpenChange]);

  // Auto-calculate vendor share when first party share changes
  const handleFirstPartyShareChange = useCallback((value: number) => {
    const remaining = watchedPricePerPerson - value;
    if (remaining >= 0) {
      form.setValue("vendorShare", remaining, { shouldValidate: true });
    }
  }, [watchedPricePerPerson, form]);

  // Auto-calculate first party share when vendor share changes
  const handleVendorShareChange = useCallback((value: number) => {
    const remaining = watchedPricePerPerson - value;
    if (remaining >= 0) {
      form.setValue("firstPartyShare", remaining, { shouldValidate: true });
    }
  }, [watchedPricePerPerson, form]);

  // Format currency for display using utility function
  const formatCurrencyDisplay = useCallback((amount: number) => {
    return formatCurrency(amount);
  }, []);

  // Get comprehensive error summary for display - memoized to prevent unnecessary recalculations
  const errorSummary = useMemo(() => {
    const formErrors = form.formState.errors;
    const hasValidationErrors = Object.keys(validationErrors).length > 0;
    const hasServerError = !!serverError;
    const hasFormErrors = Object.keys(formErrors).length > 0;
    
    // Combine all error sources
    const allErrors: string[] = [];
    
    if (hasServerError) {
      allErrors.push(serverError);
    }
    
    // Add form field errors
    Object.values(formErrors).forEach(error => {
      if (error?.message) {
        allErrors.push(error.message);
      }
    });
    
    // Add validation errors that aren't already in form errors
    Object.values(validationErrors).forEach(messages => {
      messages.forEach(message => {
        if (!allErrors.includes(message)) {
          allErrors.push(message);
        }
      });
    });
    
    return {
      hasErrors: allErrors.length > 0,
      errorCount: allErrors.length,
      messages: allErrors,
      summary: allErrors.length === 1 
        ? allErrors[0] 
        : `${allErrors.length} errors need to be fixed`,
      hasValidationErrors,
      hasServerError,
      hasFormErrors
    };
  }, [form.formState.errors, serverError, validationErrors]);
  
  const canSubmit = useMemo(() => 
    form.formState.isValid && !loading && revenueShareValidation.isValid, 
    [form.formState.isValid, loading, revenueShareValidation.isValid]
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent 
        className="sm:max-w-[600px] mobile-spacing max-h-[90vh] overflow-y-auto"
        aria-labelledby="catering-form-title"
        aria-describedby="catering-form-description"
      >
        <DialogHeader>
          <DialogTitle id="catering-form-title">
            {isEdit ? "Edit Catering Offer" : "Create New Catering Offer"}
          </DialogTitle>
          <DialogDescription id="catering-form-description">
            {isEdit
              ? "Update the catering offer information below. All fields are required."
              : "Fill in the details below to create a new catering offer. All fields are required."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* Error Summary */}
            {errorSummary.hasErrors && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-1">
                    <p className="font-medium">{errorSummary.summary}</p>
                    {errorSummary.messages.length > 1 && (
                      <ul className="list-disc list-inside text-sm space-y-1">
                        {errorSummary.messages.map((message, index) => (
                          <li key={index}>{message}</li>
                        ))}
                      </ul>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* Offer Name */}
            <FormField
              control={form.control}
              name="offerName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel htmlFor="catering-name">
                    Offer Name <span className="text-destructive" aria-label="required">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      id="catering-name"
                      placeholder="Enter catering offer name"
                      {...field}
                      disabled={loading}
                      className="touch-target focus-enhanced"
                      aria-describedby="catering-name-description catering-name-error"
                      aria-invalid={!!form.formState.errors.offerName}
                    />
                  </FormControl>
                  <FormDescription id="catering-name-description">
                    Name must be unique and contain only letters, numbers, spaces, and basic punctuation.
                  </FormDescription>
                  <FormMessage id="catering-name-error" />
                </FormItem>
              )}
            />

            {/* Price Per Person */}
            <FormField
              control={form.control}
              name="pricePerPerson"
              render={({ field }) => (
                <FormItem>
                  <FormLabel htmlFor="catering-price">
                    Price Per Person (IQD) <span className="text-destructive" aria-label="required">*</span>
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="catering-price"
                        type="number"
                        placeholder="0"
                        min="1"
                        step="1"
                        {...field}
                        onChange={(e) => {
                          const value = parseInt(e.target.value) || 0;
                          field.onChange(value);
                          // Reset shares when price changes
                          form.setValue("firstPartyShare", 0, { shouldValidate: true });
                          form.setValue("vendorShare", 0, { shouldValidate: true });
                        }}
                        disabled={loading}
                        className="pl-10 touch-target focus-enhanced"
                        aria-describedby="catering-price-description catering-price-error"
                        aria-invalid={!!form.formState.errors.pricePerPerson}
                      />
                    </div>
                  </FormControl>
                  <FormDescription id="catering-price-description">
                    Total price per person in Iraqi Dinars (IQD). Must be a whole number.
                  </FormDescription>
                  <FormMessage id="catering-price-error" />
                </FormItem>
              )}
            />

            {/* Revenue Sharing Calculator */}
            {watchedPricePerPerson > 0 && (
              <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium">Revenue Sharing Calculator</h4>
                  <div className="text-sm text-muted-foreground">
                    Total: {formatCurrencyDisplay(revenueShareValidation.total)} / {formatCurrencyDisplay(watchedPricePerPerson)}
                  </div>
                </div>

                {/* First Party Share */}
                <FormField
                  control={form.control}
                  name="firstPartyShare"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel htmlFor="first-party-share">
                        First Party Share (IQD) <span className="text-destructive" aria-label="required">*</span>
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            id="first-party-share"
                            type="number"
                            placeholder="0"
                            min="0"
                            max={watchedPricePerPerson}
                            step="1"
                            {...field}
                            onChange={(e) => {
                              const value = parseInt(e.target.value) || 0;
                              field.onChange(value);
                              handleFirstPartyShareChange(value);
                            }}
                            disabled={loading}
                            className="pl-10 touch-target focus-enhanced"
                            aria-describedby="first-party-share-description first-party-share-error"
                            aria-invalid={!!form.formState.errors.firstPartyShare}
                          />
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-muted-foreground">
                            {revenueShareValidation.firstPartyPercentage.toFixed(1)}%
                          </div>
                        </div>
                      </FormControl>
                      <FormDescription id="first-party-share-description">
                        Amount in IQD that goes to the first party per person.
                      </FormDescription>
                      <FormMessage id="first-party-share-error" />
                    </FormItem>
                  )}
                />

                {/* Vendor Share */}
                <FormField
                  control={form.control}
                  name="vendorShare"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel htmlFor="vendor-share">
                        Vendor Share (IQD) <span className="text-destructive" aria-label="required">*</span>
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            id="vendor-share"
                            type="number"
                            placeholder="0"
                            min="0"
                            max={watchedPricePerPerson}
                            step="1"
                            {...field}
                            onChange={(e) => {
                              const value = parseInt(e.target.value) || 0;
                              field.onChange(value);
                              handleVendorShareChange(value);
                            }}
                            disabled={loading}
                            className="pl-10 touch-target focus-enhanced"
                            aria-describedby="vendor-share-description vendor-share-error"
                            aria-invalid={!!form.formState.errors.vendorShare}
                          />
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-muted-foreground">
                            {revenueShareValidation.vendorPercentage.toFixed(1)}%
                          </div>
                        </div>
                      </FormControl>
                      <FormDescription id="vendor-share-description">
                        Amount in IQD that goes to the vendor per person.
                      </FormDescription>
                      <FormMessage id="vendor-share-error" />
                    </FormItem>
                  )}
                />

                {/* Revenue Sharing Validation Status */}
                <div className={`p-3 rounded-md ${
                  revenueShareValidation.isValid 
                    ? 'bg-green-50 border border-green-200' 
                    : 'bg-red-50 border border-red-200'
                }`}>
                  <div className="flex items-center space-x-2">
                    {revenueShareValidation.isValid ? (
                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-red-600" />
                    )}
                    <span className={`text-sm font-medium ${
                      revenueShareValidation.isValid ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {revenueShareValidation.isValid 
                        ? 'Revenue sharing is valid' 
                        : `Revenue shares must add up to exactly ${formatCurrencyDisplay(watchedPricePerPerson)}`
                      }
                    </span>
                  </div>
                  {!revenueShareValidation.isValid && revenueShareValidation.remaining !== 0 && (
                    <div className="mt-1 text-sm text-red-700">
                      {revenueShareValidation.remaining > 0 
                        ? `Missing ${formatCurrencyDisplay(revenueShareValidation.remaining)}`
                        : `Exceeds by ${formatCurrencyDisplay(Math.abs(revenueShareValidation.remaining))}`
                      }
                    </div>
                  )}
                </div>
              </div>
            )}

            <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
              {/* Form validation status */}
              <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-2 sm:mb-0">
                {canSubmit ? (
                  <div className="flex items-center text-green-600">
                    <CheckCircle2 className="h-4 w-4 mr-1" />
                    Form is valid
                  </div>
                ) : hasFormErrors(form.formState.errors) || serverError || !revenueShareValidation.isValid ? (
                  <div className="flex items-center text-red-600">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {errorSummary.errorCount + (!revenueShareValidation.isValid ? 1 : 0)} error{(errorSummary.errorCount + (!revenueShareValidation.isValid ? 1 : 0)) !== 1 ? 's' : ''}
                  </div>
                ) : null}
              </div>
              
              <div className="flex space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={loading}
                  className="touch-target focus-enhanced flex-1 sm:flex-none"
                >
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={!canSubmit}
                  className="touch-target focus-enhanced min-w-[140px] flex-1 sm:flex-none"
                  aria-describedby={errorSummary.hasErrors ? "form-errors" : undefined}
                >
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />}
                  {isEdit ? "Update Catering Offer" : "Create Catering Offer"}
                </Button>
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
});

CateringFormDialog.displayName = "CateringFormDialog";