"use client";

import { memo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Loader2, ChefHat, Clock, Wifi, WifiOff } from "lucide-react";

interface CateringLoadingStatesProps {
  type: 'initial' | 'refresh' | 'search' | 'create' | 'update' | 'delete';
  message?: string;
  showProgress?: boolean;
  progress?: number;
  isOnline?: boolean;
}

/**
 * Enhanced loading states for catering operations with specific feedback
 */
export const CateringLoadingStates = memo(({
  type,
  message,
  showProgress = false,
  progress = 0,
  isOnline = true
}: CateringLoadingStatesProps) => {
  const getLoadingConfig = () => {
    switch (type) {
      case 'initial':
        return {
          icon: <ChefHat className="h-6 w-6 animate-pulse" />,
          title: "Loading Catering Management",
          description: message || "Fetching catering offers and initializing the management system...",
          color: "text-blue-600"
        };
      case 'refresh':
        return {
          icon: <Loader2 className="h-6 w-6 animate-spin" />,
          title: "Refreshing Catering Data",
          description: message || "Updating catering offers with the latest information...",
          color: "text-green-600"
        };
      case 'search':
        return {
          icon: <Loader2 className="h-5 w-5 animate-spin" />,
          title: "Searching Catering Offers",
          description: message || "Finding catering offers that match your search criteria...",
          color: "text-purple-600"
        };
      case 'create':
        return {
          icon: <Loader2 className="h-5 w-5 animate-spin" />,
          title: "Creating Catering Offer",
          description: message || "Adding new catering offer to the system...",
          color: "text-green-600"
        };
      case 'update':
        return {
          icon: <Loader2 className="h-5 w-5 animate-spin" />,
          title: "Updating Catering Offer",
          description: message || "Saving changes to the catering offer...",
          color: "text-blue-600"
        };
      case 'delete':
        return {
          icon: <Loader2 className="h-5 w-5 animate-spin" />,
          title: "Deleting Catering Offer",
          description: message || "Removing catering offer from the system...",
          color: "text-red-600"
        };
      default:
        return {
          icon: <Loader2 className="h-6 w-6 animate-spin" />,
          title: "Processing",
          description: message || "Please wait...",
          color: "text-gray-600"
        };
    }
  };

  const config = getLoadingConfig();

  // Inline loading for operations (create, update, delete, search)
  if (['create', 'update', 'delete', 'search'].includes(type)) {
    return (
      <div className="flex items-center space-x-2 text-sm">
        <div className={config.color}>
          {config.icon}
        </div>
        <span className="text-muted-foreground">{config.description}</span>
        {!isOnline && (
          <Badge variant="destructive" className="flex items-center gap-1">
            <WifiOff className="h-3 w-3" />
            Offline
          </Badge>
        )}
      </div>
    );
  }

  // Full page loading for initial and refresh
  return (
    <Card className="w-full">
      <CardHeader className="text-center pb-4">
        <div className="mx-auto mb-4 w-16 h-16 bg-muted rounded-full flex items-center justify-center">
          <div className={config.color}>
            {config.icon}
          </div>
        </div>
        <h3 className="text-lg font-semibold">{config.title}</h3>
        <p className="text-sm text-muted-foreground">{config.description}</p>
        
        {/* Network status indicator */}
        <div className="flex items-center justify-center space-x-2 mt-2">
          {isOnline ? (
            <>
              <Wifi className="h-4 w-4 text-green-600" />
              <span className="text-xs text-green-600">Connected</span>
            </>
          ) : (
            <>
              <WifiOff className="h-4 w-4 text-red-600" />
              <span className="text-xs text-red-600">Offline</span>
            </>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Progress bar if enabled */}
        {showProgress && (
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Progress</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <div className="w-full bg-muted rounded-full h-2">
              <div 
                className="bg-primary h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
              />
            </div>
          </div>
        )}

        {/* Loading skeleton for table structure */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-4 w-24" />
          </div>
          
          {/* Table header skeleton */}
          <div className="hidden md:flex space-x-4 pb-2 border-b">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-28" />
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-16" />
          </div>
          
          {/* Table rows skeleton */}
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="space-y-2">
              {/* Desktop row */}
              <div className="hidden md:flex space-x-4 py-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-28" />
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-8 w-8 rounded" />
              </div>
              
              {/* Mobile card */}
              <div className="md:hidden">
                <Card>
                  <CardContent className="p-4 space-y-3">
                    <div className="flex items-center space-x-3">
                      <Skeleton className="h-10 w-10 rounded" />
                      <div className="space-y-1 flex-1">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-3 w-20" />
                      </div>
                      <Skeleton className="h-8 w-8 rounded" />
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <Skeleton className="h-3 w-24" />
                        <Skeleton className="h-3 w-16" />
                      </div>
                      <div className="flex justify-between">
                        <Skeleton className="h-3 w-28" />
                        <Skeleton className="h-3 w-20" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          ))}
        </div>

        {/* Loading time indicator */}
        <div className="flex items-center justify-center space-x-2 text-xs text-muted-foreground pt-4">
          <Clock className="h-3 w-3" />
          <span>This usually takes a few seconds</span>
        </div>
      </CardContent>
    </Card>
  );
});

CateringLoadingStates.displayName = "CateringLoadingStates";

/**
 * Simple inline loading indicator for buttons and small operations
 */
export const InlineLoadingIndicator = memo(({
  message = "Loading...",
  size = "sm"
}: {
  message?: string;
  size?: "xs" | "sm" | "md";
}) => {
  const sizeClasses = {
    xs: "h-3 w-3",
    sm: "h-4 w-4", 
    md: "h-5 w-5"
  };

  return (
    <div className="flex items-center space-x-2">
      <Loader2 className={`${sizeClasses[size]} animate-spin`} />
      <span className="text-sm text-muted-foreground">{message}</span>
    </div>
  );
});

InlineLoadingIndicator.displayName = "InlineLoadingIndicator";

/**
 * Loading overlay for forms and dialogs
 */
export const LoadingOverlay = memo(({
  message = "Processing...",
  show = false
}: {
  message?: string;
  show?: boolean;
}) => {
  if (!show) return null;

  return (
    <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
      <div className="flex flex-col items-center space-y-3 p-6">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="text-sm font-medium text-center">{message}</p>
      </div>
    </div>
  );
});

LoadingOverlay.displayName = "LoadingOverlay";