"use client"

import { memo, useCallback } from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Loader2, AlertTriangle, UtensilsCrossed, Ban } from "lucide-react"
import { Catering, CateringWithBookingCount } from "@/lib/types"
import { formatCurrency } from "@/lib/utils/catering"

interface DeleteConfirmationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  catering: Catering | CateringWithBookingCount | null
  onConfirm: () => Promise<void>
  loading?: boolean
  bookingCount?: number
  error?: string | null
}

export const DeleteConfirmationDialog = memo(({
  open,
  onOpenChange,
  catering,
  onConfirm,
  loading = false,
  bookingCount = 0,
  error = null,
}: DeleteConfirmationDialogProps) => {
  if (!catering) return null

  const handleConfirm = useCallback(async () => {
    try {
      await onConfirm()
      onOpenChange(false)
    } catch (error) {
      // Error handling is done in the parent component
      console.error('Delete confirmation error:', error)
    }
  }, [onConfirm, onOpenChange])

  // Check if catering has booking associations
  const hasBookings = bookingCount > 0 || ('_count' in catering && catering._count.bookings > 0)
  const actualBookingCount = bookingCount || ('_count' in catering ? catering._count.bookings : 0)
  const canDelete = !hasBookings

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent 
        className="mobile-spacing max-w-md"
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <AlertDialogHeader>
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-destructive" aria-hidden="true" />
            <AlertDialogTitle id="delete-dialog-title">Delete Catering Offer</AlertDialogTitle>
          </div>
          <AlertDialogDescription asChild>
            <div className="space-y-4" id="delete-dialog-description">
              <p>
                {canDelete 
                  ? "Are you sure you want to delete this catering offer? This action cannot be undone."
                  : "This catering offer cannot be deleted because it is associated with existing bookings."
                }
              </p>
              
              <div className="rounded-lg border p-4 bg-muted/50">
                <h4 className="font-medium mb-2">Catering Offer Details:</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Offer Name:</span>
                    <span className="font-medium">{catering.offerName}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Price per Person:</span>
                    <span className="font-medium">{formatCurrency(catering.pricePerPerson)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">First Party Share:</span>
                    <span className="font-medium">{formatCurrency(catering.firstPartyShare)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Vendor Share:</span>
                    <span className="font-medium">{formatCurrency(catering.vendorShare)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Created:</span>
                    <span className="font-medium">
                      {catering.createdAt 
                        ? (() => {
                            try {
                              return new Date(catering.createdAt).toLocaleDateString();
                            } catch (error) {
                              return 'Invalid date';
                            }
                          })()
                        : 'N/A'
                      }
                    </span>
                  </div>
                </div>
              </div>

              {hasBookings ? (
                <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
                  <div className="flex items-start space-x-2">
                    <Ban className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-destructive">
                        Cannot Delete: Active Bookings Found
                      </p>
                      <ul className="text-xs text-destructive/80 space-y-1">
                        <li>• This catering offer is associated with {actualBookingCount} booking{actualBookingCount !== 1 ? 's' : ''}</li>
                        <li>• Remove all booking associations before deleting</li>
                        <li>• Contact bookings to change their catering selection</li>
                        <li>• Deletion will be available once no bookings reference this offer</li>
                      </ul>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
                  <div className="flex items-start space-x-2">
                    <AlertTriangle className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-destructive">
                        Warning: This action is permanent
                      </p>
                      <ul className="text-xs text-destructive/80 space-y-1">
                        <li>• The catering offer will be permanently removed from the system</li>
                        <li>• All pricing and revenue sharing configuration will be lost</li>
                        <li>• This offer will no longer be available for new bookings</li>
                        <li>• This action cannot be reversed</li>
                      </ul>
                    </div>
                  </div>
                </div>
              )}

              {error && (
                <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
                  <div className="flex items-start space-x-2">
                    <AlertTriangle className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-destructive">Error</p>
                      <p className="text-xs text-destructive/80 mt-1">{error}</p>
                    </div>
                  </div>
                </div>
              )}

              <p className="text-sm text-muted-foreground">
                {canDelete ? (
                  <>
                    Please confirm that you want to delete the catering offer <strong>{catering.offerName}</strong>, or click Cancel to abort.
                  </>
                ) : (
                  <>
                    The catering offer <strong>{catering.offerName}</strong> cannot be deleted due to existing booking associations.
                  </>
                )}
              </p>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter className="flex flex-col sm:flex-row gap-3">
          <AlertDialogCancel 
            disabled={loading}
            className="touch-target focus-enhanced flex-1 sm:flex-none"
          >
            {canDelete ? 'Cancel' : 'Close'}
          </AlertDialogCancel>
          {canDelete && (
            <AlertDialogAction
              onClick={handleConfirm}
              disabled={loading}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90 touch-target focus-enhanced flex-1 sm:flex-none"
              aria-describedby="delete-dialog-description"
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />}
              <UtensilsCrossed className="mr-2 h-4 w-4" aria-hidden="true" />
              Delete {catering.offerName}
            </AlertDialogAction>
          )}
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
})

DeleteConfirmationDialog.displayName = "DeleteConfirmationDialog"