"use client";

import { useState, use<PERSON><PERSON>back, use<PERSON><PERSON><PERSON>, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Plus, RefreshCw, AlertCircle, ChefHat, Wifi, WifiOff } from "lucide-react";

// Import components
import { CateringTable } from "./components/catering-table";
import { CateringFormDialog } from "./components/catering-form-dialog";
import { DeleteConfirmationDialog } from "./components/delete-confirmation-dialog";
import { SearchInput } from "@/components/dashboard/search-input";
import { PaginationControls } from "@/components/dashboard/pagination-controls";
import { CateringManagementErrorBoundary } from "./components/catering-management-error-boundary";
import { CateringLoadingStates, InlineLoadingIndicator } from "./components/catering-loading-states";

// Import custom hook
import { useCatering } from "@/hooks/use-catering";

// Import types
import { Catering, CateringFormData } from "@/lib/types";

// Import error monitoring
import { useCateringErrorMonitor } from "@/lib/utils/catering-error-monitoring";

interface DialogState {
  cateringForm: {
    open: boolean;
    catering: Catering | null;
    loading: boolean;
  };
  deleteConfirmation: {
    open: boolean;
    catering: Catering | null;
    loading: boolean;
    bookingCount?: number;
  };
}

export default function CateringPage() {
  // Network status tracking - initialize as true to avoid hydration mismatch
  const [isOnline, setIsOnline] = useState(true);
  const [hasMounted, setHasMounted] = useState(false);
  const { captureError, getStats } = useCateringErrorMonitor();

  useEffect(() => {
    // Set the actual online status after component mounts
    setHasMounted(true);
    setIsOnline(navigator.onLine);

    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Use custom hook for catering data management
  const {
    catering,
    loading,
    error,
    totalCatering,
    totalPages,
    currentPage,
    pageSize,
    searchQuery,
    createCatering,
    updateCatering,
    deleteCatering,
    setSearchQuery,
    setCurrentPage,
    setPageSize,
    refresh,
    clearError,
  } = useCatering();

  // Dialog states
  const [dialogs, setDialogs] = useState<DialogState>({
    cateringForm: {
      open: false,
      catering: null,
      loading: false,
    },
    deleteConfirmation: {
      open: false,
      catering: null,
      loading: false,
    },
  });

  // Catering form handlers
  const handleCreateCatering = useCallback(() => {
    setDialogs(prev => ({
      ...prev,
      cateringForm: { open: true, catering: null, loading: false },
    }));
  }, []);

  const handleEditCatering = useCallback((catering: Catering) => {
    setDialogs(prev => ({
      ...prev,
      cateringForm: { open: true, catering, loading: false },
    }));
  }, []);

  const handleCateringFormSubmit = useCallback(async (data: CateringFormData) => {
    const isEdit = !!dialogs.cateringForm.catering;
    
    setDialogs(prev => ({
      ...prev,
      cateringForm: { ...prev.cateringForm, loading: true },
    }));

    try {
      if (isEdit) {
        await updateCatering(dialogs.cateringForm.catering!.id, data);
      } else {
        await createCatering(data);
      }

      // Close dialog on success
      setDialogs(prev => ({
        ...prev,
        cateringForm: { open: false, catering: null, loading: false },
      }));
    } catch (error) {
      // Enhanced error capture with context
      captureError(
        error instanceof Error ? error : new Error('Form submission failed'),
        {
          operation: isEdit ? 'update' : 'create',
          cateringId: isEdit ? dialogs.cateringForm.catering?.id : undefined,
          cateringName: data.offerName,
          formData: data
        },
        {
          dialogState: 'form_submission',
          isEdit,
          hasNetworkConnection: isOnline
        }
      );
      
      console.error("Error submitting catering form:", error);
      
      // Keep dialog open on error so user can retry
      setDialogs(prev => ({
        ...prev,
        cateringForm: { ...prev.cateringForm, loading: false },
      }));
      
      // Re-throw to let form handle the error display
      throw error;
    }
  }, [dialogs.cateringForm.catering, createCatering, updateCatering, captureError, isOnline]);

  // Delete catering handlers
  const handleDeleteCatering = useCallback((cateringId: number) => {
    const cateringOffer = catering.find(c => c.id === cateringId);
    if (cateringOffer) {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: true, catering: cateringOffer, loading: false },
      }));
    }
  }, [catering]);

  const handleDeleteConfirm = useCallback(async () => {
    const { catering } = dialogs.deleteConfirmation;
    if (!catering) return;

    setDialogs(prev => ({
      ...prev,
      deleteConfirmation: { ...prev.deleteConfirmation, loading: true },
    }));

    try {
      await deleteCatering(catering.id);

      // Close dialog on success
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: false, catering: null, loading: false },
      }));
    } catch (error) {
      // Enhanced error capture with context
      captureError(
        error instanceof Error ? error : new Error('Delete operation failed'),
        {
          operation: 'delete',
          cateringId: catering.id,
          cateringName: catering.offerName
        },
        {
          dialogState: 'delete_confirmation',
          hasNetworkConnection: isOnline,
          errorStats: getStats()
        }
      );
      
      console.error("Error deleting catering:", error);
      
      // Keep dialog open on error so user can see the error and retry
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { ...prev.deleteConfirmation, loading: false },
      }));
    }
  }, [dialogs.deleteConfirmation.catering, deleteCatering, captureError, isOnline, getStats]);

  // Dialog close handlers
  const handleCateringFormClose = useCallback((open: boolean) => {
    if (!open) {
      setDialogs(prev => ({
        ...prev,
        cateringForm: { open: false, catering: null, loading: false },
      }));
    }
  }, []);

  const handleDeleteConfirmationClose = useCallback((open: boolean) => {
    if (!open) {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: false, catering: null, loading: false },
      }));
    }
  }, []);

  // Memoized values for better performance
  const totalCateringText = useMemo(() => 
    `${totalCatering} total catering offers`, 
    [totalCatering]
  );

  const shouldShowPagination = useMemo(() => 
    !loading && catering.length > 0, 
    [loading, catering.length]
  );

  return (
    <CateringManagementErrorBoundary onRetry={refresh}>
      <div className="h-full bg-gray-50 mobile-spacing">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900" id="page-title">
            Catering Management
          </h1>
          <p className="text-gray-600 mt-1 text-sm sm:text-base" id="page-description">
            Manage catering offers with pricing and revenue sharing
          </p>
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
          {/* Network Status Indicator - only show after mount to avoid hydration issues */}
          {hasMounted && (
            <Badge 
              variant={isOnline ? "secondary" : "destructive"} 
              className="flex items-center gap-1 sm:hidden"
            >
              {isOnline ? <Wifi className="h-3 w-3" /> : <WifiOff className="h-3 w-3" />}
              {isOnline ? "Online" : "Offline"}
            </Badge>
          )}
          
          <Button
            variant="outline"
            onClick={refresh}
            disabled={loading || (hasMounted && !isOnline)}
            className="touch-target focus-enhanced"
            aria-label="Refresh catering offers list"
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} aria-hidden="true" />
            Refresh
          </Button>
          <Button 
            onClick={handleCreateCatering} 
            disabled={hasMounted && !isOnline}
            className="touch-target focus-enhanced"
            aria-label="Add new catering offer"
          >
            <Plus className="mr-2 h-4 w-4" aria-hidden="true" />
            Add Catering Offer
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div>
              <CardTitle className="flex items-center" id="card-title">
                <ChefHat className="mr-2 h-5 w-5" aria-hidden="true" />
                Catering Offers
              </CardTitle>
              {loading ? (
                <div className="text-sm text-muted-foreground">
                  <Skeleton className="h-4 w-48" />
                </div>
              ) : (
                <CardDescription>
                  {totalCateringText}
                </CardDescription>
              )}
            </div>
            <div className="flex items-center space-x-3">
              {/* Desktop Network Status - only show after mount */}
              {hasMounted && (
                <Badge 
                  variant={isOnline ? "secondary" : "destructive"} 
                  className="hidden sm:flex items-center gap-1"
                >
                  {isOnline ? <Wifi className="h-3 w-3" /> : <WifiOff className="h-3 w-3" />}
                  {isOnline ? "Online" : "Offline"}
                </Badge>
              )}
              
              <div className="w-full lg:w-80">
                <SearchInput
                  onSearch={(!hasMounted || isOnline) ? setSearchQuery : () => {}}
                  placeholder={(!hasMounted || isOnline) ? "Search catering offers by name..." : "Search unavailable (offline)"}
                />
              </div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {error ? (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-3">
                  <div>
                    <p className="font-medium">Unable to load catering offers</p>
                    <p className="text-sm">{error}</p>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        clearError();
                        refresh();
                      }}
                      className="touch-target focus-enhanced"
                      aria-label="Try loading catering offers again"
                      disabled={loading}
                    >
                      {loading ? (
                        <InlineLoadingIndicator message="Retrying..." size="xs" />
                      ) : (
                        <>
                          <RefreshCw className="mr-2 h-3 w-3" aria-hidden="true" />
                          Try Again
                        </>
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        clearError();
                        setSearchQuery("");
                        setCurrentPage(1);
                        refresh();
                      }}
                      className="touch-target focus-enhanced"
                      aria-label="Reset search and retry loading catering offers"
                      disabled={loading}
                    >
                      {loading ? (
                        <InlineLoadingIndicator message="Resetting..." size="xs" />
                      ) : (
                        <>
                          Reset & Retry
                        </>
                      )}
                    </Button>
                    {hasMounted && !isOnline && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.location.reload()}
                        className="touch-target focus-enhanced"
                        aria-label="Refresh entire page"
                      >
                        Refresh Page
                      </Button>
                    )}
                  </div>
                  {hasMounted && !isOnline && (
                    <p className="text-xs text-muted-foreground">
                      You appear to be offline. Please check your internet connection.
                    </p>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          ) : loading && catering.length === 0 ? (
            <CateringLoadingStates 
              type="initial" 
              isOnline={hasMounted ? isOnline : true}
              message="Loading catering offers and setting up the management interface..."
            />
          ) : (
            <>
              <CateringTable
                catering={catering}
                loading={loading}
                onEdit={handleEditCatering}
                onDelete={handleDeleteCatering}
                onRefresh={refresh}
              />
              
              {shouldShowPagination && (
                <div className="mt-4 sm:mt-6">
                  <PaginationControls
                    currentPage={currentPage}
                    totalPages={totalPages}
                    pageSize={pageSize}
                    totalItems={totalCatering}
                    onPageChange={setCurrentPage}
                    onPageSizeChange={setPageSize}
                  />
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Dialogs */}
      <CateringFormDialog
        open={dialogs.cateringForm.open}
        onOpenChange={handleCateringFormClose}
        catering={dialogs.cateringForm.catering}
        onSubmit={handleCateringFormSubmit}
        loading={dialogs.cateringForm.loading}
      />

      <DeleteConfirmationDialog
        open={dialogs.deleteConfirmation.open}
        onOpenChange={handleDeleteConfirmationClose}
        catering={dialogs.deleteConfirmation.catering}
        onConfirm={handleDeleteConfirm}
        loading={dialogs.deleteConfirmation.loading}
      />
      </div>
    </CateringManagementErrorBoundary>
  );
}