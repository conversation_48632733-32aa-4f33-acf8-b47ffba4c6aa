"use client"

import { useState, memo, useMemo, useCallback } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent } from "@/components/ui/card"
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Key,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Users,
} from "lucide-react"
import { User, UserRole } from "@/lib/types"
import { format } from "date-fns"
import { UsersTableSkeleton } from "./users-table-skeleton"

interface UsersTableProps {
  users: User[]
  loading: boolean
  onEdit: (user: User) => void
  onDelete: (userId: number) => void
  onChangePassword: (userId: number) => void
  onRefresh: () => void
}

type SortField = 'firstName' | 'lastName' | 'username' | 'role' | 'createdAt'
type SortDirection = 'asc' | 'desc'

interface SortConfig {
  field: SortField
  direction: SortDirection
}

const getRoleBadgeVariant = (role: UserRole) => {
  switch (role) {
    case 'ADMIN':
      return 'destructive'
    case 'LOGISTICS':
      return 'default'
    case 'RECEIPTION':
      return 'secondary'
    default:
      return 'outline'
  }
}

// Mobile card component for individual users - memoized for performance
const UserCard = memo(({ 
  user, 
  onEdit, 
  onDelete, 
  onChangePassword 
}: { 
  user: User
  onEdit: (user: User) => void
  onDelete: (userId: number) => void
  onChangePassword: (userId: number) => void
}) => {
  return (
    <Card className="mb-4">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <h3 className="font-medium text-base">
                {user.firstName} {user.lastName}
              </h3>
              {user.isSuperUser && (
                <Badge variant="outline" className="text-xs">Super User</Badge>
              )}
            </div>
            
            <div className="space-y-1 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <span className="font-medium">Username:</span>
                <span>{user.username}</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <span className="font-medium">Role:</span>
                <Badge variant={getRoleBadgeVariant(user.role)} className="text-xs">
                  {user.role}
                </Badge>
              </div>
              
              <div className="flex items-center space-x-2">
                <span className="font-medium">Created:</span>
                <span>
                  {user.createdAt 
                    ? (() => {
                        try {
                          return format(new Date(user.createdAt), 'MMM dd, yyyy');
                        } catch (error) {
                          return 'Invalid date';
                        }
                      })()
                    : 'N/A'
                  }
                </span>
              </div>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-9 w-9 p-0 touch-manipulation">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem 
                onClick={() => onEdit(user)}
                className="cursor-pointer touch-manipulation min-h-[44px]"
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => onChangePassword(user.id)}
                className="cursor-pointer touch-manipulation min-h-[44px]"
              >
                <Key className="mr-2 h-4 w-4" />
                Change Password
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDelete(user.id)}
                className="text-destructive cursor-pointer touch-manipulation min-h-[44px]"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  )
})

export const UsersTable = memo(({
  users,
  loading,
  onEdit,
  onDelete,
  onChangePassword,
  onRefresh,
}: UsersTableProps) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    field: 'createdAt',
    direction: 'desc',
  })

  const handleSort = useCallback((field: SortField) => {
    setSortConfig(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }))
  }, [])

  const getSortIcon = useCallback((field: SortField) => {
    if (sortConfig.field !== field) {
      return <ArrowUpDown className="ml-2 h-4 w-4" />
    }
    return sortConfig.direction === 'asc' ? (
      <ArrowUp className="ml-2 h-4 w-4" />
    ) : (
      <ArrowDown className="ml-2 h-4 w-4" />
    )
  }, [sortConfig.field, sortConfig.direction])

  const sortedUsers = useMemo(() => {
    return [...users].sort((a, b) => {
      const { field, direction } = sortConfig
      let aValue: any = a[field]
      let bValue: any = b[field]

      // Handle date sorting
      if (field === 'createdAt') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      }

      // Handle string sorting
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (aValue < bValue) {
        return direction === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return direction === 'asc' ? 1 : -1
      }
      return 0
    })
  }, [users, sortConfig])

  if (loading) {
    return <UsersTableSkeleton />
  }

  if (users.length === 0) {
    return (
      <>
        {/* Mobile empty state */}
        <div className="block md:hidden">
          <Card>
            <CardContent className="p-8 text-center">
              <div className="flex flex-col items-center justify-center space-y-4">
                <Users className="h-12 w-12 text-muted-foreground" />
                <div className="space-y-2">
                  <p className="text-lg font-medium">No users found</p>
                  <p className="text-sm text-muted-foreground">
                    Try adjusting your search or add a new user
                  </p>
                </div>
                <Button variant="outline" onClick={onRefresh} className="min-h-[44px] px-4">
                  Refresh
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Desktop empty state */}
        <div className="hidden md:block rounded-md border">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="min-w-[150px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('firstName')}
                      className="h-auto p-0 font-semibold"
                    >
                      Name
                      {getSortIcon('firstName')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[120px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('username')}
                      className="h-auto p-0 font-semibold"
                    >
                      Username
                      {getSortIcon('username')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[100px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('role')}
                      className="h-auto p-0 font-semibold"
                    >
                      Role
                      {getSortIcon('role')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[100px] hidden lg:table-cell">Super User</TableHead>
                  <TableHead className="min-w-[120px] hidden lg:table-cell">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('createdAt')}
                      className="h-auto p-0 font-semibold"
                    >
                      Created
                      {getSortIcon('createdAt')}
                    </Button>
                  </TableHead>
                  <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    <div className="flex flex-col items-center justify-center space-y-2">
                      <p className="text-muted-foreground">No users found</p>
                      <Button variant="outline" onClick={onRefresh} className="min-h-[44px] px-4">
                        Refresh
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>
      </>
    )
  }

  return (
    <>
      {/* Mobile card view */}
      <div className="block md:hidden">
        {/* Mobile sort controls */}
        <div className="flex items-center justify-between mb-4 p-3 bg-muted/50 rounded-lg">
          <span className="text-sm font-medium">Sort by:</span>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="min-h-[40px]">
                {sortConfig.field === 'firstName' && 'Name'}
                {sortConfig.field === 'username' && 'Username'}
                {sortConfig.field === 'role' && 'Role'}
                {sortConfig.field === 'createdAt' && 'Created'}
                {sortConfig.direction === 'asc' ? (
                  <ArrowUp className="ml-2 h-4 w-4" />
                ) : (
                  <ArrowDown className="ml-2 h-4 w-4" />
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-40">
              <DropdownMenuItem onClick={() => handleSort('firstName')}>
                Name {getSortIcon('firstName')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSort('username')}>
                Username {getSortIcon('username')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSort('role')}>
                Role {getSortIcon('role')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSort('createdAt')}>
                Created {getSortIcon('createdAt')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="space-y-4">
          {sortedUsers.map((user) => (
            <UserCard
              key={user.id}
              user={user}
              onEdit={onEdit}
              onDelete={onDelete}
              onChangePassword={onChangePassword}
            />
          ))}
        </div>
      </div>

      {/* Desktop table view */}
      <div className="hidden md:block rounded-md border">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="min-w-[150px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('firstName')}
                    className="h-auto p-0 font-semibold"
                  >
                    Name
                    {getSortIcon('firstName')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[120px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('username')}
                    className="h-auto p-0 font-semibold"
                  >
                    Username
                    {getSortIcon('username')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[100px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('role')}
                    className="h-auto p-0 font-semibold"
                  >
                    Role
                    {getSortIcon('role')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[100px] hidden lg:table-cell">Super User</TableHead>
                <TableHead className="min-w-[120px] hidden lg:table-cell">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('createdAt')}
                    className="h-auto p-0 font-semibold"
                  >
                    Created
                    {getSortIcon('createdAt')}
                  </Button>
                </TableHead>
                <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell className="font-medium">
                    {user.firstName} {user.lastName}
                  </TableCell>
                  <TableCell>{user.username}</TableCell>
                  <TableCell>
                    <Badge variant={getRoleBadgeVariant(user.role)}>
                      {user.role}
                    </Badge>
                  </TableCell>
                  <TableCell className="hidden lg:table-cell">
                    {user.isSuperUser ? (
                      <Badge variant="outline">Yes</Badge>
                    ) : (
                      <span className="text-muted-foreground">No</span>
                    )}
                  </TableCell>
                  <TableCell className="text-muted-foreground hidden lg:table-cell">
                    {format(new Date(user.createdAt), 'MMM dd, yyyy')}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-9 w-9 p-0 touch-manipulation">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuItem 
                          onClick={() => onEdit(user)}
                          className="cursor-pointer touch-manipulation min-h-[44px] md:min-h-auto"
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => onChangePassword(user.id)}
                          className="cursor-pointer touch-manipulation min-h-[44px] md:min-h-auto"
                        >
                          <Key className="mr-2 h-4 w-4" />
                          Change Password
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => onDelete(user.id)}
                          className="text-destructive cursor-pointer touch-manipulation min-h-[44px] md:min-h-auto"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </>
  )
})