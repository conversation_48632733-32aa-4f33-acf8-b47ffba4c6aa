"use client"

import { useEffect, useState, memo, use<PERSON><PERSON>back, useMemo } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  <PERSON>alog<PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Eye, EyeOff, Check, X, AlertCircle, CheckCircle2 } from "lucide-react"
import { PasswordChangeData } from "@/lib/types"
import { passwordChangeSchema } from "@/lib/validations/user"
import { 
  mapServerErrorsToForm, 
  getApi<PERSON><PERSON>r<PERSON>essage, 
  createError<PERSON>ummary,
  hasFormErrors 
} from "@/lib/utils/form-errors"

interface PasswordChangeDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  userId: number
  username: string
  onSubmit: (data: PasswordChangeData) => Promise<void>
  loading?: boolean
}

interface PasswordStrength {
  score: number
  label: string
  color: string
  checks: {
    length: boolean
    uppercase: boolean
    lowercase: boolean
    number: boolean
    special: boolean
  }
}

const calculatePasswordStrength = (password: string): PasswordStrength => {
  const checks = {
    length: password.length >= 6,
    uppercase: /[A-Z]/.test(password),
    lowercase: /[a-z]/.test(password),
    number: /\d/.test(password),
    special: /[!@#$%^&*(),.?":{}|<>]/.test(password),
  }

  const score = Object.values(checks).filter(Boolean).length
  
  let label = 'Very Weak'
  let color = 'bg-red-500'
  
  if (score >= 5) {
    label = 'Very Strong'
    color = 'bg-green-500'
  } else if (score >= 4) {
    label = 'Strong'
    color = 'bg-green-400'
  } else if (score >= 3) {
    label = 'Medium'
    color = 'bg-yellow-500'
  } else if (score >= 2) {
    label = 'Weak'
    color = 'bg-orange-500'
  }

  return { score, label, color, checks }
}

export const PasswordChangeDialog = memo(({
  open,
  onOpenChange,
  userId,
  username,
  onSubmit,
  loading = false,
}: PasswordChangeDialogProps) => {
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [serverError, setServerError] = useState<string>("")
  const [passwordStrength, setPasswordStrength] = useState<PasswordStrength>({
    score: 0,
    label: 'Very Weak',
    color: 'bg-red-500',
    checks: {
      length: false,
      uppercase: false,
      lowercase: false,
      number: false,
      special: false,
    },
  })

  const form = useForm<PasswordChangeData>({
    resolver: zodResolver(passwordChangeSchema),
    mode: "onChange", // Enable real-time validation
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  })

  const password = form.watch('password')

  useEffect(() => {
    if (password) {
      setPasswordStrength(calculatePasswordStrength(password))
    } else {
      setPasswordStrength({
        score: 0,
        label: 'Very Weak',
        color: 'bg-red-500',
        checks: {
          length: false,
          uppercase: false,
          lowercase: false,
          number: false,
          special: false,
        },
      })
    }
  }, [password])

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (open) {
      form.reset({
        password: '',
        confirmPassword: '',
      })
      setShowPassword(false)
      setShowConfirmPassword(false)
      setServerError("") // Clear server errors
    }
  }, [open, form])

  const handleSubmit = useCallback(async (data: PasswordChangeData) => {
    setServerError("") // Clear previous server errors
    
    try {
      await onSubmit(data)
      onOpenChange(false)
    } catch (error) {
      console.error('Password change error:', error)
      
      // Handle server validation errors
      if (error && typeof error === 'object' && 'details' in error) {
        mapServerErrorsToForm(error as any, form.setError as any)
      }
      
      // Set general server error message
      const errorMessage = getApiErrorMessage(error)
      setServerError(errorMessage)
    }
  }, [onSubmit, onOpenChange, form])

  const handleCancel = useCallback(() => {
    form.reset()
    setServerError("")
    onOpenChange(false)
  }, [form, onOpenChange])

  const strengthPercentage = useMemo(() => 
    (passwordStrength.score / 5) * 100, 
    [passwordStrength.score]
  )
  
  const errorSummary = useMemo(() => 
    createErrorSummary(form.formState.errors, serverError), 
    [form.formState.errors, serverError]
  )
  
  const canSubmit = useMemo(() => 
    form.formState.isValid && !loading && passwordStrength.score >= 3, 
    [form.formState.isValid, loading, passwordStrength.score]
  )

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Change Password</DialogTitle>
          <DialogDescription>
            Change the password for user <strong>{username}</strong>. The user will need to use the new password for their next login.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            {/* Error Summary */}
            {errorSummary.hasErrors && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-1">
                    <p className="font-medium">{errorSummary.summary}</p>
                    {errorSummary.messages.length > 1 && (
                      <ul className="list-disc list-inside text-sm space-y-1">
                        {errorSummary.messages.map((message, index) => (
                          <li key={index}>{message}</li>
                        ))}
                      </ul>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            )}
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>New Password</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type={showPassword ? "text" : "password"}
                        placeholder="Enter new password"
                        {...field}
                        disabled={loading}
                        className="pr-10"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                        disabled={loading}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {password && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Password Strength</span>
                  <Badge variant="outline" className="text-xs">
                    {passwordStrength.label}
                  </Badge>
                </div>
                <Progress value={strengthPercentage} className="h-2" />
                
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="flex items-center space-x-1">
                    {passwordStrength.checks.length ? (
                      <Check className="h-3 w-3 text-green-500" />
                    ) : (
                      <X className="h-3 w-3 text-red-500" />
                    )}
                    <span>At least 6 characters</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    {passwordStrength.checks.uppercase ? (
                      <Check className="h-3 w-3 text-green-500" />
                    ) : (
                      <X className="h-3 w-3 text-red-500" />
                    )}
                    <span>Uppercase letter</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    {passwordStrength.checks.lowercase ? (
                      <Check className="h-3 w-3 text-green-500" />
                    ) : (
                      <X className="h-3 w-3 text-red-500" />
                    )}
                    <span>Lowercase letter</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    {passwordStrength.checks.number ? (
                      <Check className="h-3 w-3 text-green-500" />
                    ) : (
                      <X className="h-3 w-3 text-red-500" />
                    )}
                    <span>Number</span>
                  </div>
                  <div className="flex items-center space-x-1 col-span-2">
                    {passwordStrength.checks.special ? (
                      <Check className="h-3 w-3 text-green-500" />
                    ) : (
                      <X className="h-3 w-3 text-red-500" />
                    )}
                    <span>Special character (!@#$%^&*)</span>
                  </div>
                </div>
              </div>
            )}

            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Confirm New Password</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type={showConfirmPassword ? "text" : "password"}
                        placeholder="Confirm new password"
                        {...field}
                        disabled={loading}
                        className="pr-10"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        disabled={loading}
                      >
                        {showConfirmPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </FormControl>
                  <FormDescription>
                    Re-enter the password to confirm it matches.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
              {/* Form validation status */}
              <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-2 sm:mb-0">
                {canSubmit ? (
                  <div className="flex items-center text-green-600">
                    <CheckCircle2 className="h-4 w-4 mr-1" />
                    Password is strong enough
                  </div>
                ) : passwordStrength.score > 0 && passwordStrength.score < 3 ? (
                  <div className="flex items-center text-yellow-600">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    Password needs to be stronger
                  </div>
                ) : hasFormErrors(form.formState.errors) || serverError ? (
                  <div className="flex items-center text-red-600">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {errorSummary.errorCount} error{errorSummary.errorCount !== 1 ? 's' : ''}
                  </div>
                ) : null}
              </div>
              
              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={!canSubmit}
                  className="min-w-[140px]"
                >
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Change Password
                </Button>
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
})

PasswordChangeDialog.displayName = "PasswordChangeDialog"