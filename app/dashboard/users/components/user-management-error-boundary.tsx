"use client";

import React from "react";
import { ErrorBoundary } from "@/components/ui/error-boundary";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Users, RefreshCw, AlertTriangle } from "lucide-react";

interface UserManagementErrorBoundaryProps {
  children: React.ReactNode;
  onRetry?: () => void;
}

export function UserManagementErrorBoundary({ 
  children, 
  onRetry 
}: UserManagementErrorBoundaryProps) {
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    // Log user management specific errors
    console.error("User Management Error:", {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      context: "user-management",
    });

    // You could send this to your monitoring service
    // errorMonitoringService.captureException(error, {
    //   tags: { component: "user-management" },
    //   extra: errorInfo
    // });
  };

  const fallbackComponent = (
    <Card className="w-full">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
          <AlertTriangle className="w-6 h-6 text-red-600" />
        </div>
        <CardTitle className="text-xl font-bold text-gray-900 flex items-center justify-center">
          <Users className="mr-2 h-5 w-5" />
          User Management Error
        </CardTitle>
        <CardDescription>
          There was a problem loading the user management interface.
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            The user management system encountered an unexpected error. 
            This could be due to a network issue or a temporary server problem.
          </AlertDescription>
        </Alert>

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {onRetry && (
            <Button onClick={onRetry} className="flex items-center">
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry Loading Users
            </Button>
          )}
          
          <Button 
            variant="outline" 
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh Page
          </Button>
        </div>

        <div className="text-center text-sm text-muted-foreground">
          <p>If this problem persists, please contact your system administrator.</p>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <ErrorBoundary
      fallback={fallbackComponent}
      onError={handleError}
      maxRetries={2}
      showErrorDetails={process.env.NODE_ENV === "development"}
    >
      {children}
    </ErrorBoundary>
  );
}