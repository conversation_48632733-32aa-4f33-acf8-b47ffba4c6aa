"use client"

import { memo, useCallback, useMemo } from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { Loader2, AlertTriangle } from "lucide-react"
import { User } from "@/lib/types"

interface DeleteConfirmationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  user: User | null
  onConfirm: () => Promise<void>
  loading?: boolean
}

export const DeleteConfirmationDialog = memo(({
  open,
  onOpenChange,
  user,
  onConfirm,
  loading = false,
}: DeleteConfirmationDialogProps) => {
  if (!user) return null

  const handleConfirm = useCallback(async () => {
    try {
      await onConfirm()
      onOpenChange(false)
    } catch (error) {
      // Error handling is done in the parent component
      console.error('Delete confirmation error:', error)
    }
  }, [onConfirm, onOpenChange])

  const getRoleBadgeVariant = useCallback((role: string) => {
    switch (role) {
      case 'ADMIN':
        return 'destructive'
      case 'LOGISTICS':
        return 'default'
      case 'RECEIPTION':
        return 'secondary'
      default:
        return 'outline'
    }
  }, [])

  const roleBadgeVariant = useMemo(() => 
    getRoleBadgeVariant(user.role), 
    [user.role, getRoleBadgeVariant]
  )

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            <AlertDialogTitle>Delete User Account</AlertDialogTitle>
          </div>
          <AlertDialogDescription asChild>
            <div className="space-y-3">
              <p>
                Are you sure you want to delete this user account? This action cannot be undone.
              </p>
              
              <div className="rounded-lg border p-4 bg-muted/50">
                <h4 className="font-medium mb-2">User Details:</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Name:</span>
                    <span className="font-medium">
                      {user.firstName} {user.lastName}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Username:</span>
                    <span className="font-medium">{user.username}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Role:</span>
                    <Badge variant={roleBadgeVariant}>
                      {user.role}
                    </Badge>
                  </div>
                  {user.isSuperUser && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Super User:</span>
                      <Badge variant="outline">Yes</Badge>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Created:</span>
                    <span className="font-medium">
                      {user.createdAt 
                        ? (() => {
                            try {
                              return new Date(user.createdAt).toLocaleDateString();
                            } catch (error) {
                              return 'Invalid date';
                            }
                          })()
                        : 'N/A'
                      }
                    </span>
                  </div>
                </div>
              </div>

              <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-destructive">
                      Warning: This action is permanent
                    </p>
                    <ul className="text-xs text-destructive/80 space-y-1">
                      <li>• The user will immediately lose access to the system</li>
                      <li>• All user data and history will be permanently removed</li>
                      <li>• Any records created by this user may be affected</li>
                      <li>• This action cannot be reversed</li>
                    </ul>
                  </div>
                </div>
              </div>

              <p className="text-sm text-muted-foreground">
                Please type the username <strong>{user.username}</strong> to confirm deletion, or click Cancel to abort.
              </p>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter>
          <AlertDialogCancel disabled={loading}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={loading}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Delete User
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
})

DeleteConfirmationDialog.displayName = "DeleteConfirmationDialog"