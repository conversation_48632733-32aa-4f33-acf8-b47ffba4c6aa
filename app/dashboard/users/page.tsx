"use client";

import { useState, use<PERSON><PERSON>back, useMemo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Plus, RefreshCw, AlertCircle, Users } from "lucide-react";

// Import components
import { UsersTable } from "./components/users-table";
import { UserFormDialog } from "./components/user-form-dialog";
import { PasswordChangeDialog } from "./components/password-change-dialog";
import { DeleteConfirmationDialog } from "./components/delete-confirmation-dialog";
import { SearchInput } from "./components/search-input";
import { PaginationControls } from "./components/pagination-controls";
import { UserManagementErrorBoundary } from "./components/user-management-error-boundary";

// Import custom hook
import { useUsers } from "@/hooks/use-users";

// Import types
import { User, UserFormData, PasswordChangeData } from "@/lib/types";

interface DialogState {
  userForm: {
    open: boolean;
    user: User | null;
    loading: boolean;
  };
  passwordChange: {
    open: boolean;
    userId: number | null;
    username: string;
    loading: boolean;
  };
  deleteConfirmation: {
    open: boolean;
    user: User | null;
    loading: boolean;
  };
}

export default function UsersPage() {
  // Use custom hook for user data management
  const {
    users,
    loading,
    error,
    totalUsers,
    totalPages,
    currentPage,
    pageSize,
    searchQuery,
    createUser,
    updateUser,
    deleteUser,
    changePassword,
    setSearchQuery,
    setCurrentPage,
    setPageSize,
    refresh,
    clearError,
  } = useUsers();

  // Dialog states
  const [dialogs, setDialogs] = useState<DialogState>({
    userForm: {
      open: false,
      user: null,
      loading: false,
    },
    passwordChange: {
      open: false,
      userId: null,
      username: "",
      loading: false,
    },
    deleteConfirmation: {
      open: false,
      user: null,
      loading: false,
    },
  });

  // User form handlers
  const handleCreateUser = useCallback(() => {
    setDialogs(prev => ({
      ...prev,
      userForm: { open: true, user: null, loading: false },
    }));
  }, []);

  const handleEditUser = useCallback((user: User) => {
    setDialogs(prev => ({
      ...prev,
      userForm: { open: true, user, loading: false },
    }));
  }, []);

  const handleUserFormSubmit = useCallback(async (data: UserFormData) => {
    const isEdit = !!dialogs.userForm.user;
    
    setDialogs(prev => ({
      ...prev,
      userForm: { ...prev.userForm, loading: true },
    }));

    try {
      if (isEdit) {
        await updateUser(dialogs.userForm.user!.id, data);
      } else {
        await createUser(data);
      }

      // Close dialog on success
      setDialogs(prev => ({
        ...prev,
        userForm: { open: false, user: null, loading: false },
      }));
    } catch (error) {
      // Error handling is done in the hook
      console.error("Error submitting user form:", error);
    } finally {
      setDialogs(prev => ({
        ...prev,
        userForm: { ...prev.userForm, loading: false },
      }));
    }
  }, [dialogs.userForm.user, createUser, updateUser]);

  // Password change handlers
  const handleChangePassword = useCallback((userId: number) => {
    const user = users.find(u => u.id === userId);
    if (user) {
      setDialogs(prev => ({
        ...prev,
        passwordChange: {
          open: true,
          userId,
          username: user.username,
          loading: false,
        },
      }));
    }
  }, [users]);

  const handlePasswordChangeSubmit = useCallback(async (data: PasswordChangeData) => {
    const { userId } = dialogs.passwordChange;
    if (!userId) return;

    setDialogs(prev => ({
      ...prev,
      passwordChange: { ...prev.passwordChange, loading: true },
    }));

    try {
      await changePassword(userId, data);

      // Close dialog on success
      setDialogs(prev => ({
        ...prev,
        passwordChange: {
          open: false,
          userId: null,
          username: "",
          loading: false,
        },
      }));
    } catch (error) {
      // Error handling is done in the hook
      console.error("Error changing password:", error);
    } finally {
      setDialogs(prev => ({
        ...prev,
        passwordChange: { ...prev.passwordChange, loading: false },
      }));
    }
  }, [dialogs.passwordChange.userId, changePassword]);

  // Delete user handlers
  const handleDeleteUser = useCallback((userId: number) => {
    const user = users.find(u => u.id === userId);
    if (user) {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: true, user, loading: false },
      }));
    }
  }, [users]);

  const handleDeleteConfirm = useCallback(async () => {
    const { user } = dialogs.deleteConfirmation;
    if (!user) return;

    setDialogs(prev => ({
      ...prev,
      deleteConfirmation: { ...prev.deleteConfirmation, loading: true },
    }));

    try {
      await deleteUser(user.id);

      // Close dialog on success
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: false, user: null, loading: false },
      }));
    } catch (error) {
      // Error handling is done in the hook
      console.error("Error deleting user:", error);
    } finally {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { ...prev.deleteConfirmation, loading: false },
      }));
    }
  }, [dialogs.deleteConfirmation.user, deleteUser]);

  // Dialog close handlers
  const handleUserFormClose = useCallback((open: boolean) => {
    if (!open) {
      setDialogs(prev => ({
        ...prev,
        userForm: { open: false, user: null, loading: false },
      }));
    }
  }, []);

  const handlePasswordChangeClose = useCallback((open: boolean) => {
    if (!open) {
      setDialogs(prev => ({
        ...prev,
        passwordChange: {
          open: false,
          userId: null,
          username: "",
          loading: false,
        },
      }));
    }
  }, []);

  const handleDeleteConfirmationClose = useCallback((open: boolean) => {
    if (!open) {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: false, user: null, loading: false },
      }));
    }
  }, []);

  // Memoized values for better performance
  const totalUsersText = useMemo(() => 
    `${totalUsers} total users`, 
    [totalUsers]
  );

  const shouldShowPagination = useMemo(() => 
    !loading && users.length > 0, 
    [loading, users.length]
  );

  return (
    <UserManagementErrorBoundary onRetry={refresh}>
      <div className="h-full bg-gray-50 p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Users</h1>
          <p className="text-gray-600 mt-1 text-sm sm:text-base">Manage user accounts and permissions</p>
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
          <Button
            variant="outline"
            onClick={refresh}
            disabled={loading}
            className="min-h-[44px] sm:min-h-auto"
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} />
            Refresh
          </Button>
          <Button onClick={handleCreateUser} className="min-h-[44px] sm:min-h-auto">
            <Plus className="mr-2 h-4 w-4" />
            Add User
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div>
              <CardTitle className="flex items-center">
                <Users className="mr-2 h-5 w-5" />
                User Management
              </CardTitle>
              {loading ? (
                <div className="text-sm text-muted-foreground">
                  <Skeleton className="h-4 w-48" />
                </div>
              ) : (
                <CardDescription>
                  {totalUsersText}
                </CardDescription>
              )}
            </div>
            <div className="w-full lg:w-80">
              <SearchInput
                onSearch={setSearchQuery}
                placeholder="Search users by name or username..."
              />
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {error ? (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {error}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    clearError();
                    refresh();
                  }}
                  className="ml-2"
                >
                  Try Again
                </Button>
              </AlertDescription>
            </Alert>
          ) : (
            <>
              <UsersTable
                users={users}
                loading={loading}
                onEdit={handleEditUser}
                onDelete={handleDeleteUser}
                onChangePassword={handleChangePassword}
                onRefresh={refresh}
              />
              
              {shouldShowPagination && (
                <div className="mt-4 sm:mt-6">
                  <PaginationControls
                    currentPage={currentPage}
                    totalPages={totalPages}
                    pageSize={pageSize}
                    totalItems={totalUsers}
                    onPageChange={setCurrentPage}
                    onPageSizeChange={setPageSize}
                  />
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Dialogs */}
      <UserFormDialog
        open={dialogs.userForm.open}
        onOpenChange={handleUserFormClose}
        user={dialogs.userForm.user}
        onSubmit={handleUserFormSubmit}
        loading={dialogs.userForm.loading}
      />

      <PasswordChangeDialog
        open={dialogs.passwordChange.open}
        onOpenChange={handlePasswordChangeClose}
        userId={dialogs.passwordChange.userId || 0}
        username={dialogs.passwordChange.username}
        onSubmit={handlePasswordChangeSubmit}
        loading={dialogs.passwordChange.loading}
      />

      <DeleteConfirmationDialog
        open={dialogs.deleteConfirmation.open}
        onOpenChange={handleDeleteConfirmationClose}
        user={dialogs.deleteConfirmation.user}
        onConfirm={handleDeleteConfirm}
        loading={dialogs.deleteConfirmation.loading}
      />
      </div>
    </UserManagementErrorBoundary>
  );
}