"use client";

import { useState, use<PERSON><PERSON>back, use<PERSON>em<PERSON>, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Plus, RefreshCw, AlertCircle, Building2, Wifi, WifiOff, Filter } from "lucide-react";

// Import components
import { ResourcesTable } from "./components/resources-table";
import { ResourceFormDialog } from "./components/resource-form-dialog";
import { DeleteConfirmationDialog } from "./components/delete-confirmation-dialog";
import { SearchInput } from "@/components/dashboard/search-input";
import { PaginationControls } from "@/components/dashboard/pagination-controls";
import { ResourceManagementErrorBoundary } from "./components/resource-management-error-boundary";

// Import custom hook
import { useResources } from "@/hooks/use-resources";

// Import types
import { 
  Resource, 
  ResourceFormData, 
  Amenity, 
  ResourceType,
  RESOURCE_TYPE_CONFIG,
  getResourceTypeDisplayName
} from "@/lib/types";

interface DialogState {
  resourceForm: {
    open: boolean;
    resource: Resource | null;
    loading: boolean;
  };
  deleteConfirmation: {
    open: boolean;
    resource: Resource | null;
    loading: boolean;
  };
}

export default function ResourcesPage() {
  // Network status tracking - initialize as true to avoid hydration mismatch
  const [isOnline, setIsOnline] = useState(true);
  const [hasMounted, setHasMounted] = useState(false);
  const [availableAmenities, setAvailableAmenities] = useState<Amenity[]>([]);
  const [amenitiesLoading, setAmenitiesLoading] = useState(true);

  useEffect(() => {
    // Set the actual online status after component mounts
    setHasMounted(true);
    setIsOnline(navigator.onLine);

    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Fetch available amenities for the form
  useEffect(() => {
    const fetchAmenities = async () => {
      try {
        setAmenitiesLoading(true);
        const response = await fetch('/api/amenities?limit=100'); // Get all amenities
        const result = await response.json();
        
        if (result.success && result.data) {
          setAvailableAmenities(result.data.data);
        }
      } catch (error) {
        console.error('Error fetching amenities:', error);
      } finally {
        setAmenitiesLoading(false);
      }
    };

    fetchAmenities();
  }, []);

  // Use custom hook for resource data management
  const {
    resources,
    loading,
    error,
    totalResources,
    totalPages,
    currentPage,
    pageSize,
    searchQuery,
    selectedType,
    createResource,
    updateResource,
    deleteResource,
    setSearchQuery,
    setCurrentPage,
    setPageSize,
    setSelectedType,
    refresh,
    clearError,
  } = useResources();

  // Dialog states
  const [dialogs, setDialogs] = useState<DialogState>({
    resourceForm: {
      open: false,
      resource: null,
      loading: false,
    },
    deleteConfirmation: {
      open: false,
      resource: null,
      loading: false,
    },
  });

  // Resource form handlers
  const handleCreateResource = useCallback(() => {
    setDialogs(prev => ({
      ...prev,
      resourceForm: { open: true, resource: null, loading: false },
    }));
  }, []);

  const handleEditResource = useCallback((resource: Resource) => {
    setDialogs(prev => ({
      ...prev,
      resourceForm: { open: true, resource, loading: false },
    }));
  }, []);

  const handleResourceFormSubmit = useCallback(async (data: ResourceFormData) => {
    const isEdit = !!dialogs.resourceForm.resource;
    
    setDialogs(prev => ({
      ...prev,
      resourceForm: { ...prev.resourceForm, loading: true },
    }));

    try {
      if (isEdit) {
        await updateResource(dialogs.resourceForm.resource!.id, data);
      } else {
        await createResource(data);
      }

      // Close dialog on success
      setDialogs(prev => ({
        ...prev,
        resourceForm: { open: false, resource: null, loading: false },
      }));
    } catch (error) {
      // Error handling is done in the hook
      console.error("Error submitting resource form:", error);
    } finally {
      setDialogs(prev => ({
        ...prev,
        resourceForm: { ...prev.resourceForm, loading: false },
      }));
    }
  }, [dialogs.resourceForm.resource, createResource, updateResource]);

  // Delete resource handlers
  const handleDeleteResource = useCallback((resourceId: number) => {
    const resource = resources.find(r => r.id === resourceId);
    if (resource) {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: true, resource, loading: false },
      }));
    }
  }, [resources]);

  const handleDeleteConfirm = useCallback(async () => {
    const { resource } = dialogs.deleteConfirmation;
    if (!resource) return;

    setDialogs(prev => ({
      ...prev,
      deleteConfirmation: { ...prev.deleteConfirmation, loading: true },
    }));

    try {
      await deleteResource(resource.id);

      // Close dialog on success
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: false, resource: null, loading: false },
      }));
    } catch (error) {
      // Error handling is done in the hook
      console.error("Error deleting resource:", error);
    } finally {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { ...prev.deleteConfirmation, loading: false },
      }));
    }
  }, [dialogs.deleteConfirmation.resource, deleteResource]);

  // Dialog close handlers
  const handleResourceFormClose = useCallback((open: boolean) => {
    if (!open) {
      setDialogs(prev => ({
        ...prev,
        resourceForm: { open: false, resource: null, loading: false },
      }));
    }
  }, []);

  const handleDeleteConfirmationClose = useCallback((open: boolean) => {
    if (!open) {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: false, resource: null, loading: false },
      }));
    }
  }, []);

  // Resource type filter options
  const resourceTypeOptions = useMemo(() => {
    const types = Object.keys(RESOURCE_TYPE_CONFIG) as ResourceType[];
    return [
      { value: 'ALL', label: 'All Types' },
      ...types.map(type => ({
        value: type,
        label: getResourceTypeDisplayName(type)
      }))
    ];
  }, []);

  // Memoized values for better performance
  const totalResourcesText = useMemo(() => 
    `${totalResources} total resources`, 
    [totalResources]
  );

  const shouldShowPagination = useMemo(() => 
    !loading && resources.length > 0, 
    [loading, resources.length]
  );

  return (
    <ResourceManagementErrorBoundary onRetry={refresh}>
      <div className="h-full bg-gray-50 mobile-spacing mobile-no-scroll">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900" id="page-title">
              Resources
            </h1>
            <p className="text-gray-600 mt-1 text-sm sm:text-base" id="page-description">
              Manage facility resources and spaces
            </p>
          </div>
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
            {/* Network Status Indicator - only show after mount to avoid hydration issues */}
            {hasMounted && (
              <Badge 
                variant={isOnline ? "secondary" : "destructive"} 
                className="flex items-center gap-1 sm:hidden"
              >
                {isOnline ? <Wifi className="h-3 w-3" /> : <WifiOff className="h-3 w-3" />}
                {isOnline ? "Online" : "Offline"}
              </Badge>
            )}
            
            <Button
              variant="outline"
              onClick={refresh}
              disabled={loading || (hasMounted && !isOnline)}
              className="touch-target-large focus-enhanced"
              aria-label="Refresh resources list"
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} aria-hidden="true" />
              Refresh
            </Button>
            <Button 
              onClick={handleCreateResource} 
              disabled={hasMounted && !isOnline}
              className="touch-target-large focus-enhanced"
              aria-label="Add new resource"
            >
              <Plus className="mr-2 h-4 w-4" aria-hidden="true" />
              Add Resource
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <div>
                <CardTitle className="flex items-center" id="card-title">
                  <Building2 className="mr-2 h-5 w-5" aria-hidden="true" />
                  Resource Management
                </CardTitle>
                {loading ? (
                  <div className="text-sm text-muted-foreground">
                    <Skeleton className="h-4 w-48" />
                  </div>
                ) : (
                  <CardDescription>
                    {totalResourcesText}
                  </CardDescription>
                )}
              </div>
              <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-3 sm:space-y-0 sm:space-x-3">
                {/* Desktop Network Status - only show after mount */}
                {hasMounted && (
                  <Badge 
                    variant={isOnline ? "secondary" : "destructive"} 
                    className="hidden sm:flex items-center gap-1"
                  >
                    {isOnline ? <Wifi className="h-3 w-3" /> : <WifiOff className="h-3 w-3" />}
                    {isOnline ? "Online" : "Offline"}
                  </Badge>
                )}
                
                {/* Resource Type Filter */}
                <div className="flex items-center space-x-2">
                  <Filter className="h-4 w-4 text-muted-foreground" />
                  <Select value={selectedType} onValueChange={setSelectedType}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Filter by type" />
                    </SelectTrigger>
                    <SelectContent>
                      {resourceTypeOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="w-full lg:w-80">
                  <SearchInput
                    onSearch={(!hasMounted || isOnline) ? setSearchQuery : () => {}}
                    placeholder={(!hasMounted || isOnline) ? "Search resources by name..." : "Search unavailable (offline)"}
                  />
                </div>
              </div>
            </div>
          </CardHeader>
          
          <CardContent>
            {error ? (
              <Alert variant="destructive" className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-3">
                    <div>
                      <p className="font-medium">Unable to load resources</p>
                      <p className="text-sm">{error}</p>
                    </div>
                    
                    {/* Enhanced error recovery suggestions */}
                    <div className="text-sm space-y-2">
                      {error.toLowerCase().includes('network') && (
                        <div className="bg-blue-50 p-3 rounded-md">
                          <p className="font-medium text-blue-900">Network Issue Detected:</p>
                          <ul className="text-blue-800 space-y-1 ml-4 mt-1">
                            <li>• Check your internet connection</li>
                            <li>• Try refreshing the page</li>
                            <li>• Contact IT support if the problem persists</li>
                          </ul>
                        </div>
                      )}
                      
                      {error.toLowerCase().includes('server') && (
                        <div className="bg-orange-50 p-3 rounded-md">
                          <p className="font-medium text-orange-900">Server Issue Detected:</p>
                          <ul className="text-orange-800 space-y-1 ml-4 mt-1">
                            <li>• Our team has been automatically notified</li>
                            <li>• Please try again in a few minutes</li>
                            <li>• The issue is likely temporary</li>
                          </ul>
                        </div>
                      )}
                      
                      {error.toLowerCase().includes('timeout') && (
                        <div className="bg-yellow-50 p-3 rounded-md">
                          <p className="font-medium text-yellow-900">Request Timeout:</p>
                          <ul className="text-yellow-800 space-y-1 ml-4 mt-1">
                            <li>• The request took too long to complete</li>
                            <li>• This could be due to high server load</li>
                            <li>• Try again with a smaller page size</li>
                          </ul>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex flex-wrap gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          clearError();
                          refresh();
                        }}
                        className="touch-target focus-enhanced"
                        aria-label="Try loading resources again"
                      >
                        <RefreshCw className="mr-2 h-3 w-3" aria-hidden="true" />
                        Try Again
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          clearError();
                          setSearchQuery("");
                          setSelectedType("ALL");
                          setCurrentPage(1);
                          setPageSize(10); // Reset to smaller page size
                          refresh();
                        }}
                        className="touch-target focus-enhanced"
                        aria-label="Reset search and retry loading resources"
                      >
                        Reset & Retry
                      </Button>
                      {error.toLowerCase().includes('timeout') && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            clearError();
                            setPageSize(5); // Use smaller page size for timeout issues
                            refresh();
                          }}
                          className="touch-target focus-enhanced"
                          aria-label="Retry with smaller page size"
                        >
                          Load Less Data
                        </Button>
                      )}
                      {hasMounted && !isOnline && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.location.reload()}
                          className="touch-target focus-enhanced"
                          aria-label="Refresh entire page"
                        >
                          Refresh Page
                        </Button>
                      )}
                    </div>
                    {hasMounted && !isOnline && (
                      <p className="text-xs text-muted-foreground">
                        You appear to be offline. Please check your internet connection.
                      </p>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            ) : (
              <>
                <ResourcesTable
                  resources={resources}
                  loading={loading}
                  onEdit={handleEditResource}
                  onDelete={handleDeleteResource}
                  onRefresh={refresh}
                  onTypeFilter={setSelectedType}
                  selectedType={selectedType}
                />
                
                {shouldShowPagination && (
                  <div className="mt-4 sm:mt-6">
                    <PaginationControls
                      currentPage={currentPage}
                      totalPages={totalPages}
                      pageSize={pageSize}
                      totalItems={totalResources}
                      onPageChange={setCurrentPage}
                      onPageSizeChange={setPageSize}
                    />
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>

        {/* Dialogs */}
        <ResourceFormDialog
          open={dialogs.resourceForm.open}
          onOpenChange={handleResourceFormClose}
          resource={dialogs.resourceForm.resource}
          onSubmit={handleResourceFormSubmit}
          loading={dialogs.resourceForm.loading}
          availableAmenities={availableAmenities}
        />

        <DeleteConfirmationDialog
          open={dialogs.deleteConfirmation.open}
          onOpenChange={handleDeleteConfirmationClose}
          resource={dialogs.deleteConfirmation.resource}
          onConfirm={handleDeleteConfirm}
          loading={dialogs.deleteConfirmation.loading}
        />
      </div>
    </ResourceManagementErrorBoundary>
  );
}