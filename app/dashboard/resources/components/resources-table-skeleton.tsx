"use client"

import { memo, useEffect, useState } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { Loader2, Building2, Clock } from "lucide-react"

interface ResourcesTableSkeletonProps {
  rows?: number
  showLoadingMessage?: boolean
  loadingText?: string
}

export const ResourcesTableSkeleton = memo(({ 
  rows = 5, 
  showLoadingMessage = true,
  loadingText = "Loading resources..."
}: ResourcesTableSkeletonProps) => {
  const [loadingDots, setLoadingDots] = useState("");
  const [elapsedTime, setElapsedTime] = useState(0);

  // Animated loading dots
  useEffect(() => {
    const interval = setInterval(() => {
      setLoadingDots(prev => {
        if (prev === "...") return "";
        return prev + ".";
      });
    }, 500);

    return () => clearInterval(interval);
  }, []);

  // Track loading time
  useEffect(() => {
    const startTime = Date.now();
    const interval = setInterval(() => {
      setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const getVariableWidth = (index: number, baseWidth: number) => {
    // Create varied skeleton widths for more realistic loading appearance
    const variations = [0.8, 1.0, 0.9, 1.1, 0.85];
    const variation = variations[index % variations.length];
    return Math.floor(baseWidth * variation);
  };

  return (
    <div className="space-y-4">
      {/* Loading Status Header */}
      {showLoadingMessage && (
        <Card className="border-dashed">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                  <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />
                </div>
                <div>
                  <p className="font-medium text-sm">
                    {loadingText}{loadingDots}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Fetching resource data from server
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {elapsedTime}s
                </Badge>
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Building2 className="h-3 w-3" />
                  Loading
                </Badge>
              </div>
            </div>
          </CardHeader>
        </Card>
      )}

      {/* Mobile loading cards */}
      <div className="block md:hidden space-y-4">
        {Array.from({ length: rows }).map((_, index) => (
          <Card key={index} className="animate-pulse">
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1 space-y-3">
                  <div className="flex items-center space-x-3">
                    <Skeleton className="h-8 w-8 rounded-md" />
                    <Skeleton 
                      className="h-5" 
                      style={{ width: `${getVariableWidth(index, 140)}px` }}
                    />
                  </div>
                  <div className="space-y-2 ml-11">
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-3 w-12" />
                      <Skeleton 
                        className="h-3" 
                        style={{ width: `${getVariableWidth(index, 80)}px` }}
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-3 w-16" />
                      <Skeleton 
                        className="h-3" 
                        style={{ width: `${getVariableWidth(index, 100)}px` }}
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-3 w-14" />
                      <Skeleton 
                        className="h-3" 
                        style={{ width: `${getVariableWidth(index, 60)}px` }}
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-3 w-20" />
                      <Skeleton 
                        className="h-3" 
                        style={{ width: `${getVariableWidth(index, 40)}px` }}
                      />
                    </div>
                  </div>
                </div>
                <Skeleton className="h-9 w-9 rounded-md" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Desktop loading table */}
      <div className="hidden md:block rounded-md border">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="min-w-[200px]">
                  <div className="flex items-center space-x-2">
                    <span>Name</span>
                    <Skeleton className="h-4 w-4" />
                  </div>
                </TableHead>
                <TableHead className="min-w-[120px]">
                  <div className="flex items-center space-x-2">
                    <span>Type</span>
                    <Skeleton className="h-4 w-4" />
                  </div>
                </TableHead>
                <TableHead className="min-w-[100px]">
                  <div className="flex items-center space-x-2">
                    <span>Price</span>
                    <Skeleton className="h-4 w-4" />
                  </div>
                </TableHead>
                <TableHead className="min-w-[100px]">
                  <div className="flex items-center space-x-2">
                    <span>Capacity</span>
                    <Skeleton className="h-4 w-4" />
                  </div>
                </TableHead>
                <TableHead className="min-w-[100px]">
                  <div className="flex items-center space-x-2">
                    <span>Amenities</span>
                    <Skeleton className="h-4 w-4" />
                  </div>
                </TableHead>
                <TableHead className="min-w-[120px] hidden lg:table-cell">
                  <div className="flex items-center space-x-2">
                    <span>Created</span>
                    <Skeleton className="h-4 w-4" />
                  </div>
                </TableHead>
                <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: rows }).map((_, index) => (
                <TableRow key={index} className="animate-pulse">
                  <TableCell>
                    <Skeleton 
                      className="h-4" 
                      style={{ width: `${getVariableWidth(index, 180)}px` }}
                    />
                  </TableCell>
                  <TableCell>
                    <Skeleton 
                      className="h-4" 
                      style={{ width: `${getVariableWidth(index, 100)}px` }}
                    />
                  </TableCell>
                  <TableCell>
                    <Skeleton 
                      className="h-4" 
                      style={{ width: `${getVariableWidth(index, 80)}px` }}
                    />
                  </TableCell>
                  <TableCell>
                    <Skeleton 
                      className="h-4" 
                      style={{ width: `${getVariableWidth(index, 60)}px` }}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-4 w-6" />
                      <Skeleton className="h-4 w-12" />
                    </div>
                  </TableCell>
                  <TableCell className="hidden lg:table-cell">
                    <Skeleton 
                      className="h-4" 
                      style={{ width: `${getVariableWidth(index, 100)}px` }}
                    />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-8 w-8 rounded-md" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Loading Progress Indicator */}
      {elapsedTime > 3 && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-3">
            <div className="flex items-center space-x-2 text-sm text-yellow-800">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>
                Taking longer than expected... 
                {elapsedTime > 10 && " Please check your connection."}
              </span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
})

ResourcesTableSkeleton.displayName = "ResourcesTableSkeleton"