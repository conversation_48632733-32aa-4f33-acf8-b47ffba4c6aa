"use client";

import { useState, useMemo, useCallback, useRef, useEffect } from "react";
import { Check, Search, X, ChevronDown, ChevronUp } from "lucide-react";
import { cn } from "@/lib/utils";
import { Amenity } from "@/lib/types";
import { getAmenityIcon } from "@/lib/utils/amenity-icons";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandItem, CommandList } from "@/components/ui/command";

interface AmenitiesSelectorProps {
  amenities: Amenity[];
  selectedAmenityIds: number[];
  onSelectionChange: (selectedIds: number[]) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  "aria-describedby"?: string;
  "aria-invalid"?: boolean;
}

export function AmenitiesSelector({
  amenities,
  selectedAmenityIds,
  onSelectionChange,
  disabled = false,
  placeholder = "Select amenities...",
  className,
  "aria-describedby": ariaDescribedBy,
  "aria-invalid": ariaInvalid,
}: AmenitiesSelectorProps) {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const triggerRef = useRef<HTMLButtonElement>(null);

  // Filter amenities based on search query
  const filteredAmenities = useMemo(() => {
    if (!searchQuery.trim()) return amenities;
    
    const query = searchQuery.toLowerCase().trim();
    return amenities.filter(amenity =>
      amenity.name.toLowerCase().includes(query)
    );
  }, [amenities, searchQuery]);

  // Get selected amenities for display
  const selectedAmenities = useMemo(() => {
    return amenities.filter(amenity => selectedAmenityIds.includes(amenity.id));
  }, [amenities, selectedAmenityIds]);

  // Handle amenity selection/deselection
  const handleAmenityToggle = useCallback((amenityId: number) => {
    const isSelected = selectedAmenityIds.includes(amenityId);
    
    if (isSelected) {
      // Remove from selection
      onSelectionChange(selectedAmenityIds.filter(id => id !== amenityId));
    } else {
      // Add to selection
      onSelectionChange([...selectedAmenityIds, amenityId]);
    }
  }, [selectedAmenityIds, onSelectionChange]);

  // Handle clear all selections
  const handleClearAll = useCallback(() => {
    onSelectionChange([]);
  }, [onSelectionChange]);

  // Handle select all filtered amenities
  const handleSelectAll = useCallback(() => {
    const allFilteredIds = filteredAmenities.map(amenity => amenity.id);
    const newSelection = [...new Set([...selectedAmenityIds, ...allFilteredIds])];
    onSelectionChange(newSelection);
  }, [filteredAmenities, selectedAmenityIds, onSelectionChange]);

  // Clear search when popover closes
  useEffect(() => {
    if (!open) {
      setSearchQuery("");
    }
  }, [open]);

  // Keyboard navigation handler
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === "Escape") {
      setOpen(false);
      triggerRef.current?.focus();
    }
  }, []);

  const selectedCount = selectedAmenityIds.length;
  const hasSelection = selectedCount > 0;
  const hasFilteredResults = filteredAmenities.length > 0;
  const allFilteredSelected = filteredAmenities.length > 0 && 
    filteredAmenities.every(amenity => selectedAmenityIds.includes(amenity.id));

  return (
    <div className={cn("w-full", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            ref={triggerRef}
            variant="outline"
            role="combobox"
            aria-expanded={open}
            aria-describedby={ariaDescribedBy}
            aria-invalid={ariaInvalid}
            disabled={disabled}
            className={cn(
              "w-full justify-between text-left font-normal touch-target-large focus-enhanced text-base",
              !hasSelection && "text-muted-foreground",
              disabled && "cursor-not-allowed opacity-50"
            )}
          >
            <div className="flex items-center gap-2 flex-1 min-w-0">
              {hasSelection ? (
                <div className="flex items-center gap-2 flex-1 min-w-0">
                  <span className="text-sm font-medium">
                    {selectedCount} amenit{selectedCount === 1 ? 'y' : 'ies'} selected
                  </span>
                  {selectedCount <= 3 && (
                    <div className="flex gap-1 flex-wrap">
                      {selectedAmenities.slice(0, 3).map((amenity) => {
                        const IconComponent = getAmenityIcon(amenity.icon);
                        return (
                          <Badge
                            key={amenity.id}
                            variant="secondary"
                            className="text-xs px-2 py-0.5 flex items-center gap-1"
                          >
                            <IconComponent className="h-3 w-3" />
                            <span className="truncate max-w-20">{amenity.name}</span>
                          </Badge>
                        );
                      })}
                    </div>
                  )}
                </div>
              ) : (
                <span>{placeholder}</span>
              )}
            </div>
            {open ? (
              <ChevronUp className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            ) : (
              <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            )}
          </Button>
        </PopoverTrigger>
        
        <PopoverContent 
          className="mobile-dropdown w-[var(--radix-popover-trigger-width)] p-0" 
          align="start"
          onKeyDown={handleKeyDown}
        >
          <Command shouldFilter={false}>
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <Input
                placeholder="Search amenities..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0 h-11"
              />
            </div>
            
            <CommandList className="max-h-[300px]">
              {/* Action buttons */}
              {amenities.length > 0 && (
                <div className="flex items-center justify-between p-2 border-b bg-muted/50">
                  <div className="text-xs text-muted-foreground">
                    {hasFilteredResults ? (
                      <>Showing {filteredAmenities.length} of {amenities.length} amenities</>
                    ) : (
                      <>No amenities found</>
                    )}
                  </div>
                  <div className="flex gap-1">
                    {hasFilteredResults && !allFilteredSelected && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleSelectAll}
                        className="h-6 px-2 text-xs"
                      >
                        Select All
                      </Button>
                    )}
                    {hasSelection && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleClearAll}
                        className="h-6 px-2 text-xs text-destructive hover:text-destructive"
                      >
                        Clear All
                      </Button>
                    )}
                  </div>
                </div>
              )}

              {hasFilteredResults ? (
                <CommandGroup>
                  {filteredAmenities.map((amenity) => {
                    const isSelected = selectedAmenityIds.includes(amenity.id);
                    const IconComponent = getAmenityIcon(amenity.icon);
                    
                    return (
                      <CommandItem
                        key={amenity.id}
                        value={amenity.name}
                        onSelect={() => handleAmenityToggle(amenity.id)}
                        className="flex items-center space-x-3 px-3 py-2 cursor-pointer"
                      >
                        <Checkbox
                          checked={isSelected}
                          onChange={() => handleAmenityToggle(amenity.id)}
                          className="shrink-0"
                          aria-label={`${isSelected ? 'Deselect' : 'Select'} ${amenity.name}`}
                        />
                        <div className="flex items-center space-x-2 flex-1 min-w-0">
                          <IconComponent className="h-4 w-4 shrink-0 text-muted-foreground" />
                          <span className="truncate">{amenity.name}</span>
                        </div>
                        {isSelected && (
                          <Check className="h-4 w-4 text-primary shrink-0" />
                        )}
                      </CommandItem>
                    );
                  })}
                </CommandGroup>
              ) : (
                <CommandEmpty className="py-6 text-center text-sm">
                  {searchQuery.trim() ? (
                    <div className="space-y-2">
                      <p>No amenities found matching "{searchQuery}"</p>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSearchQuery("")}
                        className="text-xs"
                      >
                        Clear search
                      </Button>
                    </div>
                  ) : (
                    "No amenities available"
                  )}
                </CommandEmpty>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Selected amenities display */}
      {hasSelection && (
        <div className="mt-3 space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-muted-foreground">
              Selected Amenities ({selectedCount})
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearAll}
              className="h-6 px-2 text-xs text-destructive hover:text-destructive"
              disabled={disabled}
            >
              <X className="h-3 w-3 mr-1" />
              Clear All
            </Button>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {selectedAmenities.map((amenity) => {
              const IconComponent = getAmenityIcon(amenity.icon);
              return (
                <Badge
                  key={amenity.id}
                  variant="secondary"
                  className="flex items-center gap-2 px-3 py-1.5 text-sm"
                >
                  <IconComponent className="h-3 w-3" />
                  <span>{amenity.name}</span>
                  <button
                    type="button"
                    onClick={() => handleAmenityToggle(amenity.id)}
                    disabled={disabled}
                    className="ml-1 hover:bg-muted rounded-full p-0.5 focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                    aria-label={`Remove ${amenity.name}`}
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}