"use client";

import { memo, useMemo } from "react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { 
  ResourceType, 
  RESOURCE_TYPE_CONFIG,
  getResourceTypeDisplayName,
  getResourceTypeDescription 
} from "@/lib/types";
import { cn } from "@/lib/utils";
import { 
  Building2, 
  TreePine, 
  GraduationCap, 
  Users, 
  Laptop, 
  DoorClosed 
} from "lucide-react";

interface ResourceTypeSelectorProps {
  value: ResourceType;
  onValueChange: (value: ResourceType) => void;
  disabled?: boolean;
  "aria-describedby"?: string;
  "aria-invalid"?: boolean;
}

// Resource type icons mapping
const RESOURCE_TYPE_ICONS: Record<ResourceType, React.ComponentType<{ className?: string }>> = {
  INDOOR_EVENT_HALL: Building2,
  OUTDOOR_EVENT_HALL: TreePine,
  TRAINING_ROOM: GraduationCap,
  MEETING_ROOM: Users,
  DESK: Laptop,
  PRIVATE_OFFICE: DoorClosed,
};

// Resource type colors for visual distinction
const RESOURCE_TYPE_COLORS: Record<ResourceType, string> = {
  INDOOR_EVENT_HALL: "bg-blue-50 border-blue-200 text-blue-900",
  OUTDOOR_EVENT_HALL: "bg-green-50 border-green-200 text-green-900",
  TRAINING_ROOM: "bg-purple-50 border-purple-200 text-purple-900",
  MEETING_ROOM: "bg-orange-50 border-orange-200 text-orange-900",
  DESK: "bg-gray-50 border-gray-200 text-gray-900",
  PRIVATE_OFFICE: "bg-indigo-50 border-indigo-200 text-indigo-900",
};

export const ResourceTypeSelector = memo(({
  value,
  onValueChange,
  disabled = false,
  "aria-describedby": ariaDescribedBy,
  "aria-invalid": ariaInvalid,
}: ResourceTypeSelectorProps) => {
  
  // Get all resource types in a consistent order
  const resourceTypes = useMemo(() => {
    return Object.keys(RESOURCE_TYPE_CONFIG) as ResourceType[];
  }, []);

  const handleValueChange = (newValue: string) => {
    onValueChange(newValue as ResourceType);
  };

  return (
    <div 
      className="space-y-4"
      aria-describedby={ariaDescribedBy}
      aria-invalid={ariaInvalid}
    >
      <RadioGroup
        value={value}
        onValueChange={handleValueChange}
        disabled={disabled}
        className="mobile-radio-group grid gap-3"
        aria-label="Select resource type"
      >
        {resourceTypes.map((type) => {
          const config = RESOURCE_TYPE_CONFIG[type];
          const IconComponent = RESOURCE_TYPE_ICONS[type];
          const isSelected = value === type;
          const colorClasses = RESOURCE_TYPE_COLORS[type];
          
          return (
            <div key={type} className="relative">
              <Label
                htmlFor={`resource-type-${type}`}
                className={cn(
                  "flex items-start space-x-4 p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 touch-target-large focus-enhanced mobile-radio-group",
                  isSelected 
                    ? `${colorClasses} border-current shadow-sm` 
                    : "border-muted hover:border-muted-foreground/50 hover:bg-muted/50",
                  disabled && "opacity-50 cursor-not-allowed"
                )}
              >
                <RadioGroupItem
                  id={`resource-type-${type}`}
                  value={type}
                  className="mt-1"
                  disabled={disabled}
                  aria-describedby={`resource-type-${type}-description`}
                />
                
                <div className="flex-1 space-y-2">
                  <div className="flex items-center space-x-3">
                    <div className={cn(
                      "flex items-center justify-center w-8 h-8 rounded-md",
                      isSelected ? "bg-current/10" : "bg-muted"
                    )}>
                      <IconComponent className={cn(
                        "h-5 w-5",
                        isSelected ? "text-current" : "text-muted-foreground"
                      )} />
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-sm">
                        {getResourceTypeDisplayName(type)}
                      </span>
                      
                      {config.required.length > 0 && (
                        <Badge 
                          variant={isSelected ? "secondary" : "outline"} 
                          className="text-xs"
                        >
                          {config.required.length} required field{config.required.length !== 1 ? 's' : ''}
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  <p 
                    id={`resource-type-${type}-description`}
                    className={cn(
                      "text-sm leading-relaxed",
                      isSelected ? "text-current/80" : "text-muted-foreground"
                    )}
                  >
                    {getResourceTypeDescription(type)}
                  </p>
                  
                  {/* Show required fields for this resource type */}
                  {config.required.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      <span className={cn(
                        "text-xs font-medium",
                        isSelected ? "text-current/70" : "text-muted-foreground"
                      )}>
                        Required:
                      </span>
                      {config.required.map((field, index) => (
                        <Badge 
                          key={field}
                          variant="outline" 
                          className={cn(
                            "text-xs",
                            isSelected && "border-current/30 text-current/80"
                          )}
                        >
                          {field === 'numberOfAttendees' ? 'Attendees' :
                           field === 'numberOfDesks' ? 'Desks' :
                           field === 'numberOfChairs' ? 'Chairs' :
                           field === 'seatingStyle' ? 'Seating Style' :
                           field}
                        </Badge>
                      ))}
                    </div>
                  )}
                  
                  {/* Show special features for indoor event halls */}
                  {type === 'INDOOR_EVENT_HALL' && (
                    <div className="flex items-center space-x-2 mt-2">
                      <Badge 
                        variant={isSelected ? "secondary" : "outline"}
                        className="text-xs"
                      >
                        Stage Styles Available
                      </Badge>
                    </div>
                  )}
                </div>
              </Label>
            </div>
          );
        })}
      </RadioGroup>
      
      {/* Additional information about the selected type */}
      {value && (
        <div className="mt-4 p-3 rounded-md bg-muted/50 border">
          <div className="flex items-start space-x-2">
            <div className="flex items-center justify-center w-6 h-6 rounded-md bg-primary/10 mt-0.5">
              {(() => {
                const IconComponent = RESOURCE_TYPE_ICONS[value];
                return <IconComponent className="h-4 w-4 text-primary" />;
              })()}
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-foreground">
                Selected: {getResourceTypeDisplayName(value)}
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                {RESOURCE_TYPE_CONFIG[value].fields.length > 0 
                  ? `This resource type has ${RESOURCE_TYPE_CONFIG[value].fields.length} configurable field${RESOURCE_TYPE_CONFIG[value].fields.length !== 1 ? 's' : ''}.`
                  : 'This resource type has no additional configuration fields.'
                }
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});

ResourceTypeSelector.displayName = "ResourceTypeSelector";