"use client"

import { useState, memo, useMemo, use<PERSON>allback } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  MoreHorizontal,
  Edit,
  Trash2,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Building2,
  Filter,
} from "lucide-react"
import { 
  Resource, 
  ResourceType, 
  RESOURCE_TYPE_CONFIG,
  formatResourceCapacity,
  formatResourcePrice,
  getResourceTypeDisplayName
} from "@/lib/types"
import { format } from "date-fns"
import { ResourcesTableSkeleton } from "./resources-table-skeleton"

interface ResourcesTableProps {
  resources: Resource[]
  loading: boolean
  onEdit: (resource: Resource) => void
  onDelete: (resourceId: number) => void
  onRefresh: () => void
  onTypeFilter: (type: ResourceType | 'ALL') => void
  selectedType: ResourceType | 'ALL'
}

type SortField = 'name' | 'type' | 'basePrice' | 'capacity' | 'createdAt'
type SortDirection = 'asc' | 'desc'

interface SortConfig {
  field: SortField
  direction: SortDirection
}

// Mobile card component for individual resources - memoized for performance
const ResourceCard = memo(({ 
  resource, 
  onEdit, 
  onDelete 
}: { 
  resource: Resource
  onEdit: (resource: Resource) => void
  onDelete: (resourceId: number) => void
}) => {
  return (
    <Card className="mb-4">
      <CardContent className="mobile-spacing">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-3">
              <div 
                className="flex items-center justify-center w-10 h-10 rounded-md bg-muted"
                aria-hidden="true"
              >
                <Building2 className="h-5 w-5" />
              </div>
              <div>
                <h3 className="font-medium text-base" id={`resource-${resource.id}`}>
                  {resource.name}
                </h3>
                <Badge variant="secondary" className="mt-1">
                  {getResourceTypeDisplayName(resource.type)}
                </Badge>
              </div>
            </div>
            
            <div className="space-y-1 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <span className="font-medium">Price:</span>
                <span className="font-semibold text-foreground">
                  {formatResourcePrice(resource.basePrice)}
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                <span className="font-medium">Capacity:</span>
                <span>{formatResourceCapacity(resource)}</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <span className="font-medium">Amenities:</span>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="outline" className="cursor-help">
                        {resource.amenities.length}
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="max-w-xs">
                        {resource.amenities.length > 0 ? (
                          <div className="space-y-1">
                            <p className="font-medium">Amenities:</p>
                            <ul className="text-xs space-y-0.5">
                              {resource.amenities.map((amenity) => (
                                <li key={amenity.id}>• {amenity.name}</li>
                              ))}
                            </ul>
                          </div>
                        ) : (
                          <p className="text-xs">No amenities assigned</p>
                        )}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              
              <div className="flex items-center space-x-2">
                <span className="font-medium">Created:</span>
                <span>
                  {resource.createdAt 
                    ? (() => {
                        try {
                          return format(new Date(resource.createdAt), 'MMM dd, yyyy');
                        } catch (error) {
                          return 'Invalid date';
                        }
                      })()
                    : 'N/A'
                  }
                </span>
              </div>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                className="h-11 w-11 p-0 touch-target focus-enhanced"
                aria-label={`Actions for ${resource.name}`}
              >
                <span className="sr-only">Open actions menu for {resource.name}</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem 
                onClick={() => onEdit(resource)}
                className="cursor-pointer touch-target focus-enhanced"
                role="menuitem"
              >
                <Edit className="mr-2 h-4 w-4" aria-hidden="true" />
                Edit {resource.name}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDelete(resource.id)}
                className="text-destructive cursor-pointer touch-target focus-enhanced"
                role="menuitem"
              >
                <Trash2 className="mr-2 h-4 w-4" aria-hidden="true" />
                Delete {resource.name}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  )
})

export const ResourcesTable = memo(({
  resources,
  loading,
  onEdit,
  onDelete,
  onRefresh,
  onTypeFilter,
  selectedType,
}: ResourcesTableProps) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    field: 'createdAt',
    direction: 'desc',
  })

  const handleSort = useCallback((field: SortField) => {
    setSortConfig(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }))
  }, [])

  const getSortIcon = useCallback((field: SortField) => {
    if (sortConfig.field !== field) {
      return <ArrowUpDown className="ml-2 h-4 w-4" />
    }
    return sortConfig.direction === 'asc' ? (
      <ArrowUp className="ml-2 h-4 w-4" />
    ) : (
      <ArrowDown className="ml-2 h-4 w-4" />
    )
  }, [sortConfig.field, sortConfig.direction])

  const sortedResources = useMemo(() => {
    return [...resources].sort((a, b) => {
      const { field, direction } = sortConfig
      let aValue: any
      let bValue: any

      // Handle special sorting cases
      if (field === 'createdAt') {
        try {
          aValue = a.createdAt ? new Date(a.createdAt).getTime() : 0
          bValue = b.createdAt ? new Date(b.createdAt).getTime() : 0
        } catch (error) {
          aValue = 0
          bValue = 0
        }
      } else if (field === 'type') {
        aValue = getResourceTypeDisplayName(a.type)
        bValue = getResourceTypeDisplayName(b.type)
      } else if (field === 'capacity') {
        // Sort by capacity based on resource type
        aValue = a.numberOfAttendees || a.numberOfDesks || 0
        bValue = b.numberOfAttendees || b.numberOfDesks || 0
      } else if (field === 'basePrice') {
        aValue = a.basePrice
        bValue = b.basePrice
      } else if (field === 'name') {
        aValue = a.name
        bValue = b.name
      } else {
        // Fallback for any other fields
        aValue = (a as any)[field]
        bValue = (b as any)[field]
      }

      // Handle string sorting
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (aValue < bValue) {
        return direction === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return direction === 'asc' ? 1 : -1
      }
      return 0
    })
  }, [resources, sortConfig])

  const resourceTypeOptions = useMemo(() => {
    const types = Object.keys(RESOURCE_TYPE_CONFIG) as ResourceType[]
    return [
      { value: 'ALL', label: 'All Types' },
      ...types.map(type => ({
        value: type,
        label: getResourceTypeDisplayName(type)
      }))
    ]
  }, [])

  if (loading) {
    return <ResourcesTableSkeleton />
  }

  if (resources.length === 0) {
    return (
      <>
        {/* Mobile empty state */}
        <div className="block md:hidden">
          <Card>
            <CardContent className="p-8 text-center">
              <div className="flex flex-col items-center justify-center space-y-4">
                <Building2 className="h-12 w-12 text-muted-foreground" />
                <div className="space-y-2">
                  <p className="text-lg font-medium">No resources found</p>
                  <p className="text-sm text-muted-foreground">
                    Try adjusting your search or add a new resource
                  </p>
                </div>
                <Button 
                  variant="outline" 
                  onClick={onRefresh} 
                  className="touch-target focus-enhanced px-6"
                  aria-label="Refresh resources list"
                >
                  Refresh
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Desktop empty state */}
        <div className="hidden md:block rounded-md border">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="min-w-[150px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('name')}
                      className="h-auto p-0 font-semibold"
                    >
                      Name
                      {getSortIcon('name')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[120px]">
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        onClick={() => handleSort('type')}
                        className="h-auto p-0 font-semibold"
                      >
                        Type
                        {getSortIcon('type')}
                      </Button>
                      <Select value={selectedType} onValueChange={onTypeFilter}>
                        <SelectTrigger className="w-8 h-8 p-0 border-0 bg-transparent hover:bg-muted">
                          <Filter className="h-4 w-4" />
                        </SelectTrigger>
                        <SelectContent>
                          {resourceTypeOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </TableHead>
                  <TableHead className="min-w-[100px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('basePrice')}
                      className="h-auto p-0 font-semibold"
                    >
                      Price
                      {getSortIcon('basePrice')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[100px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('capacity')}
                      className="h-auto p-0 font-semibold"
                    >
                      Capacity
                      {getSortIcon('capacity')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[100px]">Amenities</TableHead>
                  <TableHead className="min-w-[120px] hidden lg:table-cell">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('createdAt')}
                      className="h-auto p-0 font-semibold"
                    >
                      Created
                      {getSortIcon('createdAt')}
                    </Button>
                  </TableHead>
                  <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell colSpan={7} className="h-24 text-center">
                    <div className="flex flex-col items-center justify-center space-y-2">
                      <p className="text-muted-foreground">No resources found</p>
                      <Button 
                        variant="outline" 
                        onClick={onRefresh} 
                        className="touch-target focus-enhanced px-6"
                        aria-label="Refresh resources list"
                      >
                        Refresh
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>
      </>
    )
  }

  return (
    <>
      {/* Mobile card view */}
      <div className="block md:hidden">
        {/* Mobile sort and filter controls */}
        <div className="flex items-center justify-between mb-4 p-4 bg-muted/50 rounded-lg mobile-spacing">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium" id="sort-label">Sort by:</span>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="touch-target focus-enhanced"
                    aria-labelledby="sort-label"
                    aria-expanded="false"
                  >
                    {sortConfig.field === 'name' && 'Name'}
                    {sortConfig.field === 'type' && 'Type'}
                    {sortConfig.field === 'basePrice' && 'Price'}
                    {sortConfig.field === 'capacity' && 'Capacity'}
                    {sortConfig.field === 'createdAt' && 'Created'}
                    {sortConfig.direction === 'asc' ? (
                      <ArrowUp className="ml-2 h-4 w-4" aria-hidden="true" />
                    ) : (
                      <ArrowDown className="ml-2 h-4 w-4" aria-hidden="true" />
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-40" role="menu">
                  <DropdownMenuItem 
                    onClick={() => handleSort('name')}
                    className="touch-target focus-enhanced"
                    role="menuitem"
                  >
                    Name {getSortIcon('name')}
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => handleSort('type')}
                    className="touch-target focus-enhanced"
                    role="menuitem"
                  >
                    Type {getSortIcon('type')}
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => handleSort('basePrice')}
                    className="touch-target focus-enhanced"
                    role="menuitem"
                  >
                    Price {getSortIcon('basePrice')}
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => handleSort('capacity')}
                    className="touch-target focus-enhanced"
                    role="menuitem"
                  >
                    Capacity {getSortIcon('capacity')}
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => handleSort('createdAt')}
                    className="touch-target focus-enhanced"
                    role="menuitem"
                  >
                    Created {getSortIcon('createdAt')}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">Filter:</span>
              <Select value={selectedType} onValueChange={onTypeFilter}>
                <SelectTrigger className="w-32 h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {resourceTypeOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          {sortedResources.map((resource) => (
            <ResourceCard
              key={resource.id}
              resource={resource}
              onEdit={onEdit}
              onDelete={onDelete}
            />
          ))}
        </div>
      </div>

      {/* Desktop table view */}
      <div className="hidden md:block rounded-md border">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="min-w-[150px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('name')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by name ${sortConfig.field === 'name' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Name
                    {getSortIcon('name')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[120px]">
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('type')}
                      className="h-auto p-2 font-semibold focus-enhanced"
                      aria-label={`Sort by type ${sortConfig.field === 'type' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                    >
                      Type
                      {getSortIcon('type')}
                    </Button>
                    <Select value={selectedType} onValueChange={onTypeFilter}>
                      <SelectTrigger className="w-8 h-8 p-0 border-0 bg-transparent hover:bg-muted">
                        <Filter className="h-4 w-4" />
                      </SelectTrigger>
                      <SelectContent>
                        {resourceTypeOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </TableHead>
                <TableHead className="min-w-[100px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('basePrice')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by price ${sortConfig.field === 'basePrice' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Price
                    {getSortIcon('basePrice')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[100px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('capacity')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by capacity ${sortConfig.field === 'capacity' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Capacity
                    {getSortIcon('capacity')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[100px]">Amenities</TableHead>
                <TableHead className="min-w-[120px] hidden lg:table-cell">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('createdAt')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by creation date ${sortConfig.field === 'createdAt' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Created
                    {getSortIcon('createdAt')}
                  </Button>
                </TableHead>
                <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedResources.map((resource) => {
                return (
                  <TableRow key={resource.id}>
                    <TableCell className="font-medium">
                      {resource.name}
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {getResourceTypeDisplayName(resource.type)}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-semibold">
                      {formatResourcePrice(resource.basePrice)}
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {formatResourceCapacity(resource)}
                    </TableCell>
                    <TableCell>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Badge variant="outline" className="cursor-help">
                              {resource.amenities.length}
                            </Badge>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="max-w-xs">
                              {resource.amenities.length > 0 ? (
                                <div className="space-y-1">
                                  <p className="font-medium">Amenities:</p>
                                  <ul className="text-xs space-y-0.5">
                                    {resource.amenities.map((amenity) => (
                                      <li key={amenity.id}>• {amenity.name}</li>
                                    ))}
                                  </ul>
                                </div>
                              ) : (
                                <p className="text-xs">No amenities assigned</p>
                              )}
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell className="text-muted-foreground hidden lg:table-cell">
                      {resource.createdAt 
                        ? (() => {
                            try {
                              return format(new Date(resource.createdAt), 'MMM dd, yyyy');
                            } catch (error) {
                              return 'Invalid date';
                            }
                          })()
                        : 'N/A'
                      }
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button 
                            variant="ghost" 
                            className="h-10 w-10 p-0 focus-enhanced"
                            aria-label={`Actions for ${resource.name}`}
                          >
                            <span className="sr-only">Open actions menu for {resource.name}</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48" role="menu">
                          <DropdownMenuItem 
                            onClick={() => onEdit(resource)}
                            className="cursor-pointer focus-enhanced py-3"
                            role="menuitem"
                          >
                            <Edit className="mr-2 h-4 w-4" aria-hidden="true" />
                            Edit {resource.name}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => onDelete(resource.id)}
                            className="text-destructive cursor-pointer focus-enhanced py-3"
                            role="menuitem"
                          >
                            <Trash2 className="mr-2 h-4 w-4" aria-hidden="true" />
                            Delete {resource.name}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </div>
      </div>
    </>
  )
})

ResourceCard.displayName = "ResourceCard"
ResourcesTable.displayName = "ResourcesTable"