"use client"

import { memo, useCallback } from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Loader2, AlertTriangle, Building2 } from "lucide-react"
import { Resource, getResourceTypeDisplayName, formatResourceCapacity, formatResourcePrice, SEATING_STYLE_LABELS, STAGE_STYLE_LABELS } from "@/lib/types"

interface DeleteConfirmationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  resource: Resource | null
  onConfirm: () => Promise<void>
  loading?: boolean
}

export const DeleteConfirmationDialog = memo(({
  open,
  onOpenChange,
  resource,
  onConfirm,
  loading = false,
}: DeleteConfirmationDialogProps) => {
  if (!resource) return null

  const handleConfirm = useCallback(async () => {
    try {
      await onConfirm()
      onOpenChange(false)
    } catch (error) {
      // Error handling is done in the parent component
      console.error('Delete confirmation error:', error)
    }
  }, [onConfirm, onOpenChange])

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent 
        className="mobile-dialog mobile-spacing"
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <AlertDialogHeader>
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-destructive" aria-hidden="true" />
            <AlertDialogTitle id="delete-dialog-title">Delete Resource</AlertDialogTitle>
          </div>
          <AlertDialogDescription asChild>
            <div className="space-y-4" id="delete-dialog-description">
              <p>
                Are you sure you want to delete this resource? This action cannot be undone.
              </p>
              
              <div className="rounded-lg border p-4 bg-muted/50">
                <h4 className="font-medium mb-2">Resource Details:</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Name:</span>
                    <span className="font-medium">{resource.name}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Type:</span>
                    <div className="flex items-center space-x-2">
                      <Building2 className="h-4 w-4" />
                      <span className="font-medium">
                        {getResourceTypeDisplayName(resource.type)}
                      </span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Base Price:</span>
                    <span className="font-medium">
                      {formatResourcePrice(resource.basePrice)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Capacity:</span>
                    <span className="font-medium">
                      {formatResourceCapacity(resource)}
                    </span>
                  </div>
                  {resource.seatingStyle && (
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">Seating Style:</span>
                      <span className="font-medium">
                        {SEATING_STYLE_LABELS[resource.seatingStyle]}
                      </span>
                    </div>
                  )}
                  {resource.stageStyles && resource.stageStyles.length > 0 && (
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">Stage Styles:</span>
                      <span className="font-medium">
                        {resource.stageStyles.map(ss => STAGE_STYLE_LABELS[ss.style]).join(', ')}
                      </span>
                    </div>
                  )}
                  {resource.amenities && resource.amenities.length > 0 && (
                    <div className="flex justify-between items-start">
                      <span className="text-muted-foreground">Amenities:</span>
                      <span className="font-medium text-right">
                        {resource.amenities.length} amenity{resource.amenities.length !== 1 ? 'ies' : ''}
                      </span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Created:</span>
                    <span className="font-medium">
                      {resource.createdAt 
                        ? (() => {
                            try {
                              return new Date(resource.createdAt).toLocaleDateString();
                            } catch (error) {
                              return 'Invalid date';
                            }
                          })()
                        : 'N/A'
                      }
                    </span>
                  </div>
                </div>
              </div>

              <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-destructive">
                      Warning: This action is permanent
                    </p>
                    <ul className="text-xs text-destructive/80 space-y-1">
                      <li>• The resource will be permanently removed from the system</li>
                      <li>• If this resource has existing bookings, deletion will be prevented</li>
                      <li>• All associated amenities and stage styles will be disconnected</li>
                      <li>• This action cannot be reversed</li>
                    </ul>
                  </div>
                </div>
              </div>

              <p className="text-sm text-muted-foreground">
                Please confirm that you want to delete the resource <strong>{resource.name}</strong>, or click Cancel to abort.
              </p>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter className="mobile-button-group flex flex-col sm:flex-row gap-3">
          <AlertDialogCancel 
            disabled={loading}
            className="touch-target-large focus-enhanced flex-1 sm:flex-none"
          >
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={loading}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90 touch-target-large focus-enhanced flex-1 sm:flex-none"
            aria-describedby="delete-dialog-description"
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />}
            Delete {resource.name}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
})

DeleteConfirmationDialog.displayName = "DeleteConfirmationDialog"