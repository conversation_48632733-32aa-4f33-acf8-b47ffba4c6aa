"use client";

import { memo, useMemo } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { StageStyle, STAGE_STYLE_LABELS } from "@/lib/types";
import { cn } from "@/lib/utils";
import { Presentation, Users2 } from "lucide-react";

interface StageStylesSelectorProps {
  selectedStyles: StageStyle[];
  onSelectionChange: (styles: StageStyle[]) => void;
  disabled?: boolean;
  "aria-describedby"?: string;
  "aria-invalid"?: boolean;
}

// Stage style icons mapping
const STAGE_STYLE_ICONS: Record<StageStyle, React.ComponentType<{ className?: string }>> = {
  PODIUM: Presentation,
  PANEL: Users2,
};

// Stage style descriptions
const STAGE_STYLE_DESCRIPTIONS: Record<StageStyle, string> = {
  PODIUM: "Single speaker setup with elevated platform for presentations and speeches",
  PANEL: "Multi-speaker setup with table arrangement for discussions and panels",
};

// Stage style colors for visual distinction
const STAGE_STYLE_COLORS: Record<StageStyle, string> = {
  PODIUM: "bg-blue-50 border-blue-200 text-blue-900",
  PANEL: "bg-purple-50 border-purple-200 text-purple-900",
};

export const StageStylesSelector = memo(({
  selectedStyles,
  onSelectionChange,
  disabled = false,
  "aria-describedby": ariaDescribedBy,
  "aria-invalid": ariaInvalid,
}: StageStylesSelectorProps) => {
  
  // Get all stage styles in a consistent order
  const stageStyles = useMemo(() => {
    return Object.keys(STAGE_STYLE_LABELS) as StageStyle[];
  }, []);

  // Handle stage style selection/deselection
  const handleStyleToggle = (style: StageStyle) => {
    const isSelected = selectedStyles.includes(style);
    
    if (isSelected) {
      // Remove from selection
      onSelectionChange(selectedStyles.filter(s => s !== style));
    } else {
      // Add to selection
      onSelectionChange([...selectedStyles, style]);
    }
  };

  const selectedCount = selectedStyles.length;
  const hasSelection = selectedCount > 0;

  return (
    <div 
      className="space-y-4"
      aria-describedby={ariaDescribedBy}
      aria-invalid={ariaInvalid}
    >
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium">
            Stage Styles
          </Label>
          {hasSelection && (
            <Badge variant="secondary" className="text-xs">
              {selectedCount} selected
            </Badge>
          )}
        </div>
        
        <p className="text-sm text-muted-foreground">
          Select the stage configurations available for this indoor event hall. Multiple styles can be selected.
        </p>
      </div>

      <div className="mobile-checkbox-group grid gap-3" role="group" aria-label="Stage style options">
        {stageStyles.map((style) => {
          const isSelected = selectedStyles.includes(style);
          const IconComponent = STAGE_STYLE_ICONS[style];
          const colorClasses = STAGE_STYLE_COLORS[style];
          
          return (
            <div key={style} className="relative">
              <Label
                htmlFor={`stage-style-${style}`}
                className={cn(
                  "flex items-start space-x-4 p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 touch-target-large focus-enhanced mobile-checkbox-group",
                  isSelected 
                    ? `${colorClasses} border-current shadow-sm` 
                    : "border-muted hover:border-muted-foreground/50 hover:bg-muted/50",
                  disabled && "opacity-50 cursor-not-allowed"
                )}
              >
                <Checkbox
                  id={`stage-style-${style}`}
                  checked={isSelected}
                  onCheckedChange={() => handleStyleToggle(style)}
                  disabled={disabled}
                  className="mt-1"
                  aria-describedby={`stage-style-${style}-description`}
                />
                
                <div className="flex-1 space-y-2">
                  <div className="flex items-center space-x-3">
                    <div className={cn(
                      "flex items-center justify-center w-8 h-8 rounded-md",
                      isSelected ? "bg-current/10" : "bg-muted"
                    )}>
                      <IconComponent className={cn(
                        "h-5 w-5",
                        isSelected ? "text-current" : "text-muted-foreground"
                      )} />
                    </div>
                    
                    <span className="font-medium text-sm">
                      {STAGE_STYLE_LABELS[style]}
                    </span>
                  </div>
                  
                  <p 
                    id={`stage-style-${style}-description`}
                    className={cn(
                      "text-sm leading-relaxed",
                      isSelected ? "text-current/80" : "text-muted-foreground"
                    )}
                  >
                    {STAGE_STYLE_DESCRIPTIONS[style]}
                  </p>
                  
                  {/* Visual representation of stage configuration */}
                  <div className="mt-3">
                    <div className={cn(
                      "relative w-full h-16 rounded-md border-2 border-dashed transition-colors",
                      isSelected ? "border-current/30 bg-current/5" : "border-muted-foreground/30 bg-muted/30"
                    )}>
                      {style === 'PODIUM' ? (
                        // Podium visual representation
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="flex flex-col items-center space-y-1">
                            <div className={cn(
                              "w-8 h-3 rounded-sm",
                              isSelected ? "bg-current/40" : "bg-muted-foreground/40"
                            )} />
                            <div className={cn(
                              "w-12 h-2 rounded-sm",
                              isSelected ? "bg-current/60" : "bg-muted-foreground/60"
                            )} />
                            <div className="flex space-x-1 mt-2">
                              {[...Array(5)].map((_, i) => (
                                <div
                                  key={i}
                                  className={cn(
                                    "w-1.5 h-1.5 rounded-full",
                                    isSelected ? "bg-current/30" : "bg-muted-foreground/30"
                                  )}
                                />
                              ))}
                            </div>
                          </div>
                        </div>
                      ) : (
                        // Panel visual representation
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="flex flex-col items-center space-y-1">
                            <div className="flex space-x-1">
                              {[...Array(3)].map((_, i) => (
                                <div
                                  key={i}
                                  className={cn(
                                    "w-2 h-2 rounded-full",
                                    isSelected ? "bg-current/60" : "bg-muted-foreground/60"
                                  )}
                                />
                              ))}
                            </div>
                            <div className={cn(
                              "w-16 h-2 rounded-sm",
                              isSelected ? "bg-current/40" : "bg-muted-foreground/40"
                            )} />
                            <div className="flex space-x-1 mt-1">
                              {[...Array(6)].map((_, i) => (
                                <div
                                  key={i}
                                  className={cn(
                                    "w-1 h-1 rounded-full",
                                    isSelected ? "bg-current/30" : "bg-muted-foreground/30"
                                  )}
                                />
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                    
                    <p className={cn(
                      "text-xs text-center mt-1",
                      isSelected ? "text-current/60" : "text-muted-foreground/60"
                    )}>
                      {style === 'PODIUM' ? 'Single speaker + audience' : 'Panel speakers + audience'}
                    </p>
                  </div>
                </div>
              </Label>
            </div>
          );
        })}
      </div>

      {/* Selection summary */}
      {hasSelection && (
        <div className="mt-4 p-3 rounded-md bg-muted/50 border">
          <div className="flex items-start space-x-2">
            <div className="flex items-center justify-center w-6 h-6 rounded-md bg-primary/10 mt-0.5">
              <Presentation className="h-4 w-4 text-primary" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-foreground">
                Selected Stage Styles ({selectedCount})
              </p>
              <div className="flex flex-wrap gap-1 mt-1">
                {selectedStyles.map((style) => (
                  <Badge key={style} variant="secondary" className="text-xs">
                    {STAGE_STYLE_LABELS[style]}
                  </Badge>
                ))}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                This indoor event hall can be configured with the selected stage arrangements.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Validation message when no styles are selected */}
      {!hasSelection && (
        <div className="mt-2 p-3 rounded-md bg-amber-50 border border-amber-200">
          <p className="text-sm text-amber-800">
            Please select at least one stage style for this indoor event hall.
          </p>
        </div>
      )}
    </div>
  );
});

StageStylesSelector.displayName = "StageStylesSelector";