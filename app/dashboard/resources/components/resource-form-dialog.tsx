"use client";

import { useEffect, useState, memo, useCallback, useMemo } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle, CheckCircle2, DollarSign } from "lucide-react";
import { 
  Resource, 
  ResourceFormData, 
  Amenity,
  ResourceType,
  SeatingStyle,
  StageStyle,
  RESOURCE_TYPE_CONFIG,
  SEATING_STYLE_LABELS,
  getResourceTypeDisplayName
} from "@/lib/types";
import { resourceCreateSchema, resourceUpdateSchema } from "@/lib/validations/resource";
import { 
  mapServerErrorsToForm, 
  getApiErrorMessage, 
  createErrorSummary,
  hasFormErrors 
} from "@/lib/utils/form-errors";
import { ResourceTypeSelector } from "./resource-type-selector";
import { AmenitiesSelector } from "./amenities-selector";
import { StageStylesSelector } from "./stage-styles-selector";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";

interface ResourceFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  resource?: Resource | null; // null for create, Resource object for edit
  onSubmit: (data: ResourceFormData) => Promise<void>;
  loading?: boolean;
  availableAmenities: Amenity[];
}

export const ResourceFormDialog = memo(({
  open,
  onOpenChange,
  resource,
  onSubmit,
  loading = false,
  availableAmenities,
}: ResourceFormDialogProps) => {
  const isEdit = !!resource;
  const schema = isEdit ? resourceUpdateSchema : resourceCreateSchema;
  const [serverError, setServerError] = useState<string>("");

  const form = useForm<ResourceFormData>({
    resolver: zodResolver(schema),
    mode: "onChange", // Enable real-time validation
    defaultValues: {
      name: "",
      type: "INDOOR_EVENT_HALL", // Default resource type
      basePrice: 0,
      details: "",
      seatingStyle: undefined,
      numberOfAttendees: undefined,
      numberOfDesks: undefined,
      numberOfChairs: undefined,
      amenityIds: [],
      stageStyles: [],
    },
  });

  // Watch the resource type to show/hide dynamic fields
  const watchedType = form.watch("type");
  const typeConfig = RESOURCE_TYPE_CONFIG[watchedType];

  // Reset form when dialog opens/closes or resource changes
  useEffect(() => {
    if (open) {
      setServerError(""); // Clear server errors when opening
      if (isEdit && resource) {
        form.reset({
          name: resource.name,
          type: resource.type,
          basePrice: resource.basePrice,
          details: resource.details || "",
          seatingStyle: resource.seatingStyle || undefined,
          numberOfAttendees: resource.numberOfAttendees || undefined,
          numberOfDesks: resource.numberOfDesks || undefined,
          numberOfChairs: resource.numberOfChairs || undefined,
          amenityIds: resource.amenities.map(a => a.id),
          stageStyles: resource.stageStyles.map(s => s.style),
        });
      } else {
        form.reset({
          name: "",
          type: "INDOOR_EVENT_HALL", // Default resource type
          basePrice: 0,
          details: "",
          seatingStyle: undefined,
          numberOfAttendees: undefined,
          numberOfDesks: undefined,
          numberOfChairs: undefined,
          amenityIds: [],
          stageStyles: [],
        });
      }
    }
  }, [open, isEdit, resource, form]);

  // Clear type-specific fields when resource type changes
  useEffect(() => {
    if (!typeConfig) return;

    const currentValues = form.getValues();
    const updates: Partial<ResourceFormData> = {};

    // Clear fields that are not applicable to the new resource type
    if (!typeConfig.fields.includes('seatingStyle')) {
      updates.seatingStyle = undefined;
    }
    if (!typeConfig.fields.includes('numberOfAttendees')) {
      updates.numberOfAttendees = undefined;
    }
    if (!typeConfig.fields.includes('numberOfDesks')) {
      updates.numberOfDesks = undefined;
    }
    if (!typeConfig.fields.includes('numberOfChairs')) {
      updates.numberOfChairs = undefined;
    }
    if (watchedType !== 'INDOOR_EVENT_HALL') {
      updates.stageStyles = [];
    }

    // Only update if there are changes to avoid infinite loops
    const hasChanges = Object.entries(updates).some(([key, value]) => 
      currentValues[key as keyof ResourceFormData] !== value
    );

    if (hasChanges) {
      Object.entries(updates).forEach(([key, value]) => {
        form.setValue(key as keyof ResourceFormData, value as any, { shouldValidate: true });
      });
    }
  }, [watchedType, typeConfig, form]);

  const handleSubmit = useCallback(async (data: ResourceFormData) => {
    setServerError(""); // Clear previous server errors
    
    try {
      await onSubmit(data);
      onOpenChange(false);
    } catch (error) {
      console.error("Form submission error:", error);
      
      // Handle server validation errors
      if (error && typeof error === 'object' && 'details' in error) {
        mapServerErrorsToForm(error as any, form.setError as any);
      }
      
      // Set general server error message
      const errorMessage = getApiErrorMessage(error);
      setServerError(errorMessage);
    }
  }, [onSubmit, onOpenChange, form]);

  const handleCancel = useCallback(() => {
    form.reset();
    setServerError("");
    onOpenChange(false);
  }, [form, onOpenChange]);

  // Get error summary for display - memoized to prevent unnecessary recalculations
  const errorSummary = useMemo(() => 
    createErrorSummary(form.formState.errors, serverError), 
    [form.formState.errors, serverError]
  );
  
  const canSubmit = useMemo(() => 
    form.formState.isValid && !loading, 
    [form.formState.isValid, loading]
  );

  // Format price input with currency
  const formatPrice = useCallback((value: number) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(value);
  }, []);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent 
        className="mobile-dialog sm:max-w-[700px] max-h-[90vh] overflow-y-auto mobile-spacing"
        aria-labelledby="resource-form-title"
        aria-describedby="resource-form-description"
      >
        <DialogHeader>
          <DialogTitle id="resource-form-title">
            {isEdit ? "Edit Resource" : "Create New Resource"}
          </DialogTitle>
          <DialogDescription id="resource-form-description">
            {isEdit
              ? "Update the resource information below. Required fields are marked with an asterisk."
              : "Fill in the details below to create a new resource. Required fields are marked with an asterisk."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="mobile-form-layout space-y-6"
          >
            {/* Error Summary */}
            {errorSummary.hasErrors && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-1">
                    <p className="font-medium">{errorSummary.summary}</p>
                    {errorSummary.messages.length > 1 && (
                      <ul className="list-disc list-inside text-sm space-y-1">
                        {errorSummary.messages.map((message, index) => (
                          <li key={index}>{message}</li>
                        ))}
                      </ul>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* Resource Name */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel htmlFor="resource-name">
                    Resource Name <span className="text-destructive" aria-label="required">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      id="resource-name"
                      placeholder="Enter resource name"
                      {...field}
                      disabled={loading}
                      className="touch-target focus-enhanced text-base"
                      aria-describedby="resource-name-description resource-name-error"
                      aria-invalid={!!form.formState.errors.name}
                    />
                  </FormControl>
                  <FormDescription id="resource-name-description">
                    Name must be unique and contain only letters, numbers, spaces, and basic punctuation.
                  </FormDescription>
                  <FormMessage id="resource-name-error" />
                </FormItem>
              )}
            />

            {/* Resource Type */}
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Resource Type <span className="text-destructive" aria-label="required">*</span>
                  </FormLabel>
                  <FormControl>
                    <ResourceTypeSelector
                      value={field.value}
                      onValueChange={field.onChange}
                      disabled={loading}
                      aria-describedby="resource-type-description resource-type-error"
                      aria-invalid={!!form.formState.errors.type}
                    />
                  </FormControl>
                  <FormDescription id="resource-type-description">
                    Select the type of resource. This determines which additional fields are required.
                  </FormDescription>
                  <FormMessage id="resource-type-error" />
                </FormItem>
              )}
            />

            {/* Base Price */}
            <FormField
              control={form.control}
              name="basePrice"
              render={({ field }) => (
                <FormItem>
                  <FormLabel htmlFor="resource-price">
                    Base Price (IQD) <span className="text-destructive" aria-label="required">*</span>
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="resource-price"
                        type="number"
                        placeholder="0.00"
                        min="0"
                        step="0.01"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        disabled={loading}
                        className="pl-10 touch-target focus-enhanced text-base"
                        aria-describedby="resource-price-description resource-price-error"
                        aria-invalid={!!form.formState.errors.basePrice}
                      />
                    </div>
                  </FormControl>
                  <FormDescription id="resource-price-description">
                    Base price in Iraqi Dinars (IQD). Enter the standard rate for this resource.
                  </FormDescription>
                  <FormMessage id="resource-price-error" />
                </FormItem>
              )}
            />

            {/* Details */}
            <FormField
              control={form.control}
              name="details"
              render={({ field }) => (
                <FormItem>
                  <FormLabel htmlFor="resource-details">
                    Details
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      id="resource-details"
                      placeholder="Enter additional details about this resource..."
                      {...field}
                      disabled={loading}
                      className="touch-target focus-enhanced min-h-[80px] text-base"
                      aria-describedby="resource-details-description resource-details-error"
                      aria-invalid={!!form.formState.errors.details}
                    />
                  </FormControl>
                  <FormDescription id="resource-details-description">
                    Optional additional information about the resource (max 500 characters).
                  </FormDescription>
                  <FormMessage id="resource-details-error" />
                </FormItem>
              )}
            />

            {/* Dynamic Fields Based on Resource Type */}
            
            {/* Seating Style */}
            {typeConfig.fields.includes('seatingStyle') && (
              <FormField
                control={form.control}
                name="seatingStyle"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Seating Style {typeConfig.required.includes('seatingStyle') && (
                        <span className="text-destructive" aria-label="required">*</span>
                      )}
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        value={field.value || ""}
                        onValueChange={(value) => field.onChange(value as SeatingStyle)}
                        disabled={loading}
                        className="flex flex-col space-y-3"
                        aria-describedby="seating-style-description seating-style-error"
                        aria-invalid={!!form.formState.errors.seatingStyle}
                      >
                        {Object.entries(SEATING_STYLE_LABELS).map(([value, label]) => (
                          <div key={value} className="flex items-center space-x-2">
                            <RadioGroupItem
                              value={value}
                              id={`seating-${value}`}
                              className="touch-target"
                            />
                            <Label 
                              htmlFor={`seating-${value}`}
                              className="text-sm font-medium cursor-pointer"
                            >
                              {label}
                            </Label>
                          </div>
                        ))}
                      </RadioGroup>
                    </FormControl>
                    <FormDescription id="seating-style-description">
                      Select the seating arrangement style for this {getResourceTypeDisplayName(watchedType).toLowerCase()}.
                    </FormDescription>
                    <FormMessage id="seating-style-error" />
                  </FormItem>
                )}
              />
            )}

            {/* Number of Attendees */}
            {typeConfig.fields.includes('numberOfAttendees') && (
              <FormField
                control={form.control}
                name="numberOfAttendees"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel htmlFor="resource-attendees">
                      Number of Attendees {typeConfig.required.includes('numberOfAttendees') && (
                        <span className="text-destructive" aria-label="required">*</span>
                      )}
                    </FormLabel>
                    <FormControl>
                      <Input
                        id="resource-attendees"
                        type="number"
                        placeholder="0"
                        min="1"
                        max="10000"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || undefined)}
                        value={field.value || ""}
                        disabled={loading}
                        className="touch-target focus-enhanced text-base"
                        aria-describedby="resource-attendees-description resource-attendees-error"
                        aria-invalid={!!form.formState.errors.numberOfAttendees}
                      />
                    </FormControl>
                    <FormDescription id="resource-attendees-description">
                      Maximum number of people that can be accommodated in this {getResourceTypeDisplayName(watchedType).toLowerCase()}.
                    </FormDescription>
                    <FormMessage id="resource-attendees-error" />
                  </FormItem>
                )}
              />
            )}

            {/* Number of Desks */}
            {typeConfig.fields.includes('numberOfDesks') && (
              <FormField
                control={form.control}
                name="numberOfDesks"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel htmlFor="resource-desks">
                      Number of Desks {typeConfig.required.includes('numberOfDesks') && (
                        <span className="text-destructive" aria-label="required">*</span>
                      )}
                    </FormLabel>
                    <FormControl>
                      <Input
                        id="resource-desks"
                        type="number"
                        placeholder="0"
                        min="1"
                        max="100"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || undefined)}
                        value={field.value || ""}
                        disabled={loading}
                        className="touch-target focus-enhanced text-base"
                        aria-describedby="resource-desks-description resource-desks-error"
                        aria-invalid={!!form.formState.errors.numberOfDesks}
                      />
                    </FormControl>
                    <FormDescription id="resource-desks-description">
                      Number of desks available in this private office.
                    </FormDescription>
                    <FormMessage id="resource-desks-error" />
                  </FormItem>
                )}
              />
            )}

            {/* Number of Chairs */}
            {typeConfig.fields.includes('numberOfChairs') && (
              <FormField
                control={form.control}
                name="numberOfChairs"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel htmlFor="resource-chairs">
                      Number of Chairs {typeConfig.required.includes('numberOfChairs') && (
                        <span className="text-destructive" aria-label="required">*</span>
                      )}
                    </FormLabel>
                    <FormControl>
                      <Input
                        id="resource-chairs"
                        type="number"
                        placeholder="0"
                        min="1"
                        max="500"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || undefined)}
                        value={field.value || ""}
                        disabled={loading}
                        className="touch-target focus-enhanced text-base"
                        aria-describedby="resource-chairs-description resource-chairs-error"
                        aria-invalid={!!form.formState.errors.numberOfChairs}
                      />
                    </FormControl>
                    <FormDescription id="resource-chairs-description">
                      Number of chairs available in this private office. Should be at least equal to the number of desks.
                    </FormDescription>
                    <FormMessage id="resource-chairs-error" />
                  </FormItem>
                )}
              />
            )}

            {/* Stage Styles (only for Indoor Event Halls) */}
            {watchedType === 'INDOOR_EVENT_HALL' && (
              <FormField
                control={form.control}
                name="stageStyles"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Stage Styles <span className="text-destructive" aria-label="required">*</span>
                    </FormLabel>
                    <FormControl>
                      <StageStylesSelector
                        selectedStyles={field.value}
                        onSelectionChange={field.onChange}
                        disabled={loading}
                        aria-describedby="stage-styles-description stage-styles-error"
                        aria-invalid={!!form.formState.errors.stageStyles}
                      />
                    </FormControl>
                    <FormDescription id="stage-styles-description">
                      Select the stage configurations available for this indoor event hall.
                    </FormDescription>
                    <FormMessage id="stage-styles-error" />
                  </FormItem>
                )}
              />
            )}

            {/* Amenities */}
            <FormField
              control={form.control}
              name="amenityIds"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Amenities
                  </FormLabel>
                  <FormControl>
                    <AmenitiesSelector
                      amenities={availableAmenities}
                      selectedAmenityIds={field.value}
                      onSelectionChange={field.onChange}
                      disabled={loading}
                      placeholder="Select amenities for this resource..."
                      aria-describedby="amenities-description amenities-error"
                      aria-invalid={!!form.formState.errors.amenityIds}
                    />
                  </FormControl>
                  <FormDescription id="amenities-description">
                    Select the amenities that are available with this resource. This helps users understand what facilities are included.
                  </FormDescription>
                  <FormMessage id="amenities-error" />
                </FormItem>
              )}
            />

            <DialogFooter className="mobile-button-group flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
              {/* Form validation status */}
              <div className="flex items-center justify-center sm:justify-start space-x-2 text-sm text-muted-foreground mb-2 sm:mb-0">
                {form.formState.isValid && !serverError ? (
                  <div className="flex items-center text-green-600">
                    <CheckCircle2 className="h-4 w-4 mr-1" />
                    Form is valid
                  </div>
                ) : hasFormErrors(form.formState.errors) || serverError ? (
                  <div className="flex items-center text-red-600">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {errorSummary.errorCount} error{errorSummary.errorCount !== 1 ? 's' : ''}
                  </div>
                ) : null}
              </div>
              
              <div className="mobile-button-group flex space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={loading}
                  className="touch-target-large focus-enhanced flex-1 sm:flex-none"
                >
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={!canSubmit}
                  className="touch-target-large focus-enhanced min-w-[140px] flex-1 sm:flex-none"
                  aria-describedby={errorSummary.hasErrors ? "form-errors" : undefined}
                >
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />}
                  {isEdit ? "Update Resource" : "Create Resource"}
                </Button>
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
});

ResourceFormDialog.displayName = "ResourceFormDialog";