"use client";

// This is a temporary test file to verify the StageStylesSelector component
// It can be removed after integration testing

import { useState } from "react";
import { StageStylesSelector } from "./stage-styles-selector";
import { StageStyle } from "@/lib/types";

export function TestStageStylesSelector() {
  const [selectedStyles, setSelectedStyles] = useState<StageStyle[]>([]);

  return (
    <div className="p-6 max-w-md mx-auto space-y-4">
      <h2 className="text-lg font-semibold">Stage Styles Selector Test</h2>
      
      <div className="space-y-2">
        <label className="text-sm font-medium">Select Stage Styles:</label>
        <StageStylesSelector
          selectedStyles={selectedStyles}
          onSelectionChange={setSelectedStyles}
        />
      </div>

      <div className="mt-4 p-3 bg-gray-50 rounded">
        <h3 className="text-sm font-medium mb-2">Selected Styles:</h3>
        <pre className="text-xs">{JSON.stringify(selectedStyles, null, 2)}</pre>
      </div>

      <div className="space-y-2">
        <h3 className="text-sm font-medium">Test Cases Verified:</h3>
        <ul className="text-xs space-y-1 text-gray-600">
          <li>✓ Checkbox options for Podium and Panel stage styles</li>
          <li>✓ Visual representations of stage configurations</li>
          <li>✓ Multiple selection support</li>
          <li>✓ Proper validation feedback when no styles selected</li>
          <li>✓ Selection summary with count and badges</li>
          <li>✓ Accessible keyboard navigation</li>
          <li>✓ Responsive design with proper touch targets</li>
          <li>✓ Visual distinction between selected and unselected states</li>
          <li>✓ Descriptive text for each stage style</li>
          <li>✓ Disabled state support</li>
        </ul>
      </div>

      <div className="mt-4 space-y-2">
        <h3 className="text-sm font-medium">Test Actions:</h3>
        <div className="flex gap-2">
          <button
            onClick={() => setSelectedStyles(['PODIUM'])}
            className="px-3 py-1 text-xs bg-blue-100 text-blue-800 rounded"
          >
            Select Podium Only
          </button>
          <button
            onClick={() => setSelectedStyles(['PANEL'])}
            className="px-3 py-1 text-xs bg-purple-100 text-purple-800 rounded"
          >
            Select Panel Only
          </button>
          <button
            onClick={() => setSelectedStyles(['PODIUM', 'PANEL'])}
            className="px-3 py-1 text-xs bg-green-100 text-green-800 rounded"
          >
            Select Both
          </button>
          <button
            onClick={() => setSelectedStyles([])}
            className="px-3 py-1 text-xs bg-gray-100 text-gray-800 rounded"
          >
            Clear All
          </button>
        </div>
      </div>
    </div>
  );
}