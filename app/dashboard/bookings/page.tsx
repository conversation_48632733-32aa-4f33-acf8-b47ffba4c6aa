"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useBookings } from '@/hooks/use-bookings';
import { BookingCalendar } from '@/components/booking-calendar';
import { CalendarEvent } from '@/lib/types';
import { useToast } from '@/hooks/use-toast';
import { BookingManagementErrorBoundary } from './components/booking-management-error-boundary';

export default function BookingsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const {
    calendarEvents,
    loading,
    error,
    fetchCalendarEvents,
    refreshCalendar,
  } = useBookings();

  // Fetch calendar events on component mount
  useEffect(() => {
    fetchCalendarEvents();
  }, [fetchCalendarEvents]);

  const handleEventClick = (event: CalendarEvent) => {
    // Navigate to booking edit page
    router.push(`/dashboard/bookings/${event.extendedProps.bookingId}`);
  };

  const handleDateSelect = (date: Date) => {
    // Could be used for quick booking creation on a specific date
    console.log('Date selected:', date);
  };

  const handleAddBooking = () => {
    // Navigate to booking creation page
    router.push('/dashboard/bookings/create');
  };

  // Show error state if there's an error
  if (error) {
    return (
      <div className="h-full bg-gray-50 p-6">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Bookings</h1>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="text-center py-8">
            <div className="text-red-600 mb-4">
              <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to Load Calendar</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={refreshCalendar}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <BookingManagementErrorBoundary onRetry={refreshCalendar}>
      <div className="h-full bg-gray-50 p-6">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Bookings</h1>
            <p className="text-gray-600 mt-1">Manage your bookings and schedule</p>
          </div>
        </div>
        
        <BookingCalendar
          events={calendarEvents}
          onEventClick={handleEventClick}
          onDateSelect={handleDateSelect}
          onAddBooking={handleAddBooking}
          loading={loading}
          className="w-full"
        />
      </div>
    </BookingManagementErrorBoundary>
  );
}