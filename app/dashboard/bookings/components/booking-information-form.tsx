"use client";

import { useEffect, useState, memo, useCallback, useMemo } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Loader2, 
  AlertCircle, 
  CheckCircle2, 
  X, 
  Plus, 
  Minus, 
  Users, 
  Clock, 
  MapPin,
  AlertTriangle 
} from "lucide-react";
import { Booking, BookingFormData, Customer, Resource, Catering, BookingStatus } from "@/lib/types";
import { bookingCreateSchema, bookingUpdateSchema } from "@/lib/validations/booking";
import { 
  mapServerErrorsToForm, 
  getApiErrorMessage, 
  createErrorSummary,
  hasFormErrors 
} from "@/lib/utils/form-errors";
import { useCustomers } from "@/hooks/use-customers";
import { useResources } from "@/hooks/use-resources";
import { useCatering } from "@/hooks/use-catering";

interface BookingInformationFormProps {
  booking?: Booking | null;
  onSubmit: (data: BookingFormData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  submitButtonText?: string;
}

interface CateringSelection {
  cateringId: number;
  quantity: number;
  catering?: Catering;
}

export const BookingInformationForm = memo(({
  booking,
  onSubmit,
  onCancel,
  loading = false,
  submitButtonText = "Save Booking",
}: BookingInformationFormProps) => {
  const isEdit = !!booking;
  const schema = isEdit ? bookingUpdateSchema : bookingCreateSchema;
  const [serverError, setServerError] = useState<string>("");
  const [selectedResources, setSelectedResources] = useState<number[]>([]);
  const [cateringSelections, setCateringSelections] = useState<CateringSelection[]>([]);
  const [statusChanged, setStatusChanged] = useState(false);

  // Fetch data for dropdowns
  const { customers, fetchCustomers, loading: customersLoading } = useCustomers();
  const { resources, fetchResources, loading: resourcesLoading } = useResources();
  const { catering, fetchCatering, loading: cateringLoading } = useCatering();

  const form = useForm<BookingFormData>({
    resolver: zodResolver(schema),
    mode: "onChange",
    defaultValues: {
      customerId: 0,
      resourceIds: [],
      status: "PENDING" as BookingStatus,
      start: new Date(),
      end: new Date(),
      caterings: [],
    },
  });

  // Fetch data on component mount
  useEffect(() => {
    fetchCustomers();
    fetchResources();
    fetchCatering();
  }, [fetchCustomers, fetchResources, fetchCatering]);

  // Initialize form with booking data
  useEffect(() => {
    if (isEdit && booking) {
      const startDate = new Date(booking.start);
      const endDate = new Date(booking.end);
      
      form.reset({
        customerId: booking.customerId,
        resourceIds: booking.resources.map(r => r.id),
        status: booking.status,
        start: startDate,
        end: endDate,
        caterings: booking.caterings.map(c => ({
          cateringId: c.catering?.id || c.cateringId,
          quantity: c.quantity,
        })),
      });
      
      setSelectedResources(booking.resources.map(r => r.id));
      
      // Initialize catering selections - only populate catering object if catering data is available
      if (booking.caterings.length > 0) {
        const initialSelections = booking.caterings.map(c => {
          // Handle both nested catering object and direct cateringId
          const cateringId = c.catering?.id || c.cateringId;
          
          return {
            cateringId: cateringId,
            quantity: c.quantity,
            catering: catering.length > 0 ? catering.find(cat => cat.id === cateringId) : (c.catering || undefined),
          };
        });
        
        setCateringSelections(initialSelections);
      } else {
        setCateringSelections([]);
      }
    } else if (!isEdit) {
      const now = new Date();
      const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);
      
      form.reset({
        customerId: 0,
        resourceIds: [],
        status: "PENDING",
        start: now,
        end: oneHourLater,
        caterings: [],
      });
      
      setSelectedResources([]);
      setCateringSelections([]);
    }
    }, [isEdit, booking, form, catering]);

  // Handle case where booking has catering but catering data loads later
  useEffect(() => {
    if (isEdit && booking && booking.caterings.length > 0 && catering.length > 0) {
      // Update catering selections with proper catering objects when catering data loads
      setCateringSelections(prevSelections => {
        const updatedSelections = booking.caterings.map(c => {
          const cateringId = c.catering?.id || c.cateringId;
          const cateringObj = catering.find(cat => cat.id === cateringId);
          
          return {
            cateringId: cateringId,
            quantity: c.quantity,
            catering: cateringObj,
          };
        });
        
        // Only update if we have more complete data
        if (updatedSelections.some(s => s.catering && !prevSelections.find(ps => ps.cateringId === s.cateringId && ps.catering))) {
          return updatedSelections;
        }
        
        return prevSelections;
      });
    }
  }, [isEdit, booking, catering]);

  // Sync catering selections with form field
  useEffect(() => {
    form.setValue("caterings", cateringSelections.map(cs => ({
      cateringId: cs.cateringId,
      quantity: cs.quantity,
    })), { shouldValidate: true, shouldDirty: false });
  }, [cateringSelections, form]);

  // Clear catering selections when booking status is not CONFIRMED
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      // Only clear catering if the user actively changed the status (not during initial load)
      if (name === 'status' && value.status !== 'CONFIRMED' && cateringSelections.length > 0) {
        setCateringSelections([]);
        form.setValue("caterings", [], { shouldValidate: true, shouldDirty: true });
        setStatusChanged(true);
      }
    });

    return () => subscription.unsubscribe();
  }, [form, cateringSelections.length]);

  const handleSubmit = useCallback(async (data: BookingFormData) => {
    setServerError("");
    
    try {
      // Ensure dates are Date objects
      const formattedData: BookingFormData = {
        ...data,
        start: data.start instanceof Date ? data.start : new Date(data.start),
        end: data.end instanceof Date ? data.end : new Date(data.end),
      };
      
      await onSubmit(formattedData);
    } catch (error) {
      console.error("Form submission error:", error);
      
      if (error && typeof error === 'object' && 'details' in error) {
        mapServerErrorsToForm(error as any, form.setError as any);
      }
      
      const errorMessage = getApiErrorMessage(error);
      setServerError(errorMessage);
    }
  }, [onSubmit, form]);

  // Handle resource selection
  const handleResourceToggle = useCallback((resourceId: number, checked: boolean) => {
    const newSelection = checked 
      ? [...selectedResources, resourceId]
      : selectedResources.filter(id => id !== resourceId);
    
    setSelectedResources(newSelection);
    form.setValue("resourceIds", newSelection);
  }, [selectedResources, form]);

  // Handle catering selection
  const addCateringSelection = useCallback(() => {
    if (catering.length > 0) {
      const availableCatering = catering.find(c => 
        !cateringSelections.some(cs => cs.cateringId === c.id)
      );
      
      if (availableCatering) {
        const newSelection: CateringSelection = {
          cateringId: availableCatering.id,
          quantity: 1,
          catering: availableCatering,
        };
        
        const newSelections = [...cateringSelections, newSelection];
        setCateringSelections(newSelections);
        
        form.setValue("caterings", newSelections.map(cs => ({
          cateringId: cs.cateringId,
          quantity: cs.quantity,
        })), { shouldValidate: true, shouldDirty: true });
      }
    }
  }, [catering, cateringSelections, form]);

  const removeCateringSelection = useCallback((index: number) => {
    const newSelections = cateringSelections.filter((_, i) => i !== index);
    setCateringSelections(newSelections);
    
    form.setValue("caterings", newSelections.map(cs => ({
      cateringId: cs.cateringId,
      quantity: cs.quantity,
    })), { shouldValidate: true, shouldDirty: true });
  }, [cateringSelections, form]);

  const updateCateringQuantity = useCallback((index: number, quantity: number) => {
    if (quantity < 1) return;
    
    const newSelections = [...cateringSelections];
    newSelections[index].quantity = quantity;
    setCateringSelections(newSelections);
    
    form.setValue("caterings", newSelections.map(cs => ({
      cateringId: cs.cateringId,
      quantity: cs.quantity,
    })), { shouldValidate: true, shouldDirty: true });
  }, [cateringSelections, form]);

  const updateCateringSelection = useCallback((index: number, cateringId: number) => {
    const selectedCatering = catering.find(c => c.id === cateringId);
    if (!selectedCatering) return;
    
    // Check if this catering is already selected in another index
    const existingIndex = cateringSelections.findIndex((cs, i) => i !== index && cs.cateringId === cateringId);
    
    const newSelections = [...cateringSelections];
    
    // If the catering is already selected elsewhere, swap them
    if (existingIndex !== -1) {
      const currentSelection = newSelections[index];
      newSelections[existingIndex] = {
        ...newSelections[existingIndex],
        cateringId: currentSelection.cateringId,
        catering: currentSelection.catering,
      };
    }
    
    newSelections[index] = {
      ...newSelections[index],
      cateringId,
      catering: selectedCatering,
    };
    
    setCateringSelections(newSelections);
    
    form.setValue("caterings", newSelections.map(cs => ({
      cateringId: cs.cateringId,
      quantity: cs.quantity,
    })), { shouldValidate: true, shouldDirty: true });
  }, [catering, cateringSelections, form]);

  const errorSummary = useMemo(() => 
    createErrorSummary(form.formState.errors, serverError), 
    [form.formState.errors, serverError]
  );
  
  const canSubmit = useMemo(() => 
    form.formState.isValid && !loading && selectedResources.length > 0, 
    [form.formState.isValid, loading, selectedResources.length]
  );

  const availableCateringOptions = useMemo(() => 
    catering.filter(c => !cateringSelections.some(cs => cs.cateringId === c.id)),
    [catering, cateringSelections]
  );

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className="space-y-6"
      >
        {/* Error Summary */}
        {errorSummary.hasErrors && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1">
                <p className="font-medium">{errorSummary.summary}</p>
                {errorSummary.messages.length > 1 && (
                  <ul className="list-disc list-inside text-sm space-y-1">
                    {errorSummary.messages.map((message, index) => (
                      <li key={index}>{message}</li>
                    ))}
                  </ul>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Status Change Warning */}
        {statusChanged && (
          <Alert variant="default" className="border-amber-200 bg-amber-50">
            <AlertTriangle className="h-4 w-4 text-amber-600" />
            <AlertDescription className="text-amber-800">
              <p className="font-medium">Catering options removed</p>
              <p className="text-sm">Catering is only available for confirmed bookings. The selected catering options have been removed due to the status change.</p>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setStatusChanged(false)}
                className="mt-2 h-auto p-0 text-amber-700 hover:text-amber-800"
              >
                Dismiss
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Customer Selection */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900 flex items-center gap-2">
            <Users className="h-4 w-4" />
            Customer Information
          </h4>
          
          <FormField
            control={form.control}
            name="customerId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Customer *</FormLabel>
                <Select
                  onValueChange={(value) => field.onChange(parseInt(value))}
                  value={field.value ? field.value.toString() : ""}
                  disabled={loading || customersLoading}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a customer" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {customers.map((customer) => (
                      <SelectItem key={customer.id} value={customer.id.toString()}>
                        <div className="flex flex-col">
                          <span className="font-medium">{customer.name}</span>
                          <span className="text-sm text-gray-500">{customer.email}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Status</FormLabel>
                <Select onValueChange={field.onChange} value={field.value} disabled={loading}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="PENDING">
                      <Badge variant="outline" className="bg-yellow-100 border-yellow-300 text-yellow-800">
                        Pending
                      </Badge>
                    </SelectItem>
                    <SelectItem value="CONFIRMED">
                      <Badge variant="outline" className="bg-green-100 border-green-300 text-green-800">
                        Confirmed
                      </Badge>
                    </SelectItem>
                    <SelectItem value="CANCELLED">
                      <Badge variant="outline" className="bg-red-100 border-red-300 text-red-800">
                        Cancelled
                      </Badge>
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Date and Time Selection */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900 flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Date and Time
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="start"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Start Date & Time *</FormLabel>
                  <FormControl>
                    <Input
                      type="datetime-local"
                      value={field.value ? format(field.value, "yyyy-MM-dd'T'HH:mm") : ""}
                      onChange={(e) => {
                        if (e.target.value) {
                          const date = new Date(e.target.value);
                          if (!isNaN(date.getTime())) {
                            field.onChange(date);
                          }
                        }
                      }}
                      disabled={loading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="end"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>End Date & Time *</FormLabel>
                  <FormControl>
                    <Input
                      type="datetime-local"
                      value={field.value ? format(field.value, "yyyy-MM-dd'T'HH:mm") : ""}
                      onChange={(e) => {
                        if (e.target.value) {
                          const date = new Date(e.target.value);
                          if (!isNaN(date.getTime())) {
                            field.onChange(date);
                          }
                        }
                      }}
                      disabled={loading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Resource Selection */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900 flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Resources *
          </h4>
          
          {resourcesLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading resources...</span>
            </div>
          ) : (
            <Card>
              <CardContent className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {resources.map((resource) => (
                    <div key={resource.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`resource-${resource.id}`}
                        checked={selectedResources.includes(resource.id)}
                        onCheckedChange={(checked) => 
                          handleResourceToggle(resource.id, checked as boolean)
                        }
                        disabled={loading}
                      />
                      <label
                        htmlFor={`resource-${resource.id}`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                      >
                        <div className="flex flex-col">
                          <span>{resource.name}</span>
                          <span className="text-xs text-gray-500">
                            {resource.type}
                          </span>
                        </div>
                      </label>
                    </div>
                  ))}
                </div>
                
                {selectedResources.length === 0 && (
                  <p className="text-sm text-red-600 mt-2">
                    Please select at least one resource.
                  </p>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Catering Selection */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-900">Catering Options</h4>
            {form.watch('status') === 'CONFIRMED' ? (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addCateringSelection}
                disabled={loading || cateringLoading || availableCateringOptions.length === 0}
                className="gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Catering
              </Button>
            ) : (
              <div className="text-sm text-muted-foreground">
                Only available for confirmed bookings
              </div>
            )}
          </div>
          
          {form.watch('status') !== 'CONFIRMED' ? (
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3 text-muted-foreground">
                  <AlertCircle className="h-5 w-5" />
                  <div>
                    <p className="font-medium">Catering requires confirmed booking</p>
                    <p className="text-sm">Please set the booking status to "Confirmed" to add catering options.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : cateringLoading ? (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="ml-2">Loading catering options...</span>
            </div>
          ) : (
            <div className="space-y-3">
              {cateringSelections.map((selection, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h6 className="text-sm font-medium flex items-center gap-2">
                          Catering Item {index + 1}
                        </h6>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCateringSelection(index)}
                          disabled={loading}
                          className="text-red-600 hover:text-red-700"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="md:col-span-2">
                          <label className="text-sm font-medium text-gray-700">Catering Offer *</label>
                          <Select
                            key={`catering-${index}-${selection.cateringId}-${catering.length}`}
                            value={selection.cateringId?.toString() || ""}
                            onValueChange={(value) => 
                              updateCateringSelection(index, parseInt(value))
                            }
                            disabled={loading}
                          >
                            <SelectTrigger className="mt-1">
                              <SelectValue 
                                placeholder="Select catering option" 
                              />
                            </SelectTrigger>
                            <SelectContent>
                              {catering.map((cateringOption) => (
                                <SelectItem key={cateringOption.id} value={cateringOption.id.toString()}>
                                  <div className="flex flex-col">
                                    <span className="font-medium">{cateringOption.offerName}</span>
                                    <span className="text-sm text-muted-foreground">
                                      IQD {cateringOption.pricePerPerson}/person
                                    </span>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          
                        </div>
                        
                        <div>
                          <label className="text-sm font-medium text-gray-700">Quantity (People) *</label>
                          <div className="mt-1">
                            <Input
                              type="number"
                              min="1"
                              step="1"
                              value={selection.quantity}
                              onChange={(e) => {
                                const newQuantity = parseInt(e.target.value) || 1;
                                if (newQuantity >= 1) {
                                  updateCateringQuantity(index, newQuantity);
                                }
                              }}
                              disabled={loading}
                              className="w-full"
                              placeholder="1"
                            />
                          </div>
                        </div>

                        <div>
                          <label className="text-sm font-medium text-gray-700">Unit Price</label>
                          <div className="mt-1 px-3 py-2 bg-gray-50 border rounded-md text-sm text-gray-600">
                            IQD {selection.catering?.pricePerPerson || 0}/person
                            {selection.catering && " (auto-filled from catering offer)"}
                          </div>
                        </div>
                      </div>
                      
                      {selection.catering && (
                        <div className="pt-2 border-t">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">Line Total:</span>
                            <span className="font-bold text-lg">
                              IQD {(selection.catering.pricePerPerson * selection.quantity).toFixed(2)}
                            </span>
                          </div>
                          {/* Revenue Split Info (similar to invoice) */}
                          <div className="mt-2 text-xs text-muted-foreground">
                            <div className="space-y-1">
                              <div>Revenue Split: First Party IQD {(selection.catering.firstPartyShare * selection.quantity).toFixed(2)}, Vendor IQD {(selection.catering.vendorShare * selection.quantity).toFixed(2)}</div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
              
              {cateringSelections.length === 0 && (
                <p className="text-sm text-gray-500 text-center py-4">
                  No catering selected. Click "Add Catering" to include catering options.
                </p>
              )}

              {/* Total Catering Cost */}
              {cateringSelections.length > 0 && (
                <Card className="bg-muted/50">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-lg font-semibold">Total Catering Cost:</span>
                      </div>
                      <span className="text-2xl font-bold text-primary">
                        IQD {cateringSelections.reduce((total, selection) => 
                          total + (selection.catering?.pricePerPerson || 0) * selection.quantity, 0
                        ).toFixed(2)}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
          {/* Form validation status */}
          <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-2 sm:mb-0">
            {canSubmit ? (
              <div className="flex items-center text-green-600">
                <CheckCircle2 className="h-4 w-4 mr-1" />
                Form is valid
              </div>
            ) : hasFormErrors(form.formState.errors) || serverError || selectedResources.length === 0 ? (
              <div className="flex items-center text-red-600">
                <AlertCircle className="h-4 w-4 mr-1" />
                {selectedResources.length === 0 ? "Select resources" : `${errorSummary.errorCount} error${errorSummary.errorCount !== 1 ? 's' : ''}`}
              </div>
            ) : null}
          </div>
          
          <div className="flex space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={!canSubmit}
              className="min-w-[140px]"
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {submitButtonText}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
});

BookingInformationForm.displayName = "BookingInformationForm";