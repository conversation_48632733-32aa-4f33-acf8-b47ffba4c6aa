"use client";

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface BookingCalendarSkeletonProps {
  className?: string;
}

const WEEKDAYS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

export const BookingCalendarSkeleton: React.FC<BookingCalendarSkeletonProps> = ({
  className
}) => {
  // Generate 42 days (6 weeks) for a typical calendar view
  const calendarDays = Array.from({ length: 42 }, (_, i) => i);

  return (
    <Card className={cn("w-full", className)}>
      <CardContent className="p-6">
        {/* Calendar Header Skeleton */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Skeleton className="h-8 w-40" /> {/* Month/Year title */}
            <Skeleton className="h-8 w-16" /> {/* Today button */}
          </div>
          
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-28" /> {/* Add Booking button */}
            
            <div className="flex items-center gap-1">
              <Skeleton className="h-8 w-8" /> {/* Previous button */}
              <Skeleton className="h-8 w-8" /> {/* Next button */}
            </div>
          </div>
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-7 gap-1">
          {/* Weekday Headers */}
          {WEEKDAYS.map(day => (
            <div
              key={day}
              className="h-10 flex items-center justify-center text-sm font-medium text-gray-500 border-b"
            >
              {day}
            </div>
          ))}

          {/* Calendar Days Skeleton */}
          {calendarDays.map(index => (
            <div
              key={index}
              className="min-h-[120px] p-1 border border-gray-200"
            >
              {/* Day Number Skeleton */}
              <div className="mb-1">
                <Skeleton className="h-4 w-6" />
              </div>

              {/* Event Skeletons - randomize to look more realistic */}
              <div className="space-y-1">
                {index % 3 === 0 && (
                  <>
                    <Skeleton className="h-8 w-full rounded" />
                    {index % 6 === 0 && <Skeleton className="h-8 w-full rounded" />}
                  </>
                )}
                {index % 4 === 1 && (
                  <Skeleton className="h-8 w-full rounded" />
                )}
                {index % 5 === 2 && (
                  <>
                    <Skeleton className="h-8 w-full rounded" />
                    <Skeleton className="h-8 w-full rounded" />
                    <Skeleton className="h-3 w-16" /> {/* "+X more" text */}
                  </>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Loading overlay */}
        <div className="absolute inset-0 bg-white/30 flex items-center justify-center rounded-lg">
          <div className="flex items-center space-x-2 bg-white px-4 py-2 rounded-lg shadow-sm">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="text-sm text-gray-600">Loading calendar events...</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default BookingCalendarSkeleton;