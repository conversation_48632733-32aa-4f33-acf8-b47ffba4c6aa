"use client";

import { useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Calendar, FileText } from "lucide-react";
import { BookingInformationForm } from "../components/booking-information-form";
import { useBookings } from "@/hooks/use-bookings";
import { BookingFormData } from "@/lib/types";
import { toast } from "@/hooks/use-toast";

export default function CreateBookingPage() {
  const router = useRouter();
  const { createBooking } = useBookings();
  const [loading, setLoading] = useState(false);

  const handleSubmit = useCallback(async (data: BookingFormData) => {
    setLoading(true);
    try {
      await createBooking(data);
      toast({
        title: "Success",
        description: "Booking created successfully.",
      });
      router.push("/dashboard/bookings");
    } catch (error) {
      console.error("Error creating booking:", error);
      toast({
        title: "Error",
        description: "Failed to create booking. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [createBooking, router]);

  const handleCancel = useCallback(() => {
    router.push("/dashboard/bookings");
  }, [router]);

  return (
    <div className="h-full bg-gray-50 p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 space-y-4 sm:space-y-0">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
            className="min-h-[44px] sm:min-h-auto"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Bookings
          </Button>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Create Booking</h1>
            <p className="text-gray-600 mt-1 text-sm sm:text-base">Schedule a new booking</p>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="mr-2 h-5 w-5" />
            Booking Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="booking-info" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="booking-info" className="flex items-center">
                <Calendar className="mr-2 h-4 w-4" />
                Booking Information
              </TabsTrigger>
              <TabsTrigger value="invoice" className="flex items-center" disabled>
                <FileText className="mr-2 h-4 w-4" />
                Invoice
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="booking-info" className="mt-6">
              <BookingInformationForm
                onSubmit={handleSubmit}
                onCancel={handleCancel}
                loading={loading}
                submitButtonText="Create Booking"
              />
            </TabsContent>
            
            <TabsContent value="invoice" className="mt-6">
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h4 className="text-lg font-medium text-gray-900 mb-2">Invoice Not Available</h4>
                <p className="text-gray-600">
                  Create the booking first, then you can generate an invoice.
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}