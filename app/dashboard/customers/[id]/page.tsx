"use client";

import { useState, useCallback, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowLeft, Save, AlertCircle, User, FileText } from "lucide-react";
import { CustomerInformationForm } from "../components/customer-information-form";
import { CustomerInvoicesTab } from "../components/customer-invoices-tab";
import { CustomerManagementErrorBoundary } from "../components/customer-management-error-boundary";
import { useCustomers } from "@/hooks/use-customers";
import { Customer, CustomerFormData } from "@/lib/types";
import { toast } from "@/hooks/use-toast";

export default function EditCustomerPage() {
  const router = useRouter();
  const params = useParams();
  const customerId = parseInt(params.id as string);
  const { updateCustomer } = useCustomers();
  
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string>("");
  const [activeTab, setActiveTab] = useState("customer-info");

  // Fetch customer data
  const fetchCustomer = useCallback(async () => {
    try {
      setLoading(true);
      setError("");
      
      const response = await fetch(`/api/customers/${customerId}`);
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Customer not found");
        }
        throw new Error("Failed to fetch customer");
      }
      
      const result = await response.json();
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to fetch customer");
      }
      
      setCustomer(result.data);
    } catch (error) {
      console.error("Error fetching customer:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch customer";
      setError(errorMessage);
      
      if (errorMessage === "Customer not found") {
        toast({
          title: "Customer Not Found",
          description: "The customer you're looking for doesn't exist or may have been deleted.",
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  }, [customerId]);

  useEffect(() => {
    if (customerId && !isNaN(customerId)) {
      fetchCustomer();
    } else {
      setError("Invalid customer ID");
      setLoading(false);
    }
  }, [customerId, fetchCustomer]);

  const handleSubmit = useCallback(async (data: CustomerFormData) => {
    if (!customer) return;
    
    setUpdating(true);
    try {
      const updatedCustomer = await updateCustomer(customer.id, data);
      setCustomer(updatedCustomer);
      toast({
        title: "Success",
        description: "Customer updated successfully.",
      });
    } catch (error) {
      console.error("Error updating customer:", error);
      toast({
        title: "Error",
        description: "Failed to update customer. Please try again.",
        variant: "destructive",
      });
    } finally {
      setUpdating(false);
    }
  }, [customer, updateCustomer]);

  const handleCancel = useCallback(() => {
    router.push("/dashboard/customers");
  }, [router]);

  const handleRetry = useCallback(() => {
    fetchCustomer();
  }, [fetchCustomer]);

  if (loading) {
    return (
      <CustomerManagementErrorBoundary>
        <div className="h-full bg-gray-50 p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-4">
              <Skeleton className="h-10 w-32" />
              <div>
                <Skeleton className="h-8 w-48 mb-2" />
                <Skeleton className="h-4 w-64" />
              </div>
            </div>
          </div>

          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-40" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Skeleton className="h-10 w-full" />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-20 w-full" />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-20 w-full" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </CustomerManagementErrorBoundary>
    );
  }

  if (error) {
    return (
      <CustomerManagementErrorBoundary onRetry={handleRetry}>
        <div className="h-full bg-gray-50 p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={handleCancel}
                className="min-h-[44px] sm:min-h-auto"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Customers
              </Button>
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Edit Customer</h1>
                <p className="text-gray-600 mt-1 text-sm sm:text-base">Customer not found</p>
              </div>
            </div>
          </div>

          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error}
              <Button
                variant="outline"
                size="sm"
                onClick={handleRetry}
                className="ml-2"
              >
                Try Again
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      </CustomerManagementErrorBoundary>
    );
  }

  if (!customer) {
    return null;
  }

  return (
    <CustomerManagementErrorBoundary onRetry={handleRetry}>
      <div className="h-full bg-gray-50 p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={updating}
              className="min-h-[44px] sm:min-h-auto"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Customers
            </Button>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Edit Customer</h1>
              <p className="text-gray-600 mt-1 text-sm sm:text-base">
                Update information for {customer.name}
              </p>
            </div>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="mr-2 h-5 w-5" />
              Customer Management
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="customer-info" className="flex items-center">
                  <User className="mr-2 h-4 w-4" />
                  Customer Information
                </TabsTrigger>
                <TabsTrigger value="invoices" className="flex items-center">
                  <FileText className="mr-2 h-4 w-4" />
                  Invoices
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="customer-info" className="mt-6">
                <CustomerInformationForm
                  customer={customer}
                  onSubmit={handleSubmit}
                  onCancel={handleCancel}
                  loading={updating}
                  submitButtonText="Update Customer"
                />
              </TabsContent>
              
              <TabsContent value="invoices" className="mt-6">
                <CustomerInvoicesTab customerId={customer.id} />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </CustomerManagementErrorBoundary>
  );
}