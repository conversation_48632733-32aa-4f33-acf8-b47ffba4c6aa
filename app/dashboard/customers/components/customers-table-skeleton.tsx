"use client"

import { memo } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

interface CustomersTableSkeletonProps {
  rows?: number
}

export const CustomersTableSkeleton = memo(({ rows = 5 }: CustomersTableSkeletonProps) => {
  return (
    <>
      {/* Mobile loading cards */}
      <div className="block md:hidden space-y-4">
        {Array.from({ length: rows }).map((_, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1 space-y-2">
                  <div className="flex items-center space-x-2">
                    <Skeleton className="h-5 w-[160px]" />
                    <Skeleton className="h-4 w-[80px]" />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Skeleton className="h-3 w-3" />
                    <Skeleton className="h-4 w-[180px]" />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Skeleton className="h-3 w-3" />
                    <Skeleton className="h-4 w-[120px]" />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Skeleton className="h-4 w-[60px]" />
                    <Skeleton className="h-4 w-[100px]" />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Skeleton className="h-4 w-[50px]" />
                    <Skeleton className="h-4 w-[80px]" />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Skeleton className="h-4 w-[60px]" />
                    <Skeleton className="h-4 w-[20px]" />
                  </div>
                </div>
                <Skeleton className="h-9 w-9" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Desktop loading table */}
      <div className="hidden md:block rounded-md border">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="min-w-[150px]">Name</TableHead>
                <TableHead className="min-w-[200px]">Email</TableHead>
                <TableHead className="min-w-[120px] hidden lg:table-cell">Phone</TableHead>
                <TableHead className="min-w-[150px]">Company</TableHead>
                <TableHead className="min-w-[100px] hidden lg:table-cell">Bookings</TableHead>
                <TableHead className="min-w-[120px] hidden lg:table-cell">Created</TableHead>
                <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: rows }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <Skeleton className="h-4 w-[140px]" />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-3 w-3" />
                      <Skeleton className="h-4 w-[160px]" />
                    </div>
                  </TableCell>
                  <TableCell className="hidden lg:table-cell">
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-3 w-3" />
                      <Skeleton className="h-4 w-[100px]" />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-3 w-3" />
                      <Skeleton className="h-4 w-[120px]" />
                    </div>
                  </TableCell>
                  <TableCell className="hidden lg:table-cell">
                    <Skeleton className="h-5 w-[30px]" />
                  </TableCell>
                  <TableCell className="hidden lg:table-cell">
                    <Skeleton className="h-4 w-[90px]" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-8 w-8" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </>
  )
})

CustomersTableSkeleton.displayName = "CustomersTableSkeleton"