"use client";

import React, { useState, useCallback } from "react";
import { ErrorBoundary } from "@/components/ui/error-boundary";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Users, RefreshCw, AlertTriangle, Wifi, WifiOff, Clock, Bug } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useCustomerErrorMonitor } from "@/lib/utils/customer-error-monitoring";

interface CustomerManagementErrorBoundaryProps {
  children: React.ReactNode;
  onRetry?: () => void;
}

interface ErrorState {
  errorCount: number;
  lastErrorTime: Date | null;
  errorType: 'network' | 'server' | 'client' | 'unknown';
}

export function CustomerManagementErrorBoundary({ 
  children, 
  onRetry 
}: CustomerManagementErrorBoundaryProps) {
  const { toast } = useToast();
  const { captureError, getStats } = useCustomerErrorMonitor();
  const [errorState, setErrorState] = useState<ErrorState>({
    errorCount: 0,
    lastErrorTime: null,
    errorType: 'unknown'
  });

  const categorizeError = useCallback((error: Error): ErrorState['errorType'] => {
    const message = error.message.toLowerCase();
    
    // Network and connection errors
    if (message.includes('network') || message.includes('fetch') || message.includes('connection') || 
        message.includes('timeout') || message.includes('aborterror')) {
      return 'network';
    }
    
    // Server errors (5xx)
    if (message.includes('500') || message.includes('502') || message.includes('503') || 
        message.includes('server error') || message.includes('database connection')) {
      return 'server';
    }
    
    // Client errors (4xx) - validation, authorization, not found
    if (message.includes('400') || message.includes('401') || message.includes('403') || 
        message.includes('404') || message.includes('validation') || message.includes('invalid')) {
      return 'client';
    }
    
    return 'unknown';
  }, []);

  const handleError = useCallback((error: Error, errorInfo: React.ErrorInfo) => {
    const errorType = categorizeError(error);
    
    setErrorState(prev => ({
      errorCount: prev.errorCount + 1,
      lastErrorTime: new Date(),
      errorType
    }));

    // Capture error with enhanced monitoring
    const errorId = captureError(error, 'error-boundary', {
      componentStack: errorInfo.componentStack,
      errorType,
      errorCount: errorState.errorCount + 1,
      component: 'customer-management-error-boundary'
    });

    // Show toast notification for error
    const toastDescription = process.env.NODE_ENV === 'development' 
      ? `${getErrorMessage(errorType)} (Error ID: ${errorId.slice(-8)})`
      : getErrorMessage(errorType);

    toast({
      variant: "destructive",
      title: "Customer Management Error",
      description: toastDescription,
    });
  }, [categorizeError, errorState.errorCount, toast, captureError]);

  const getErrorMessage = useCallback((errorType: ErrorState['errorType']): string => {
    switch (errorType) {
      case 'network':
        return 'Network connection issue. Please check your internet connection and try again.';
      case 'server':
        return 'Server error. Our team has been notified and is working on a fix. Please try again in a few moments.';
      case 'client':
        return 'Request error. This could be due to invalid data or permissions. Please refresh the page and try again.';
      default:
        return 'An unexpected error occurred in the customer management system. Please try refreshing the page.';
    }
  }, []);

  const getErrorIcon = useCallback((errorType: ErrorState['errorType']) => {
    switch (errorType) {
      case 'network':
        return <WifiOff className="w-6 h-6 text-red-600" />;
      case 'server':
        return <AlertTriangle className="w-6 h-6 text-red-600" />;
      case 'client':
        return <Bug className="w-6 h-6 text-red-600" />;
      default:
        return <AlertTriangle className="w-6 h-6 text-red-600" />;
    }
  }, []);

  const handleRetryWithFeedback = useCallback(async () => {
    if (onRetry) {
      toast({
        title: "Retrying...",
        description: "Attempting to reload customer management system.",
      });
      
      try {
        await onRetry();
        toast({
          title: "Success",
          description: "Customer management system loaded successfully.",
        });
      } catch (error) {
        toast({
          variant: "destructive",
          title: "Retry Failed",
          description: "Unable to reload the system. Please try again.",
        });
      }
    }
  }, [onRetry, toast]);

  const fallbackComponent = (
    <Card className="w-full">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
          {getErrorIcon(errorState.errorType)}
        </div>
        <CardTitle className="text-xl font-bold text-gray-900 flex items-center justify-center">
          <Users className="mr-2 h-5 w-5" />
          Customer Management Error
        </CardTitle>
        <CardDescription>
          {getErrorMessage(errorState.errorType)}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Error Statistics */}
        <div className="flex flex-wrap gap-2 justify-center">
          <Badge variant="destructive">
            Error Count: {errorState.errorCount}
          </Badge>
          <Badge variant="outline">
            Type: {errorState.errorType.toUpperCase()}
          </Badge>
          {errorState.lastErrorTime && (
            <Badge variant="outline" className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {errorState.lastErrorTime.toLocaleTimeString()}
            </Badge>
          )}
        </div>

        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium">
                The customer management system encountered an error and cannot be displayed.
              </p>
              <div className="text-sm space-y-2">
                {errorState.errorType === 'network' && (
                  <div>
                    <p className="font-medium">Network Issue Detected:</p>
                    <ul className="space-y-1 ml-4">
                      <li>• Check your internet connection</li>
                      <li>• Try refreshing the page</li>
                      <li>• Contact IT support if the problem persists</li>
                    </ul>
                  </div>
                )}
                {errorState.errorType === 'server' && (
                  <div>
                    <p className="font-medium">Server Issue Detected:</p>
                    <ul className="space-y-1 ml-4">
                      <li>• Our team has been automatically notified</li>
                      <li>• Please try again in a few minutes</li>
                      <li>• Check our status page for updates</li>
                    </ul>
                  </div>
                )}
                {errorState.errorType === 'client' && (
                  <div>
                    <p className="font-medium">Request Issue Detected:</p>
                    <ul className="space-y-1 ml-4">
                      <li>• This could be due to invalid data or permissions</li>
                      <li>• Try refreshing the page</li>
                      <li>• Clear your browser cache if the problem persists</li>
                    </ul>
                  </div>
                )}
                {errorState.errorType === 'unknown' && (
                  <div>
                    <p className="font-medium">Troubleshooting Steps:</p>
                    <ul className="space-y-1 ml-4">
                      <li>• Try refreshing the page</li>
                      <li>• Check your internet connection</li>
                      <li>• Clear your browser cache</li>
                      <li>• Contact support if the issue continues</li>
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </AlertDescription>
        </Alert>

        {/* Network Status Indicator */}
        <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
          {typeof window !== 'undefined' && navigator.onLine ? (
            <>
              <Wifi className="h-4 w-4 text-green-600" />
              <span>Network Connected</span>
            </>
          ) : typeof window !== 'undefined' ? (
            <>
              <WifiOff className="h-4 w-4 text-red-600" />
              <span>Network Disconnected</span>
            </>
          ) : (
            <>
              <Wifi className="h-4 w-4 text-gray-400" />
              <span>Checking Connection...</span>
            </>
          )}
        </div>

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {onRetry && (
            <Button onClick={handleRetryWithFeedback} className="flex items-center min-h-[44px]">
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry Loading Customers
            </Button>
          )}
          
          <Button 
            variant="outline" 
            onClick={() => {
              toast({
                title: "Refreshing Page",
                description: "Reloading the entire application...",
              });
              window.location.reload();
            }}
            className="min-h-[44px]"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh Page
          </Button>
        </div>

        <div className="text-center text-sm text-muted-foreground space-y-2">
          <p>If this problem persists, please contact your system administrator.</p>
          <p className="text-xs">
            Error ID: {Date.now().toString(36)} | 
            Time: {new Date().toISOString()}
          </p>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <ErrorBoundary
      fallback={fallbackComponent}
      onError={handleError}
      maxRetries={3}
      showErrorDetails={process.env.NODE_ENV === "development"}
    >
      {children}
    </ErrorBoundary>
  );
}