"use client"

import { memo, useCallback } from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { Loader2, AlertTriangle, Building, Mail, Phone } from "lucide-react"
import { Customer } from "@/lib/types"

interface DeleteConfirmationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  customer: Customer | null
  onConfirm: () => Promise<void>
  loading?: boolean
}

export const DeleteConfirmationDialog = memo(({
  open,
  onOpenChange,
  customer,
  onConfirm,
  loading = false,
}: DeleteConfirmationDialogProps) => {
  if (!customer) return null

  const handleConfirm = useCallback(async () => {
    try {
      await onConfirm()
      onOpenChange(false)
    } catch (error) {
      // Error handling is done in the parent component
      console.error('Delete confirmation error:', error)
    }
  }, [onConfirm, onOpenChange])

  const bookingCount = customer.bookings?.length || 0

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            <AlertDialogTitle>Delete Customer</AlertDialogTitle>
          </div>
          <AlertDialogDescription asChild>
            <div className="space-y-3">
              <p>
                Are you sure you want to delete this customer? This action cannot be undone.
              </p>
              
              <div className="rounded-lg border p-4 bg-muted/50">
                <h4 className="font-medium mb-2">Customer Details:</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Name:</span>
                    <span className="font-medium">{customer.name}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Email:</span>
                    <div className="flex items-center space-x-1">
                      <Mail className="h-3 w-3 text-muted-foreground" />
                      <span className="font-medium">{customer.email}</span>
                    </div>
                  </div>
                  {customer.phoneNumber && (
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">Phone:</span>
                      <div className="flex items-center space-x-1">
                        <Phone className="h-3 w-3 text-muted-foreground" />
                        <span className="font-medium">{customer.phoneNumber}</span>
                      </div>
                    </div>
                  )}
                  {customer.companyName && (
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">Company:</span>
                      <div className="flex items-center space-x-1">
                        <Building className="h-3 w-3 text-muted-foreground" />
                        <span className="font-medium">{customer.companyName}</span>
                      </div>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Bookings:</span>
                    <Badge variant="secondary" className="text-xs">
                      {bookingCount}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Created:</span>
                    <span className="font-medium">
                      {new Date(customer.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>

              {bookingCount > 0 ? (
                <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
                  <div className="flex items-start space-x-2">
                    <AlertTriangle className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-destructive">
                        Cannot Delete Customer
                      </p>
                      <p className="text-xs text-destructive/80">
                        This customer has {bookingCount} existing booking{bookingCount !== 1 ? 's' : ''}. 
                        Please remove all bookings before deleting the customer.
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
                  <div className="flex items-start space-x-2">
                    <AlertTriangle className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-destructive">
                        Warning: This action is permanent
                      </p>
                      <ul className="text-xs text-destructive/80 space-y-1">
                        <li>• The customer will be permanently removed from the system</li>
                        <li>• All customer data and history will be lost</li>
                        <li>• Any related records may be affected</li>
                        <li>• This action cannot be reversed</li>
                      </ul>
                    </div>
                  </div>
                </div>
              )}

              {bookingCount === 0 && (
                <p className="text-sm text-muted-foreground">
                  Please confirm that you want to permanently delete <strong>{customer.name}</strong>.
                </p>
              )}
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter>
          <AlertDialogCancel disabled={loading}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={loading || bookingCount > 0}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {bookingCount > 0 ? "Cannot Delete" : "Delete Customer"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
})

DeleteConfirmationDialog.displayName = "DeleteConfirmationDialog"