"use client";

import { useState, useCallback, useMemo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Plus, RefreshCw, AlertCircle, Users } from "lucide-react";

// Import components
import { CustomersTable } from "./components/customers-table";
import { DeleteConfirmationDialog } from "./components/delete-confirmation-dialog";
import { SearchInput } from "./components/search-input";
import { PaginationControls } from "./components/pagination-controls";
import { CustomerManagementErrorBoundary } from "./components/customer-management-error-boundary";

// Import custom hook
import { useCustomers } from "@/hooks/use-customers";

// Import types
import { Customer } from "@/lib/types";

interface DialogState {
  deleteConfirmation: {
    open: boolean;
    customer: Customer | null;
    loading: boolean;
  };
}

export default function CustomersPage() {
  // Use custom hook for customer data management
  const {
    customers,
    loading,
    error,
    totalCustomers,
    totalPages,
    currentPage,
    pageSize,
    searchQuery,
    createCustomer,
    updateCustomer,
    deleteCustomer,
    setSearchQuery,
    setCurrentPage,
    setPageSize,
    refresh,
    clearError,
  } = useCustomers();

  // Dialog states
  const [dialogs, setDialogs] = useState<DialogState>({
    deleteConfirmation: {
      open: false,
      customer: null,
      loading: false,
    },
  });

  // Customer form handlers
  const handleCreateCustomer = useCallback(() => {
    window.location.href = "/dashboard/customers/create";
  }, []);

  const handleEditCustomer = useCallback((customer: Customer) => {
    window.location.href = `/dashboard/customers/${customer.id}`;
  }, []);



  // Delete customer handlers
  const handleDeleteCustomer = useCallback((customerId: number) => {
    const customer = customers.find(c => c.id === customerId);
    if (customer) {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: true, customer, loading: false },
      }));
    }
  }, [customers]);

  const handleDeleteConfirm = useCallback(async () => {
    const { customer } = dialogs.deleteConfirmation;
    if (!customer) return;

    setDialogs(prev => ({
      ...prev,
      deleteConfirmation: { ...prev.deleteConfirmation, loading: true },
    }));

    try {
      await deleteCustomer(customer.id);

      // Close dialog on success
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: false, customer: null, loading: false },
      }));
    } catch (error) {
      // Error handling is done in the hook
      console.error("Error deleting customer:", error);
    } finally {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { ...prev.deleteConfirmation, loading: false },
      }));
    }
  }, [dialogs.deleteConfirmation.customer, deleteCustomer]);

  // Dialog close handlers
  const handleDeleteConfirmationClose = useCallback((open: boolean) => {
    if (!open) {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: false, customer: null, loading: false },
      }));
    }
  }, []);

  // Memoized values for better performance
  const totalCustomersText = useMemo(() => 
    `${totalCustomers} total customers`, 
    [totalCustomers]
  );

  const shouldShowPagination = useMemo(() => 
    !loading && customers.length > 0, 
    [loading, customers.length]
  );

  return (
    <CustomerManagementErrorBoundary onRetry={refresh}>
      <div className="h-full bg-gray-50 p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Customers</h1>
            <p className="text-gray-600 mt-1 text-sm sm:text-base">Manage customer information and relationships</p>
          </div>
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
            <Button
              variant="outline"
              onClick={refresh}
              disabled={loading}
              className="min-h-[44px] sm:min-h-auto"
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} />
              Refresh
            </Button>
            <Button onClick={handleCreateCustomer} className="min-h-[44px] sm:min-h-auto">
              <Plus className="mr-2 h-4 w-4" />
              Add Customer
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <div>
                <CardTitle className="flex items-center">
                  <Users className="mr-2 h-5 w-5" />
                  Customer Management
                </CardTitle>
                {loading ? (
                  <div className="text-sm text-muted-foreground">
                    <Skeleton className="h-4 w-48" />
                  </div>
                ) : (
                  <CardDescription>
                    {totalCustomersText}
                  </CardDescription>
                )}
              </div>
              <div className="w-full lg:w-80">
                <SearchInput
                  onSearch={setSearchQuery}
                  placeholder="Search customers by name, email, or company..."
                />
              </div>
            </div>
          </CardHeader>
          
          <CardContent>
            {error ? (
              <Alert variant="destructive" className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {error}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      clearError();
                      refresh();
                    }}
                    className="ml-2"
                  >
                    Try Again
                  </Button>
                </AlertDescription>
              </Alert>
            ) : (
              <>
                <CustomersTable
                  customers={customers}
                  loading={loading}
                  onEdit={handleEditCustomer}
                  onDelete={handleDeleteCustomer}
                  onRefresh={refresh}
                />
                
                {shouldShowPagination && (
                  <div className="mt-4 sm:mt-6">
                    <PaginationControls
                      currentPage={currentPage}
                      totalPages={totalPages}
                      pageSize={pageSize}
                      totalItems={totalCustomers}
                      onPageChange={setCurrentPage}
                      onPageSizeChange={setPageSize}
                    />
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>

        {/* Dialogs */}
        <DeleteConfirmationDialog
          open={dialogs.deleteConfirmation.open}
          onOpenChange={handleDeleteConfirmationClose}
          customer={dialogs.deleteConfirmation.customer}
          onConfirm={handleDeleteConfirm}
          loading={dialogs.deleteConfirmation.loading}
        />
      </div>
    </CustomerManagementErrorBoundary>
  );
}