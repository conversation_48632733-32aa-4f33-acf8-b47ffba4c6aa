"use client";

import { useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, User } from "lucide-react";
import { CustomerInformationForm } from "../components/customer-information-form";
import { CustomerManagementErrorBoundary } from "../components/customer-management-error-boundary";
import { useCustomers } from "@/hooks/use-customers";
import { CustomerFormData } from "@/lib/types";
import { toast } from "@/hooks/use-toast";

export default function CreateCustomerPage() {
  const router = useRouter();
  const { createCustomer } = useCustomers();
  const [loading, setLoading] = useState(false);

  const handleSubmit = useCallback(async (data: CustomerFormData) => {
    setLoading(true);
    try {
      await createCustomer(data);
      toast({
        title: "Success",
        description: "Customer created successfully.",
      });
      router.push("/dashboard/customers");
    } catch (error) {
      console.error("Error creating customer:", error);
      toast({
        title: "Error",
        description: "Failed to create customer. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [createCustomer, router]);

  const handleCancel = useCallback(() => {
    router.push("/dashboard/customers");
  }, [router]);

  return (
    <CustomerManagementErrorBoundary>
      <div className="h-full bg-gray-50 p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={loading}
              className="min-h-[44px] sm:min-h-auto"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Customers
            </Button>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Create Customer</h1>
              <p className="text-gray-600 mt-1 text-sm sm:text-base">Add a new customer to the system</p>
            </div>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="mr-2 h-5 w-5" />
              Customer Management
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="customer-info" className="w-full">
              <TabsList className="grid w-full grid-cols-1">
                <TabsTrigger value="customer-info" className="flex items-center">
                  <User className="mr-2 h-4 w-4" />
                  Customer Information
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="customer-info" className="mt-6">
                <CustomerInformationForm
                  onSubmit={handleSubmit}
                  onCancel={handleCancel}
                  loading={loading}
                  submitButtonText="Create Customer"
                />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </CustomerManagementErrorBoundary>
  );
}