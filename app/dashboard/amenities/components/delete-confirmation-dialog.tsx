"use client"

import { memo, useCallback } from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Loader2, AlertTriangle } from "lucide-react"
import { Amenity } from "@/lib/types"
import { getAmenityIcon } from "@/lib/utils/amenity-icons"

interface DeleteConfirmationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  amenity: Amenity | null
  onConfirm: () => Promise<void>
  loading?: boolean
}

export const DeleteConfirmationDialog = memo(({
  open,
  onOpenChange,
  amenity,
  onConfirm,
  loading = false,
}: DeleteConfirmationDialogProps) => {
  if (!amenity) return null

  const handleConfirm = useCallback(async () => {
    try {
      await onConfirm()
      onOpenChange(false)
    } catch (error) {
      // Error handling is done in the parent component
      console.error('Delete confirmation error:', error)
    }
  }, [onConfirm, onOpenChange])

  const IconComponent = getAmenityIcon(amenity.icon)

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent 
        className="mobile-spacing"
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <AlertDialogHeader>
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-destructive" aria-hidden="true" />
            <AlertDialogTitle id="delete-dialog-title">Delete Amenity</AlertDialogTitle>
          </div>
          <AlertDialogDescription asChild>
            <div className="space-y-4" id="delete-dialog-description">
              <p>
                Are you sure you want to delete this amenity? This action cannot be undone.
              </p>
              
              <div className="rounded-lg border p-4 bg-muted/50">
                <h4 className="font-medium mb-2">Amenity Details:</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Name:</span>
                    <span className="font-medium">{amenity.name}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Icon:</span>
                    <div className="flex items-center space-x-2">
                      <IconComponent className="h-4 w-4" />
                      <span className="font-medium">
                        {amenity.icon.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                      </span>
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Created:</span>
                    <span className="font-medium">
                      {amenity.createdAt 
                        ? (() => {
                            try {
                              return new Date(amenity.createdAt).toLocaleDateString();
                            } catch (error) {
                              return 'Invalid date';
                            }
                          })()
                        : 'N/A'
                      }
                    </span>
                  </div>
                </div>
              </div>

              <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-destructive">
                      Warning: This action is permanent
                    </p>
                    <ul className="text-xs text-destructive/80 space-y-1">
                      <li>• The amenity will be permanently removed from the system</li>
                      <li>• If this amenity is associated with resources, deletion will be prevented</li>
                      <li>• All references to this amenity will be lost</li>
                      <li>• This action cannot be reversed</li>
                    </ul>
                  </div>
                </div>
              </div>

              <p className="text-sm text-muted-foreground">
                Please confirm that you want to delete the amenity <strong>{amenity.name}</strong>, or click Cancel to abort.
              </p>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter className="flex flex-col sm:flex-row gap-3">
          <AlertDialogCancel 
            disabled={loading}
            className="touch-target focus-enhanced flex-1 sm:flex-none"
          >
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={loading}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90 touch-target focus-enhanced flex-1 sm:flex-none"
            aria-describedby="delete-dialog-description"
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />}
            Delete {amenity.name}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
})

DeleteConfirmationDialog.displayName = "DeleteConfirmationDialog"