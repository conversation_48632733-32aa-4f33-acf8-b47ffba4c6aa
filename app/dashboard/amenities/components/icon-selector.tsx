"use client";

import { useState, memo, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Check, ChevronDown, Search } from "lucide-react";
import { AmenityIcon } from "@/lib/types";
import { getAmenityIconOptions, getAmenityIcon } from "@/lib/utils/amenity-icons";
import { cn } from "@/lib/utils";

interface IconSelectorProps {
  value: AmenityIcon;
  onValueChange: (value: AmenityIcon) => void;
  disabled?: boolean;
  "aria-describedby"?: string;
  "aria-invalid"?: boolean;
}

export const IconSelector = memo(({
  value,
  onValueChange,
  disabled = false,
  "aria-describedby": ariaDescribedBy,
  "aria-invalid": ariaInvalid,
}: IconSelectorProps) => {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const iconOptions = useMemo(() => getAmenityIconOptions(), []);

  // Filter icons based on search query
  const filteredOptions = useMemo(() => {
    if (!searchQuery.trim()) return iconOptions;
    
    const query = searchQuery.toLowerCase();
    return iconOptions.filter(option =>
      option.label.toLowerCase().includes(query) ||
      option.value.toLowerCase().includes(query)
    );
  }, [iconOptions, searchQuery]);

  const selectedOption = useMemo(() => 
    iconOptions.find(option => option.value === value),
    [iconOptions, value]
  );

  const handleSelect = (iconValue: AmenityIcon) => {
    onValueChange(iconValue);
    setOpen(false);
    setSearchQuery(""); // Clear search when selecting
  };

  const SelectedIcon = selectedOption ? getAmenityIcon(selectedOption.value) : null;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          aria-haspopup="listbox"
          aria-label={`Select icon, current selection: ${selectedOption?.label || "None selected"}`}
          aria-describedby={ariaDescribedBy}
          aria-invalid={ariaInvalid}
          className={cn(
            "w-full justify-between h-auto touch-target p-4 focus-enhanced",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          disabled={disabled}
        >
          <div className="flex items-center space-x-3">
            {SelectedIcon && (
              <div className="flex items-center justify-center w-8 h-8 rounded-md bg-muted">
                <SelectedIcon className="h-5 w-5" />
              </div>
            )}
            <div className="flex flex-col items-start">
              <span className="font-medium">
                {selectedOption?.label || "Select an icon"}
              </span>
              {selectedOption && (
                <Badge variant="secondary" className="text-xs mt-1">
                  {selectedOption.value}
                </Badge>
              )}
            </div>
          </div>
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      
      <PopoverContent 
        className="w-[400px] p-0" 
        align="start"
        role="listbox"
        aria-label="Icon selection"
      >
        <div className="p-4 border-b">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" aria-hidden="true" />
            <Input
              placeholder="Search icons..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9 touch-target focus-enhanced"
              aria-label="Search for icons"
            />
          </div>
        </div>
        
        <ScrollArea className="h-[300px]">
          <div className="p-3">
            {filteredOptions.length === 0 ? (
              <div className="text-center py-6 text-muted-foreground">
                No icons found matching "{searchQuery}"
              </div>
            ) : (
              <div className="grid grid-cols-2 gap-2">
                {filteredOptions.map((option) => {
                  const IconComponent = getAmenityIcon(option.value);
                  const isSelected = value === option.value;
                  
                  return (
                    <Button
                      key={option.value}
                      variant={isSelected ? "default" : "ghost"}
                      className={cn(
                        "h-auto p-4 justify-start space-x-3 touch-target focus-enhanced",
                        isSelected && "bg-primary text-primary-foreground"
                      )}
                      onClick={() => handleSelect(option.value)}
                      role="option"
                      aria-selected={isSelected}
                      aria-label={`${option.label} icon`}
                    >
                      <div className={cn(
                        "flex items-center justify-center w-8 h-8 rounded-md",
                        isSelected ? "bg-primary-foreground/20" : "bg-muted"
                      )}>
                        <IconComponent className={cn(
                          "h-5 w-5",
                          isSelected ? "text-primary-foreground" : "text-foreground"
                        )} />
                      </div>
                      <div className="flex flex-col items-start flex-1 min-w-0">
                        <span className="font-medium text-sm truncate w-full">
                          {option.label}
                        </span>
                        <Badge 
                          variant={isSelected ? "secondary" : "outline"} 
                          className="text-xs mt-1"
                        >
                          {option.value}
                        </Badge>
                      </div>
                      {isSelected && (
                        <Check className="h-4 w-4 ml-auto" />
                      )}
                    </Button>
                  );
                })}
              </div>
            )}
          </div>
        </ScrollArea>
        
        {searchQuery && (
          <div className="p-4 border-t bg-muted/50">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSearchQuery("")}
              className="w-full touch-target focus-enhanced"
              aria-label="Clear search query"
            >
              Clear search
            </Button>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
});

IconSelector.displayName = "IconSelector";