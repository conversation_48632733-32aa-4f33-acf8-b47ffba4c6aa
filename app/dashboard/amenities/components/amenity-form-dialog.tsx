"use client";

import { useEffect, useState, memo, useCallback, useMemo } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle, CheckCircle2 } from "lucide-react";
import { Amenity, AmenityFormData } from "@/lib/types";
import { amenityCreateSchema, amenityUpdateSchema } from "@/lib/validations/amenity";
import { 
  mapServerErrorsToForm, 
  getApiErrorMessage, 
  createErrorSummary,
  hasFormErrors 
} from "@/lib/utils/form-errors";
import { IconSelector } from "./icon-selector";

interface AmenityFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  amenity?: Amenity | null; // null for create, Amenity object for edit
  onSubmit: (data: AmenityFormData) => Promise<void>;
  loading?: boolean;
}

export const AmenityFormDialog = memo(({
  open,
  onOpenChange,
  amenity,
  onSubmit,
  loading = false,
}: AmenityFormDialogProps) => {
  const isEdit = !!amenity;
  const schema = isEdit ? amenityUpdateSchema : amenityCreateSchema;
  const [serverError, setServerError] = useState<string>("");

  const form = useForm<AmenityFormData>({
    resolver: zodResolver(schema),
    mode: "onChange", // Enable real-time validation
    defaultValues: {
      name: "",
      icon: "PROJECTOR", // Default icon
    },
  });

  // Reset form when dialog opens/closes or amenity changes
  useEffect(() => {
    if (open) {
      setServerError(""); // Clear server errors when opening
      if (isEdit && amenity) {
        form.reset({
          name: amenity.name,
          icon: amenity.icon,
        });
      } else {
        form.reset({
          name: "",
          icon: "PROJECTOR", // Default icon for new amenities
        });
      }
    }
  }, [open, isEdit, amenity, form]);

  const handleSubmit = useCallback(async (data: AmenityFormData) => {
    setServerError(""); // Clear previous server errors
    
    try {
      await onSubmit(data);
      onOpenChange(false);
    } catch (error) {
      console.error("Form submission error:", error);
      
      // Handle server validation errors
      if (error && typeof error === 'object' && 'details' in error) {
        mapServerErrorsToForm(error as any, form.setError as any);
      }
      
      // Set general server error message
      const errorMessage = getApiErrorMessage(error);
      setServerError(errorMessage);
    }
  }, [onSubmit, onOpenChange, form]);

  const handleCancel = useCallback(() => {
    form.reset();
    setServerError("");
    onOpenChange(false);
  }, [form, onOpenChange]);

  // Get error summary for display - memoized to prevent unnecessary recalculations
  const errorSummary = useMemo(() => 
    createErrorSummary(form.formState.errors, serverError), 
    [form.formState.errors, serverError]
  );
  
  const canSubmit = useMemo(() => 
    form.formState.isValid && !loading, 
    [form.formState.isValid, loading]
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent 
        className="sm:max-w-[500px] mobile-spacing"
        aria-labelledby="amenity-form-title"
        aria-describedby="amenity-form-description"
      >
        <DialogHeader>
          <DialogTitle id="amenity-form-title">
            {isEdit ? "Edit Amenity" : "Create New Amenity"}
          </DialogTitle>
          <DialogDescription id="amenity-form-description">
            {isEdit
              ? "Update the amenity information below. All fields are required."
              : "Fill in the details below to create a new amenity. All fields are required."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* Error Summary */}
            {errorSummary.hasErrors && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-1">
                    <p className="font-medium">{errorSummary.summary}</p>
                    {errorSummary.messages.length > 1 && (
                      <ul className="list-disc list-inside text-sm space-y-1">
                        {errorSummary.messages.map((message, index) => (
                          <li key={index}>{message}</li>
                        ))}
                      </ul>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            )}

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel htmlFor="amenity-name">
                    Amenity Name <span className="text-destructive" aria-label="required">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      id="amenity-name"
                      placeholder="Enter amenity name"
                      {...field}
                      disabled={loading}
                      className="touch-target focus-enhanced"
                      aria-describedby="amenity-name-description amenity-name-error"
                      aria-invalid={!!form.formState.errors.name}
                    />
                  </FormControl>
                  <FormDescription id="amenity-name-description">
                    Name must be unique and contain only letters, numbers, spaces, and basic punctuation.
                  </FormDescription>
                  <FormMessage id="amenity-name-error" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="icon"
              render={({ field }) => (
                <FormItem>
                  <FormLabel htmlFor="amenity-icon">
                    Icon <span className="text-destructive" aria-label="required">*</span>
                  </FormLabel>
                  <FormControl>
                    <IconSelector
                      value={field.value}
                      onValueChange={field.onChange}
                      disabled={loading}
                      aria-describedby="amenity-icon-description amenity-icon-error"
                      aria-invalid={!!form.formState.errors.icon}
                    />
                  </FormControl>
                  <FormDescription id="amenity-icon-description">
                    Choose an icon that best represents this amenity.
                  </FormDescription>
                  <FormMessage id="amenity-icon-error" />
                </FormItem>
              )}
            />

            <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
              {/* Form validation status */}
              <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-2 sm:mb-0">
                {form.formState.isValid && !serverError ? (
                  <div className="flex items-center text-green-600">
                    <CheckCircle2 className="h-4 w-4 mr-1" />
                    Form is valid
                  </div>
                ) : hasFormErrors(form.formState.errors) || serverError ? (
                  <div className="flex items-center text-red-600">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {errorSummary.errorCount} error{errorSummary.errorCount !== 1 ? 's' : ''}
                  </div>
                ) : null}
              </div>
              
              <div className="flex space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={loading}
                  className="touch-target focus-enhanced flex-1 sm:flex-none"
                >
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={!canSubmit}
                  className="touch-target focus-enhanced min-w-[140px] flex-1 sm:flex-none"
                  aria-describedby={errorSummary.hasErrors ? "form-errors" : undefined}
                >
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />}
                  {isEdit ? "Update Amenity" : "Create Amenity"}
                </Button>
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
});

AmenityFormDialog.displayName = "AmenityFormDialog";