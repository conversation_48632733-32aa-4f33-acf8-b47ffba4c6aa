"use client"

import { useState, memo, useMemo, useCallback } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {
  MoreHorizontal,
  Edit,
  Trash2,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Package,
} from "lucide-react"
import { Amenity } from "@/lib/types"
import { format } from "date-fns"
import { getAmenityIcon } from "@/lib/utils/amenity-icons"
import { AmenitiesTableSkeleton } from "./amenities-table-skeleton"

interface AmenitiesTableProps {
  amenities: Amenity[]
  loading: boolean
  onEdit: (amenity: Amenity) => void
  onDelete: (amenityId: number) => void
  onRefresh: () => void
}

type SortField = 'name' | 'icon' | 'createdAt'
type SortDirection = 'asc' | 'desc'

interface SortConfig {
  field: SortField
  direction: SortDirection
}

// Mobile card component for individual amenities - memoized for performance
const AmenityCard = memo(({ 
  amenity, 
  onEdit, 
  onDelete 
}: { 
  amenity: Amenity
  onEdit: (amenity: Amenity) => void
  onDelete: (amenityId: number) => void
}) => {
  const IconComponent = getAmenityIcon(amenity.icon)
  
  return (
    <Card className="mb-4">
      <CardContent className="mobile-spacing">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-3">
              <div 
                className="flex items-center justify-center w-10 h-10 rounded-md bg-muted"
                aria-hidden="true"
              >
                <IconComponent className="h-5 w-5" />
              </div>
              <h3 className="font-medium text-base" id={`amenity-${amenity.id}`}>
                {amenity.name}
              </h3>
            </div>
            
            <div className="space-y-1 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <span className="font-medium">Icon:</span>
                <span className="capitalize">
                  {amenity.icon.replace(/_/g, ' ').toLowerCase()}
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                <span className="font-medium">Created:</span>
                <span>
                  {amenity.createdAt 
                    ? (() => {
                        try {
                          return format(new Date(amenity.createdAt), 'MMM dd, yyyy');
                        } catch (error) {
                          return 'Invalid date';
                        }
                      })()
                    : 'N/A'
                  }
                </span>
              </div>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                className="h-11 w-11 p-0 touch-target focus-enhanced"
                aria-label={`Actions for ${amenity.name}`}
              >
                <span className="sr-only">Open actions menu for {amenity.name}</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem 
                onClick={() => onEdit(amenity)}
                className="cursor-pointer touch-target focus-enhanced"
                role="menuitem"
              >
                <Edit className="mr-2 h-4 w-4" aria-hidden="true" />
                Edit {amenity.name}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDelete(amenity.id)}
                className="text-destructive cursor-pointer touch-target focus-enhanced"
                role="menuitem"
              >
                <Trash2 className="mr-2 h-4 w-4" aria-hidden="true" />
                Delete {amenity.name}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  )
})

export const AmenitiesTable = memo(({
  amenities,
  loading,
  onEdit,
  onDelete,
  onRefresh,
}: AmenitiesTableProps) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    field: 'createdAt',
    direction: 'desc',
  })

  const handleSort = useCallback((field: SortField) => {
    setSortConfig(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }))
  }, [])

  const getSortIcon = useCallback((field: SortField) => {
    if (sortConfig.field !== field) {
      return <ArrowUpDown className="ml-2 h-4 w-4" />
    }
    return sortConfig.direction === 'asc' ? (
      <ArrowUp className="ml-2 h-4 w-4" />
    ) : (
      <ArrowDown className="ml-2 h-4 w-4" />
    )
  }, [sortConfig.field, sortConfig.direction])

  const sortedAmenities = useMemo(() => {
    return [...amenities].sort((a, b) => {
      const { field, direction } = sortConfig
      let aValue: any = a[field]
      let bValue: any = b[field]

      // Handle date sorting
      if (field === 'createdAt') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      }

      // Handle string sorting
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (aValue < bValue) {
        return direction === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return direction === 'asc' ? 1 : -1
      }
      return 0
    })
  }, [amenities, sortConfig])

  if (loading) {
    return <AmenitiesTableSkeleton />
  }

  if (amenities.length === 0) {
    return (
      <>
        {/* Mobile empty state */}
        <div className="block md:hidden">
          <Card>
            <CardContent className="p-8 text-center">
              <div className="flex flex-col items-center justify-center space-y-4">
                <Package className="h-12 w-12 text-muted-foreground" />
                <div className="space-y-2">
                  <p className="text-lg font-medium">No amenities found</p>
                  <p className="text-sm text-muted-foreground">
                    Try adjusting your search or add a new amenity
                  </p>
                </div>
                <Button 
                  variant="outline" 
                  onClick={onRefresh} 
                  className="touch-target focus-enhanced px-6"
                  aria-label="Refresh amenities list"
                >
                  Refresh
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Desktop empty state */}
        <div className="hidden md:block rounded-md border">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="min-w-[150px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('name')}
                      className="h-auto p-0 font-semibold"
                    >
                      Name
                      {getSortIcon('name')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[120px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('icon')}
                      className="h-auto p-0 font-semibold"
                    >
                      Icon
                      {getSortIcon('icon')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[120px] hidden lg:table-cell">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('createdAt')}
                      className="h-auto p-0 font-semibold"
                    >
                      Created
                      {getSortIcon('createdAt')}
                    </Button>
                  </TableHead>
                  <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center">
                    <div className="flex flex-col items-center justify-center space-y-2">
                      <p className="text-muted-foreground">No amenities found</p>
                      <Button 
                        variant="outline" 
                        onClick={onRefresh} 
                        className="touch-target focus-enhanced px-6"
                        aria-label="Refresh amenities list"
                      >
                        Refresh
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>
      </>
    )
  }

  return (
    <>
      {/* Mobile card view */}
      <div className="block md:hidden">
        {/* Mobile sort controls */}
        <div className="flex items-center justify-between mb-4 p-4 bg-muted/50 rounded-lg mobile-spacing">
          <span className="text-sm font-medium" id="sort-label">Sort by:</span>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="outline" 
                size="sm" 
                className="touch-target focus-enhanced"
                aria-labelledby="sort-label"
                aria-expanded="false"
              >
                {sortConfig.field === 'name' && 'Name'}
                {sortConfig.field === 'icon' && 'Icon'}
                {sortConfig.field === 'createdAt' && 'Created'}
                {sortConfig.direction === 'asc' ? (
                  <ArrowUp className="ml-2 h-4 w-4" aria-hidden="true" />
                ) : (
                  <ArrowDown className="ml-2 h-4 w-4" aria-hidden="true" />
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-40" role="menu">
              <DropdownMenuItem 
                onClick={() => handleSort('name')}
                className="touch-target focus-enhanced"
                role="menuitem"
              >
                Name {getSortIcon('name')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('icon')}
                className="touch-target focus-enhanced"
                role="menuitem"
              >
                Icon {getSortIcon('icon')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('createdAt')}
                className="touch-target focus-enhanced"
                role="menuitem"
              >
                Created {getSortIcon('createdAt')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="space-y-4">
          {sortedAmenities.map((amenity) => (
            <AmenityCard
              key={amenity.id}
              amenity={amenity}
              onEdit={onEdit}
              onDelete={onDelete}
            />
          ))}
        </div>
      </div>

      {/* Desktop table view */}
      <div className="hidden md:block rounded-md border">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="min-w-[150px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('name')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by name ${sortConfig.field === 'name' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Name
                    {getSortIcon('name')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[120px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('icon')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by icon ${sortConfig.field === 'icon' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Icon
                    {getSortIcon('icon')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[120px] hidden lg:table-cell">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('createdAt')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by creation date ${sortConfig.field === 'createdAt' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Created
                    {getSortIcon('createdAt')}
                  </Button>
                </TableHead>
                <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedAmenities.map((amenity) => {
                const IconComponent = getAmenityIcon(amenity.icon)
                
                return (
                  <TableRow key={amenity.id}>
                    <TableCell className="font-medium">
                      {amenity.name}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <div className="flex items-center justify-center w-6 h-6 rounded bg-muted">
                          <IconComponent className="h-4 w-4" />
                        </div>
                        <span className="capitalize text-sm">
                          {amenity.icon.replace(/_/g, ' ').toLowerCase()}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-muted-foreground hidden lg:table-cell">
                      {amenity.createdAt 
                        ? (() => {
                            try {
                              return format(new Date(amenity.createdAt), 'MMM dd, yyyy');
                            } catch (error) {
                              return 'Invalid date';
                            }
                          })()
                        : 'N/A'
                      }
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button 
                            variant="ghost" 
                            className="h-10 w-10 p-0 focus-enhanced"
                            aria-label={`Actions for ${amenity.name}`}
                          >
                            <span className="sr-only">Open actions menu for {amenity.name}</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48" role="menu">
                          <DropdownMenuItem 
                            onClick={() => onEdit(amenity)}
                            className="cursor-pointer focus-enhanced py-3"
                            role="menuitem"
                          >
                            <Edit className="mr-2 h-4 w-4" aria-hidden="true" />
                            Edit {amenity.name}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => onDelete(amenity.id)}
                            className="text-destructive cursor-pointer focus-enhanced py-3"
                            role="menuitem"
                          >
                            <Trash2 className="mr-2 h-4 w-4" aria-hidden="true" />
                            Delete {amenity.name}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </div>
      </div>
    </>
  )
})