"use client";

import React, { useState, useCallback } from "react";
import { ErrorBoundary } from "@/components/ui/error-boundary";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Settings, Refresh<PERSON>w, AlertTriangle, Wifi, WifiOff, Clock, Bug } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface AmenityManagementErrorBoundaryProps {
  children: React.ReactNode;
  onRetry?: () => void;
}

interface ErrorState {
  errorCount: number;
  lastErrorTime: Date | null;
  errorType: 'network' | 'server' | 'client' | 'unknown';
}

export function AmenityManagementErrorBoundary({ 
  children, 
  onRetry 
}: AmenityManagementErrorBoundaryProps) {
  const { toast } = useToast();
  const [errorState, setErrorState] = useState<ErrorState>({
    errorCount: 0,
    lastErrorTime: null,
    errorType: 'unknown'
  });

  const categorizeError = useCallback((error: Error): ErrorState['errorType'] => {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
      return 'network';
    }
    
    if (message.includes('500') || message.includes('502') || message.includes('503')) {
      return 'server';
    }
    
    if (message.includes('400') || message.includes('401') || message.includes('403') || message.includes('404')) {
      return 'client';
    }
    
    return 'unknown';
  }, []);

  const handleError = useCallback((error: Error, errorInfo: React.ErrorInfo) => {
    const errorType = categorizeError(error);
    
    setErrorState(prev => ({
      errorCount: prev.errorCount + 1,
      lastErrorTime: new Date(),
      errorType
    }));

    // Log amenity management specific errors with enhanced context
    const errorContext = {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      context: "amenity-management",
      errorType,
      userAgent: navigator.userAgent,
      url: window.location.href,
      errorCount: errorState.errorCount + 1,
    };

    console.error("Amenity Management Error:", errorContext);

    // Show toast notification for error
    toast({
      variant: "destructive",
      title: "Amenity Management Error",
      description: getErrorMessage(errorType),
    });

    // You could send this to your monitoring service
    // errorMonitoringService.captureException(error, {
    //   tags: { 
    //     component: "amenity-management",
    //     errorType,
    //     errorCount: errorState.errorCount + 1
    //   },
    //   extra: errorContext
    // });
  }, [categorizeError, errorState.errorCount, toast]);

  const getErrorMessage = useCallback((errorType: ErrorState['errorType']): string => {
    switch (errorType) {
      case 'network':
        return 'Network connection issue. Please check your internet connection.';
      case 'server':
        return 'Server error. Our team has been notified and is working on a fix.';
      case 'client':
        return 'Request error. Please try refreshing the page.';
      default:
        return 'An unexpected error occurred in the amenity management system.';
    }
  }, []);

  const getErrorIcon = useCallback((errorType: ErrorState['errorType']) => {
    switch (errorType) {
      case 'network':
        return <WifiOff className="w-6 h-6 text-red-600" />;
      case 'server':
        return <AlertTriangle className="w-6 h-6 text-red-600" />;
      case 'client':
        return <Bug className="w-6 h-6 text-red-600" />;
      default:
        return <AlertTriangle className="w-6 h-6 text-red-600" />;
    }
  }, []);

  const handleRetryWithFeedback = useCallback(async () => {
    if (onRetry) {
      toast({
        title: "Retrying...",
        description: "Attempting to reload amenity management system.",
      });
      
      try {
        await onRetry();
        toast({
          title: "Success",
          description: "Amenity management system loaded successfully.",
        });
      } catch (error) {
        toast({
          variant: "destructive",
          title: "Retry Failed",
          description: "Unable to reload the system. Please try again.",
        });
      }
    }
  }, [onRetry, toast]);

  const fallbackComponent = (
    <Card className="w-full">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
          {getErrorIcon(errorState.errorType)}
        </div>
        <CardTitle className="text-xl font-bold text-gray-900 flex items-center justify-center">
          <Settings className="mr-2 h-5 w-5" />
          Amenity Management Error
        </CardTitle>
        <CardDescription>
          {getErrorMessage(errorState.errorType)}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Error Statistics */}
        <div className="flex flex-wrap gap-2 justify-center">
          <Badge variant="destructive">
            Error Count: {errorState.errorCount}
          </Badge>
          <Badge variant="outline">
            Type: {errorState.errorType.toUpperCase()}
          </Badge>
          {errorState.lastErrorTime && (
            <Badge variant="outline" className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {errorState.lastErrorTime.toLocaleTimeString()}
            </Badge>
          )}
        </div>

        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium">
                The amenity management system encountered an error and cannot be displayed.
              </p>
              <ul className="text-sm space-y-1 ml-4">
                <li>• This could be due to a network connectivity issue</li>
                <li>• The server might be temporarily unavailable</li>
                <li>• There could be a temporary system malfunction</li>
              </ul>
            </div>
          </AlertDescription>
        </Alert>

        {/* Network Status Indicator */}
        <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
          {typeof window !== 'undefined' && navigator.onLine ? (
            <>
              <Wifi className="h-4 w-4 text-green-600" />
              <span>Network Connected</span>
            </>
          ) : typeof window !== 'undefined' ? (
            <>
              <WifiOff className="h-4 w-4 text-red-600" />
              <span>Network Disconnected</span>
            </>
          ) : (
            <>
              <Wifi className="h-4 w-4 text-gray-400" />
              <span>Checking Connection...</span>
            </>
          )}
        </div>

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {onRetry && (
            <Button onClick={handleRetryWithFeedback} className="flex items-center min-h-[44px]">
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry Loading Amenities
            </Button>
          )}
          
          <Button 
            variant="outline" 
            onClick={() => {
              toast({
                title: "Refreshing Page",
                description: "Reloading the entire application...",
              });
              window.location.reload();
            }}
            className="min-h-[44px]"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh Page
          </Button>
        </div>

        <div className="text-center text-sm text-muted-foreground space-y-2">
          <p>If this problem persists, please contact your system administrator.</p>
          <p className="text-xs">
            Error ID: {Date.now().toString(36)} | 
            Time: {new Date().toISOString()}
          </p>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <ErrorBoundary
      fallback={fallbackComponent}
      onError={handleError}
      maxRetries={3}
      showErrorDetails={process.env.NODE_ENV === "development"}
    >
      {children}
    </ErrorBoundary>
  );
}