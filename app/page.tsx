
'use client'
import { useRouter } from 'next/navigation'
import { VillageEntrance } from '@/components/village-entrance'
import dynamic from 'next/dynamic'
const VillageExplorer = dynamic(() => import('@/components/village-explorer').then(mod => ({ default: mod.VillageExplorer })), { loading: () => <div>Loading district...</div> })
const BusinessDistrictExplorer = dynamic(() => import('@/components/business-district-explorer').then(mod => ({ default: mod.BusinessDistrictExplorer })), { loading: () => <div>Loading business district...</div> })
import { StorySection } from '@/components/story-section'
import { CTASection } from '@/components/cta-section'
import { Footer } from '@/components/footer'
import { unstable_batchedUpdates } from 'react-dom';
import { Header } from '@/components/header';

export type ExplorationMode = 'entrance' | 'map' | 'guided' | 'free' | 'district' | 'business'
export type DistrictType = 'square' | 'school' | 'market' | 'field' | 'council' | 'workshop' | 'guide' | 'bureau' | 'gateway' | 'portfolio' | 'spot' | 'business'
export type BusinessType = 'sadeed' | 'tamam' | 'smart-access' | 'spot'

export default function Home() {
  const router = useRouter();
  const handleEnterVillage = (mode: 'guided' | 'free') => {
    router.push('/map');
  }

  return (
    <main className="min-h-screen bg-gradient-to-b from-amber-50 to-orange-50">
      <Header currentMode="entrance" discoveredCount={0} onViewMap={() => {}} />
      <VillageEntrance onEnterVillage={handleEnterVillage} />
      <StorySection />
      <CTASection />
      <Footer />
    </main>
  )
}
