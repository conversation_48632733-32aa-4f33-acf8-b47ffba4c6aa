"use client"
import { useRouter, useParams } from 'next/navigation';
import { useLanguage } from '@/components/language-provider';
import { ArrowLeft, Home, Mail, Users } from 'lucide-react';
import Image from 'next/image';
import { Header } from '@/components/header';
import { useContext } from 'react';
import { useVillageStore } from '@/components/ui/village-context';

const businessData = {
  sadeed: {
    icon: require('lucide-react').Scale,
    color: 'from-amber-600 to-amber-700',
    image: 'https://cdn.abacus.ai/images/9e611d47-70c8-44cb-81f5-629df994f3c0.png',
  },
  tamam: {
    icon: require('lucide-react').Hash,
    color: 'from-purple-600 to-purple-700',
    image: 'https://cdn.abacus.ai/images/7426ce8e-0e68-4604-96c2-92fb4d9c16e2.png',
  },
  'smart-access': {
    icon: require('lucide-react').Laptop,
    color: 'from-blue-600 to-blue-700',
    image: 'https://cdn.abacus.ai/images/b2a23339-c998-4db1-9206-177f0f6ae1eb.png',
  },
  spot: {
    icon: require('lucide-react').Coffee,
    color: 'from-orange-600 to-orange-700',
    image: 'https://cdn.abacus.ai/images/e29a5e6b-6aa6-48cd-a96b-4420d0eb4a10.png',
  },
};

export default function BusinessDetailPage() {
  const router = useRouter();
  const params = useParams();
  const { t } = useLanguage();
  const business = params.business as keyof typeof businessData;
  const config = businessData[business];
  if (!config) {
    return (
      <main className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Business not found</h1>
          <button className="px-4 py-2 bg-amber-500 text-white rounded" onClick={() => router.push('/business')}>
            Back to Business District
          </button>
        </div>
      </main>
    );
  }
  const IconComponent = config.icon;
  const getBusinessName = (b: string) => {
    const nameMap: Record<string, string> = {
      sadeed: 'business_sadeed_name',
      tamam: 'business_tamam_name',
      'smart-access': 'business_smart_access_name',
      spot: 'business_spot_name',
    };
    return t(nameMap[b]);
  };
  const getBusinessDesc = (b: string) => {
    const descMap: Record<string, string> = {
      sadeed: 'business_sadeed_desc',
      tamam: 'business_tamam_desc',
      'smart-access': 'business_smart_access_desc',
      spot: 'business_spot_desc',
    };
    return t(descMap[b]);
  };
  const getBusinessDetails = (b: string) => {
    const detailsMap: Record<string, string> = {
      sadeed: 'business_sadeed_details',
      tamam: 'business_tamam_details',
      'smart-access': 'business_smart_access_details',
      spot: 'business_spot_details',
    };
    return t(detailsMap[b]);
  };
  const getBusinessServices = (b: string) => {
    const servicesMap: Record<string, string> = {
      sadeed: 'business_sadeed_services',
      tamam: 'business_tamam_services',
      'smart-access': 'business_smart_access_services',
      spot: 'business_spot_services',
    };
    return t(servicesMap[b]);
  };
  const discoveredBusinesses = useVillageStore(state => state.discoveredBusinesses);

  return (
    <>
      <Header
        onViewMap={() => router.push('/map')}
        currentMode="business"
        discoveredCount={discoveredBusinesses.size}
      />
      <section className="min-h-screen bg-gradient-to-b from-gray-50 to-white pt-20">
        {/* Hero Section for Business */}
        <div className="relative h-96 overflow-hidden">
          <Image
            src={config.image}
            alt={getBusinessName(business)}
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/30 to-transparent" />
          {/* Business Header */}
          <div className="absolute inset-0 flex items-center">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
              <div className="max-w-2xl">
                <div className="flex items-center space-x-4 mb-4">
                  <div className={`w-16 h-16 bg-gradient-to-br ${config.color} rounded-xl flex items-center justify-center shadow-lg`}>
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h1 className="text-4xl md:text-6xl font-bold text-white leading-tight">
                      {getBusinessName(business)}
                    </h1>
                    <p className="text-white/90 text-lg mt-2">
                      {getBusinessDesc(business)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* Business Content */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Navigation */}
          <div className="flex items-center justify-between mb-8">
            <button
              className="flex items-center space-x-2 border border-gray-300 rounded px-4 py-2 bg-white hover:bg-gray-50"
              onClick={() => router.push('/business')}
            >
              <ArrowLeft className="h-4 w-4" />
              <span>{t('return_to_business_district')}</span>
            </button>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <Home className="h-4 w-4" />
              <span>{t('business_district')} / {getBusinessName(business)}</span>
            </div>
          </div>
          {/* Business Description */}
          <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">{t('about_business')}</h3>
            <div className="prose prose-lg max-w-none">
              <p className="text-gray-700 leading-relaxed text-lg mb-6">
                {getBusinessDetails(business)}
              </p>
            </div>
            {/* Services */}
            <div className="border-t pt-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">{t('our_services')}</h4>
              <p className="text-gray-700 leading-relaxed">
                {getBusinessServices(business)}
              </p>
            </div>
          </div>
          {/* Contact Information */}
          <div className="bg-gradient-to-r from-teal-50 to-teal-100 rounded-2xl p-6 mb-8 border border-teal-200">
            <div className="flex items-start space-x-4">
              <div className={`w-12 h-12 bg-gradient-to-br ${config.color} rounded-xl flex items-center justify-center`}>
                <IconComponent className="w-6 h-6 text-white" />
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-teal-900 mb-2">{t('connect_with_us')}</h4>
                <p className="text-teal-800 text-sm leading-relaxed mb-4">
                  {t('business_contact_message')}
                </p>
                <div className="flex flex-wrap gap-3">
                  <div className="flex items-center space-x-2 bg-white px-3 py-2 rounded-lg shadow-sm">
                    <Mail className="h-4 w-4 text-teal-600" />
                    <span className="text-sm text-gray-700">{t('email_us')}</span>
                  </div>
                  <div className="flex items-center space-x-2 bg-white px-3 py-2 rounded-lg shadow-sm">
                    <Users className="h-4 w-4 text-teal-600" />
                    <span className="text-sm text-gray-700">{t('visit_office')}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
