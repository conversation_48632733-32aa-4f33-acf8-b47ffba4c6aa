"use client"
import { useContext } from 'react';
import { useRouter } from 'next/navigation';
import { Header } from '@/components/header';
import dynamic from 'next/dynamic';
import { useVillageStore } from '@/components/ui/village-context';
import type { BusinessType } from '@/app/page';

const BusinessDistrictExplorer = dynamic(() => import('@/components/business-district-explorer').then(mod => mod.BusinessDistrictExplorer), { loading: () => <div>Loading business district...</div> });

export default function BusinessPage() {
  const router = useRouter();
  const discoveredBusinesses = useVillageStore(state => state.discoveredBusinesses);
  const setDiscoveredBusinesses = useVillageStore(state => state.setDiscoveredBusinesses);

  const handleReturnToMap = () => {
    router.push('/map');
  };

  const handleBusinessVisit = (business: BusinessType) => {
    setDiscoveredBusinesses(prev => {
      const next = new Set(prev);
      next.add(business);
      return next;
    });
    // Navigate to /business/[business] for individual business view
    router.push(`/business/${business}`);
  };

  return (
    <main className="min-h-screen bg-gradient-to-b from-amber-50 to-orange-50">
      <Header onViewMap={() => router.push('/map')} currentMode="business" discoveredCount={discoveredBusinesses.size} />
      <BusinessDistrictExplorer
        onReturnToMap={handleReturnToMap}
        onBusinessVisit={handleBusinessVisit}
        discoveredBusinesses={discoveredBusinesses}
        currentBusiness={null}
        onReturnToBusinessDistrict={() => {}}
      />
    </main>
  );
}
