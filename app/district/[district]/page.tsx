"use client";

import { useRouter, useParams } from "next/navigation";
import { Header } from "@/components/header";
import dynamic from "next/dynamic";
import { useVillageStore } from "@/components/ui/village-context";
import type { DistrictType } from "@/app/page";

const VillageExplorer = dynamic(
  () =>
    import("@/components/village-explorer").then((mod) => mod.VillageExplorer),
  { loading: () => <div>Loading district...</div> }
);

export default function DistrictPage() {
  const router = useRouter();
  const params = useParams();
  const discoveredDistricts = useVillageStore(
    (state) => state.discoveredDistricts
  );
  const setDiscoveredDistricts = useVillageStore(
    (state) => state.setDiscoveredDistricts
  );
  const district = params.district as DistrictType;

  const handleReturnToMap = () => {
    router.push("/map");
  };

  const handleNextDistrict = (nextDistrict: DistrictType) => {
    setDiscoveredDistricts((prev) => {
      const next = new Set(prev);
      next.add(nextDistrict);
      return next;
    });
    router.push(`/district/${nextDistrict}`);
  };

  return (
    <main className="min-h-screen bg-gradient-to-b from-amber-50 to-orange-50">
      <Header
        onViewMap={() => router.push("/map")}
        currentMode="district"
        discoveredCount={discoveredDistricts.size}
      />
      <VillageExplorer
        district={district}
        onReturnToMap={handleReturnToMap}
        onNextDistrict={handleNextDistrict}
        discoveredDistricts={discoveredDistricts}
      />
    </main>
  );
}
