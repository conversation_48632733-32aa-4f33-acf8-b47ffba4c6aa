"use client"
import { useContext } from 'react';
import { useRouter } from 'next/navigation';
import { Header } from '@/components/header';
import { VillageMap } from '@/components/village-map';
import { StorySection } from '@/components/story-section';
import { CTASection } from '@/components/cta-section';
import { Footer } from '@/components/footer';
import { useVillageStore } from '@/components/ui/village-context';
import type { DistrictType } from '@/app/page';

export default function MapPage() {
  const router = useRouter();
  const discoveredDistricts = useVillageStore(state => state.discoveredDistricts);
  const setDiscoveredDistricts = useVillageStore(state => state.setDiscoveredDistricts);

  const handleDistrictSelect = (district: DistrictType) => {
    setDiscoveredDistricts(prev => {
      const next = new Set(prev);
      next.add(district);
      return next;
    });
    if (district === 'business') {
      router.push('/business');
    } else {
      router.push(`/district/${district}`);
    }
  };

  return (
    <main className="min-h-screen bg-gradient-to-b from-amber-50 to-orange-50">
      <Header onViewMap={() => {}} currentMode="map" discoveredCount={discoveredDistricts.size} />
      <VillageMap onDistrictSelect={handleDistrictSelect} discoveredDistricts={discoveredDistricts} />
      <StorySection />
      <CTASection />
      <Footer />
    </main>
  );
}
