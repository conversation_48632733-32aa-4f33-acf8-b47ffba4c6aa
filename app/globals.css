
@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Rubik:wght@400;500;700&display=swap');

:root {
  --font-sans: 'Rubik', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}

body {
  font-family: var(--font-sans);
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Village-specific styles */
.village-parallax {
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #d97706;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #b45309;
}

/* RTL Support */
[dir="rtl"] {
  direction: rtl;
  text-align: right;
}

[dir="rtl"] .flex {
  flex-direction: row-reverse;
}

/* Village exploration animations */
@keyframes village-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

.village-pulse {
  animation: village-pulse 2s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float {
  animation: float 3s ease-in-out infinite;
}

/* Village pathway effect */
.village-path {
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(217, 119, 6, 0.3) 25%, 
    rgba(217, 119, 6, 0.5) 50%, 
    rgba(217, 119, 6, 0.3) 75%, 
    transparent 100%
  );
  animation: village-path-flow 3s ease-in-out infinite;
}

@keyframes village-path-flow {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Interactive district hover effects */
.district-card {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.district-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.district-card:hover::before {
  left: 100%;
}

.district-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Village entrance portal effect */
.entrance-portal {
  background: radial-gradient(ellipse at center, 
    rgba(217, 119, 6, 0.1) 0%, 
    rgba(217, 119, 6, 0.05) 50%, 
    transparent 100%
  );
  animation: portal-glow 4s ease-in-out infinite alternate;
}

@keyframes portal-glow {
  0% {
    box-shadow: 0 0 20px rgba(217, 119, 6, 0.3);
  }
  100% {
    box-shadow: 0 0 40px rgba(217, 119, 6, 0.6);
  }
}

/* Village guide speaking animation */
.guide-speaking {
  animation: guide-bounce 0.6s ease-in-out infinite alternate;
}

@keyframes guide-bounce {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(-3px);
  }
}

/* Progress bar village themed */
.village-progress {
  background: linear-gradient(90deg, 
    #d97706 0%, 
    #f59e0b 50%, 
    #d97706 100%
  );
  background-size: 200% 100%;
  animation: progress-shine 2s linear infinite;
}

@keyframes progress-shine {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Village map interactive elements */
.map-district {
  cursor: pointer;
  transition: all 0.2s ease;
  filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.1));
}

.map-district:hover {
  filter: drop-shadow(4px 4px 8px rgba(0,0,0,0.2));
  transform: scale(1.05);
}

.map-district.discovered {
  filter: drop-shadow(0 0 10px rgba(34, 197, 94, 0.5));
}

/* Village storytelling text effects */
.story-text {
  background: linear-gradient(45deg, #1f2937, #374151, #1f2937);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: story-shimmer 3s ease-in-out infinite;
}

@keyframes story-shimmer {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* CTA button scale effect (replaces Framer Motion hover/active) */
.cta-scale-effect {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}
.cta-scale-effect:hover {
  transform: scale(1.05);
}
.cta-scale-effect:active {
  transform: scale(0.95);
}

/* Card scale effect (replaces Framer Motion hover/active for cards and logo) */
.card-scale-effect {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}
.card-scale-effect:hover {
  transform: scale(1.05);
}
.card-scale-effect:active {
  transform: scale(0.95);
}

/* Hero CTA button shadow effect */
.hero-cta-shadow:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Disable hover scale effects on touch devices */
@media (hover: none) and (pointer: coarse) {
  .cta-scale-effect:hover,
  .card-scale-effect:hover {
    transform: none !important;
  }
}

/* Accessibility and Mobile Optimization Utilities */

/* Minimum touch target size for mobile accessibility */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Enhanced touch targets for mobile devices */
@media (hover: none) and (pointer: coarse) {
  .touch-target {
    min-height: 48px;
    min-width: 48px;
    padding: 0.75rem;
  }
  
  /* Larger touch areas for small interactive elements */
  .touch-target-small {
    min-height: 40px;
    min-width: 40px;
    padding: 0.5rem;
  }
  
  /* Extra large touch targets for primary actions */
  .touch-target-large {
    min-height: 56px;
    min-width: 56px;
    padding: 1rem;
  }
}

/* Enhanced focus indicators for better accessibility */
.focus-enhanced:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
  box-shadow: 0 0 0 2px hsl(var(--background)), 0 0 0 4px hsl(var(--ring));
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .high-contrast-border {
    border-width: 2px;
    border-color: currentColor;
  }
  
  .high-contrast-text {
    font-weight: 600;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .village-pulse,
  .float,
  .village-path,
  .portal-glow,
  .guide-speaking,
  .progress-shine,
  .story-shimmer {
    animation: none !important;
  }
}

/* Mobile-first responsive spacing */
.mobile-spacing {
  padding: 1rem;
}

@media (min-width: 640px) {
  .mobile-spacing {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .mobile-spacing {
    padding: 2rem;
  }
}

/* Enhanced mobile form layouts */
.mobile-form-layout {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

@media (min-width: 640px) {
  .mobile-form-layout {
    gap: 1rem;
  }
}

/* Mobile-optimized dialog content */
.mobile-dialog {
  max-height: 90vh;
  overflow-y: auto;
  margin: 1rem;
  width: calc(100vw - 2rem);
  max-width: 100%;
}

@media (min-width: 640px) {
  .mobile-dialog {
    margin: 2rem;
    width: auto;
    max-width: 700px;
  }
}

/* Mobile-friendly button groups */
.mobile-button-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
}

@media (min-width: 640px) {
  .mobile-button-group {
    flex-direction: row;
    gap: 0.5rem;
    width: auto;
  }
  
  .mobile-button-group > * {
    flex: none;
  }
}

/* Mobile card optimizations */
.mobile-card {
  border-radius: 0.75rem;
  padding: 1.25rem;
  margin-bottom: 1rem;
}

@media (min-width: 640px) {
  .mobile-card {
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 0.75rem;
  }
}

/* Mobile table alternatives */
.mobile-table-card {
  display: block;
  border: 1px solid hsl(var(--border));
  border-radius: 0.75rem;
  padding: 1.25rem;
  margin-bottom: 1rem;
  background: hsl(var(--card));
}

@media (min-width: 768px) {
  .mobile-table-card {
    display: none;
  }
}

/* Enhanced mobile touch areas */
.mobile-touch-area {
  padding: 0.75rem;
  margin: 0.25rem;
}

@media (min-width: 640px) {
  .mobile-touch-area {
    padding: 0.5rem;
    margin: 0.125rem;
  }
}

/* Skip link for keyboard navigation */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 6px;
}

/* Screen reader only content */
.sr-only-focusable:not(:focus):not(:focus-within) {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Mobile-specific optimizations */
@media (max-width: 767px) {
  /* Ensure proper viewport handling */
  body {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }
  
  /* Prevent horizontal scrolling */
  .mobile-no-scroll {
    overflow-x: hidden;
    max-width: 100vw;
  }
  
  /* Mobile-friendly input fields */
  input[type="text"],
  input[type="number"],
  input[type="email"],
  input[type="password"],
  textarea,
  select {
    font-size: 16px; /* Prevents zoom on iOS */
    min-height: 44px;
  }
  
  /* Mobile dropdown improvements */
  .mobile-dropdown {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: calc(100vw - 2rem) !important;
    max-width: 400px !important;
    max-height: 70vh !important;
    overflow-y: auto !important;
    z-index: 9999 !important;
  }
  
  /* Mobile modal improvements */
  .mobile-modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100% !important;
    height: 100% !important;
    max-width: none !important;
    max-height: none !important;
    border-radius: 0 !important;
    margin: 0 !important;
  }
  
  /* Mobile-friendly radio and checkbox groups */
  .mobile-radio-group,
  .mobile-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .mobile-radio-group > *,
  .mobile-checkbox-group > * {
    padding: 1rem;
    border: 1px solid hsl(var(--border));
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .mobile-radio-group > *:hover,
  .mobile-checkbox-group > *:hover {
    background-color: hsl(var(--muted));
  }
}

/* Tablet-specific optimizations */
@media (min-width: 768px) and (max-width: 1023px) {
  .tablet-optimized {
    padding: 1.5rem;
  }
  
  /* Tablet touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Large mobile devices */
@media (min-width: 475px) and (max-width: 767px) {
  .large-mobile-spacing {
    padding: 1.25rem;
  }
  
  .large-mobile-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
}
