model Catering {
  id              Int                 @id @default(autoincrement())
  offerName       String
  pricePerPerson  Float
  firstPartyShare Float
  vendorShare     Float
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt
  createdById     Int?
  updatedById     Int?
  updatedBy       User?               @relation("CateringUpdatedBy", fields: [updatedById], references: [id])
  createdBy       User?               @relation("CateringCreatedBy", fields: [createdById], references: [id])
  bookings        CateringOnBooking[]
  lineItems       LineItem[]
}

model CateringOnBooking {
  id          Int      @id @default(autoincrement())
  bookingId   Int
  cateringId  Int
  quantity    Int
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdById Int?
  updatedById Int?
  updatedBy   User?    @relation("CateringOnBookingUpdatedBy", fields: [updatedById], references: [id])
  createdBy   User?    @relation("CateringOnBookingCreatedBy", fields: [createdById], references: [id])
  catering    Catering @relation(fields: [cateringId], references: [id])
  booking     Booking  @relation(fields: [bookingId], references: [id])
}
