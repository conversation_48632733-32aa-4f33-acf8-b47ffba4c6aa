model User {
  id                         Int                  @id @default(autoincrement())
  firstName                  String
  lastName                   String
  username                   String               @unique
  password                   String
  role                       String               @default("ADMIN")
  isSuperUser                Boolean              @default(false)
  createdAt                  DateTime             @default(now())
  updatedAt                  DateTime             @updatedAt
  createdById                Int?
  updatedById                Int?
  amenitiesUpdated           Amenity[]            @relation("AmenityUpdatedBy")
  amenitiesCreated           Amenity[]            @relation("AmenityCreatedBy")
  bookingsUpdated            Booking[]            @relation("BookingUpdatedBy")
  bookingsCreated            Booking[]            @relation("BookingCreatedBy")
  cateringsUpdated           Catering[]           @relation("CateringUpdatedBy")
  cateringsCreated           Catering[]           @relation("CateringCreatedBy")
  cateringOnBookingsUpdated  CateringOnBooking[]  @relation("CateringOnBookingUpdatedBy")
  cateringOnBookingsCreated  CateringOnBooking[]  @relation("CateringOnBookingCreatedBy")
  customersUpdated           Customer[]           @relation("CustomerUpdatedBy")
  customersCreated           Customer[]           @relation("CustomerCreatedBy")
  invoicesUpdated            Invoice[]            @relation("InvoiceUpdatedBy")
  invoicesCreated            Invoice[]            @relation("InvoiceCreatedBy")
  lineItemsUpdated           LineItem[]           @relation("LineItemUpdatedBy")
  lineItemsCreated           LineItem[]           @relation("LineItemCreatedBy")
  paymentsUpdated            Payment[]            @relation("PaymentUpdatedBy")
  paymentsCreated            Payment[]            @relation("PaymentCreatedBy")
  resourcesUpdated           Resource[]           @relation("ResourceUpdatedBy")
  resourcesCreated           Resource[]           @relation("ResourceCreatedBy")
  resourceStageStylesUpdated ResourceStageStyle[] @relation("ResourceStageStyleUpdatedBy")
  resourceStageStylesCreated ResourceStageStyle[] @relation("ResourceStageStyleCreatedBy")
  updatedBy                  User?                @relation("UserUpdatedBy", fields: [updatedById], references: [id])
  usersUpdated               User[]               @relation("UserUpdatedBy")
  createdBy                  User?                @relation("UserCreatedBy", fields: [createdById], references: [id])
  usersCreated               User[]               @relation("UserCreatedBy")
}
