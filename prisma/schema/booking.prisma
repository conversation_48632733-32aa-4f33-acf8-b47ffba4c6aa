model Booking {
  id          Int                 @id @default(autoincrement())
  customerId  Int
  status      String              @default("PENDING")
  start       DateTime
  end         DateTime
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt
  createdById Int?
  updatedById Int?
  updatedBy   User?               @relation("BookingUpdatedBy", fields: [updatedById], references: [id])
  createdBy   User?               @relation("BookingCreatedBy", fields: [createdById], references: [id])
  customer    Customer            @relation(fields: [customerId], references: [id])
  caterings   CateringOnBooking[]
  invoice     Invoice?
  resources   Resource[]          @relation("BookingToResource")
}
