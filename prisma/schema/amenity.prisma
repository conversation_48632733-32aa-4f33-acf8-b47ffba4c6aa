model Amenity {
  id          Int        @id @default(autoincrement())
  name        String
  icon        String
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  createdById Int?
  updatedById Int?
  updatedBy   User?      @relation("AmenityUpdatedBy", fields: [updatedById], references: [id])
  createdBy   User?      @relation("AmenityCreatedBy", fields: [createdById], references: [id])
  resources   Resource[] @relation("AmenityToResource")
}
