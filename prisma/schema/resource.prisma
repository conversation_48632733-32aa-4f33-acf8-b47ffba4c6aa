model Resource {
  id                Int                  @id @default(autoincrement())
  name              String
  type              String
  basePrice         Float
  details           String?
  seatingStyle      String?
  numberOfAttendees Int?
  numberOfDesks     Int?
  numberOfChairs    Int?
  createdAt         DateTime             @default(now())
  updatedAt         DateTime             @updatedAt
  createdById       Int?
  updatedById       Int?
  updatedBy         User?                @relation("ResourceUpdatedBy", fields: [updatedById], references: [id])
  createdBy         User?                @relation("ResourceCreatedBy", fields: [createdById], references: [id])
  stageStyles       ResourceStageStyle[]
  amenities         Amenity[]            @relation("AmenityToResource")
  bookings          Booking[]            @relation("BookingToResource")
}

model ResourceStageStyle {
  id          Int      @id @default(autoincrement())
  resourceId  Int
  style       String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdById Int?
  updatedById Int?
  updatedBy   User?    @relation("ResourceStageStyleUpdatedBy", fields: [updatedById], references: [id])
  createdBy   User?    @relation("ResourceStageStyleCreatedBy", fields: [createdById], references: [id])
  resource    Resource @relation(fields: [resourceId], references: [id])
}
