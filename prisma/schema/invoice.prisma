model Invoice {
  id          Int        @id @default(autoincrement())
  bookingId   Int        @unique
  status      String     @default("PENDING")
  total       Decimal
  paid        Decimal    @default(0)
  pdfUrl      String?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  createdById Int?
  updatedById Int?
  updatedBy   User?      @relation("InvoiceUpdatedBy", fields: [updatedById], references: [id])
  createdBy   User?      @relation("InvoiceCreatedBy", fields: [createdById], references: [id])
  booking     Booking    @relation(fields: [bookingId], references: [id])
  lineItems   LineItem[]
  payments    Payment[]
}

model LineItem {
  id          Int       @id @default(autoincrement())
  invoiceId   Int
  description String
  amount      Decimal
  isCatering  Boolean   @default(false)
  qty         Int       @default(1)
  cateringId  Int?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  createdById Int?
  updatedById Int?
  updatedBy   User?     @relation("LineItemUpdatedBy", fields: [updatedById], references: [id])
  createdBy   User?     @relation("LineItemCreatedBy", fields: [createdById], references: [id])
  catering    Catering? @relation(fields: [cateringId], references: [id])
  invoice     Invoice   @relation(fields: [invoiceId], references: [id])
}

model Payment {
  id          Int       @id @default(autoincrement())
  invoiceId   Int
  amount      Decimal
  method      String
  status      String    @default("PENDING")
  reference   String?
  notes       String?
  paidAt      DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  createdById Int?
  updatedById Int?
  updatedBy   User?     @relation("PaymentUpdatedBy", fields: [updatedById], references: [id])
  createdBy   User?     @relation("PaymentCreatedBy", fields: [createdById], references: [id])
  invoice     Invoice   @relation(fields: [invoiceId], references: [id])
}
