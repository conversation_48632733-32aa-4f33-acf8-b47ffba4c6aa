# Revenue Sharing Calculations Documentation

## Overview

This document provides comprehensive documentation for the revenue sharing calculations implemented in the catering management system. The system uses fixed amounts per person (in Iraqi Dinar - IQD) rather than percentages to ensure precise financial calculations and avoid floating-point precision issues.

## Core Concepts

### Fixed Amount Model

Unlike percentage-based revenue sharing, this system uses fixed amounts per person:

- **Price Per Person**: Total cost per person (e.g., 1000 IQD)
- **First Party Share**: Fixed amount per person for the first party (e.g., 600 IQD)
- **Vendor Share**: Fixed amount per person for the vendor (e.g., 400 IQD)

**Constraint**: `firstPartyShare + vendorShare = pricePerPerson`

### Currency Configuration

The system is configured for Iraqi Dinar (IQD):
- No decimal places (whole numbers only)
- Thousands separator: comma (,)
- Format: "1,000 IQD"

## Core Functions

### 1. Revenue Split Calculation

```typescript
calculateRevenueSplit(quantity, firstPartySharePerPerson, vendorSharePerPerson)
```

**Purpose**: Calculate total revenue split for a given quantity of people.

**Example**:
```typescript
const result = calculateRevenueSplit(10, 600, 400);
// Result: {
//   totalAmount: 10000,
//   firstPartyAmount: 6000,
//   vendorAmount: 4000,
//   firstPartySharePerPerson: 600,
//   vendorSharePerPerson: 400
// }
```

### 2. Booking System Integration

```typescript
calculateBookingRevenueSplit(cateringOffer, quantity)
```

**Purpose**: Calculate detailed revenue breakdown for booking system integration.

**Example**:
```typescript
const cateringOffer = {
  pricePerPerson: 1000,
  firstPartyShare: 600,
  vendorShare: 400
};

const result = calculateBookingRevenueSplit(cateringOffer, 15);
// Result: {
//   totalCost: 15000,
//   firstPartyRevenue: 9000,
//   vendorRevenue: 6000,
//   breakdown: { ... },
//   percentages: { firstPartyPercentage: 60, vendorPercentage: 40 }
// }
```

### 3. Multiple Catering Items

```typescript
calculateMultipleCateringRevenue(cateringItems)
```

**Purpose**: Calculate aggregated revenue for multiple catering items in a single booking.

**Example**:
```typescript
const cateringItems = [
  {
    cateringOffer: { pricePerPerson: 1000, firstPartyShare: 600, vendorShare: 400 },
    quantity: 10
  },
  {
    cateringOffer: { pricePerPerson: 500, firstPartyShare: 200, vendorShare: 300 },
    quantity: 5
  }
];

const result = calculateMultipleCateringRevenue(cateringItems);
// Result: {
//   totalCost: 12500,
//   totalFirstPartyRevenue: 7000,
//   totalVendorRevenue: 5500,
//   itemBreakdown: [...],
//   overallPercentages: { firstPartyPercentage: 56, vendorPercentage: 44 }
// }
```

## Validation Functions

### 1. Revenue Sharing Validation

```typescript
validateRevenueSharing(firstPartyShare, vendorShare, pricePerPerson)
```

**Purpose**: Ensure revenue shares add up to the total price per person.

**Tolerance**: 0.01 IQD to handle floating-point precision issues.

### 2. Catering Offer Validation

```typescript
validateCateringOfferForBooking(cateringOffer)
```

**Purpose**: Comprehensive validation for booking system compatibility.

**Checks**:
- Price per person within valid range (1-999,999 IQD)
- Share amounts within valid range (0-999,999 IQD)
- Revenue sharing totals correctly
- No zero-zero splits
- Warnings for extreme splits (>90% to one party)

### 3. Form Data Validation

```typescript
validateCateringFormData(data)
```

**Purpose**: Validate user input from catering management forms.

**Validates**:
- Offer name (3-100 characters, no "test", not only numbers)
- Price per person (1-999,999 IQD)
- Share amounts (0-999,999 IQD)
- Revenue sharing constraint

## Currency and Formatting

### Currency Formatting

```typescript
formatCurrency(amount) // Returns "1,000 IQD"
```

**Features**:
- Rounds to whole numbers (no decimals for IQD)
- Adds thousands separators
- Handles invalid inputs gracefully

### Monetary Precision

```typescript
formatMonetaryPrecision(amount) // Returns whole number
```

**Purpose**: Ensure all monetary calculations use whole numbers for IQD.

### Percentage Calculations

```typescript
calculatePercentageBreakdown(firstPartyShare, vendorShare)
```

**Purpose**: Calculate display percentages from fixed amounts.

**Features**:
- Handles division by zero
- Rounds to whole percentages
- Provides error logging for invalid inputs

## Error Handling

### Input Validation

All functions validate inputs and handle edge cases:

- **NaN/Infinity**: Converted to 0
- **Negative values**: Converted to 0 (except where negative makes sense)
- **Out of range**: Clamped to valid ranges
- **Invalid types**: Converted to appropriate defaults

### Precision Handling

The system handles floating-point precision issues:

- **Rounding**: All monetary values rounded to whole numbers
- **Tolerance**: 0.01 IQD tolerance for validation comparisons
- **Consistency**: Consistent rounding behavior across all functions

## Real-World Examples

### Wedding Catering (200 guests)

```typescript
const weddingCatering = [
  {
    cateringOffer: {
      pricePerPerson: 50000, // 50,000 IQD per person
      firstPartyShare: 30000, // 60%
      vendorShare: 20000 // 40%
    },
    quantity: 200
  },
  {
    cateringOffer: {
      pricePerPerson: 15000, // Drinks package
      firstPartyShare: 9000, // 60%
      vendorShare: 6000 // 40%
    },
    quantity: 200
  }
];

const result = calculateMultipleCateringRevenue(weddingCatering);
// Total cost: 13,000,000 IQD
// First party revenue: 7,800,000 IQD (60%)
// Vendor revenue: 5,200,000 IQD (40%)
```

### Corporate Meeting (50 attendees)

```typescript
const corporateCatering = {
  cateringOffer: {
    pricePerPerson: 25000, // Business lunch
    firstPartyShare: 15000, // 60%
    vendorShare: 10000 // 40%
  },
  quantity: 50
};

const result = calculateBookingRevenueSplit(corporateCatering.cateringOffer, 50);
// Total cost: 1,250,000 IQD
// First party revenue: 750,000 IQD (60%)
// Vendor revenue: 500,000 IQD (40%)
```

### Budget Event (100 people)

```typescript
const budgetCatering = {
  cateringOffer: {
    pricePerPerson: 8000, // 8,000 IQD per person
    firstPartyShare: 3000, // 37.5%
    vendorShare: 5000 // 62.5%
  },
  quantity: 100
};

const result = calculateBookingRevenueSplit(budgetCatering.cateringOffer, 100);
// Total cost: 800,000 IQD
// First party revenue: 300,000 IQD (38% rounded)
// Vendor revenue: 500,000 IQD (63% rounded)
```

## Integration Points

### Database Schema

The system integrates with the following database relationships:

- **Catering**: Stores offer details and fixed share amounts
- **CateringOnBooking**: Links catering offers to bookings with quantities
- **LineItem**: Tracks individual line items for invoicing

### API Endpoints

Revenue calculations are used in:

- **POST /api/catering**: Validate revenue sharing on creation
- **PUT /api/catering/[id]**: Validate revenue sharing on updates
- **GET /api/bookings**: Calculate total costs and revenue splits
- **POST /api/bookings**: Validate catering selections and calculate costs

### Frontend Components

The calculations support:

- **Catering Form Dialog**: Real-time validation and preview
- **Booking Creation**: Cost calculation and display
- **Revenue Reports**: Financial breakdown and analytics
- **Invoice Generation**: Line item calculations

## Performance Considerations

### Optimization Strategies

1. **Whole Number Arithmetic**: Avoids floating-point precision issues
2. **Input Validation**: Early validation prevents expensive calculations
3. **Memoization**: Results can be cached for repeated calculations
4. **Batch Processing**: Multiple items calculated efficiently

### Scalability

The system handles:

- **Large Events**: Tested with 1000+ attendees
- **Multiple Items**: Efficiently processes 1000+ catering items
- **High Frequency**: Optimized for real-time form validation
- **Memory Efficiency**: No memory leaks in repeated calculations

## Testing Coverage

### Test Categories

1. **Unit Tests**: Individual function testing
2. **Integration Tests**: Booking system integration
3. **Edge Cases**: Boundary values and error conditions
4. **Stress Tests**: Large datasets and performance
5. **Real-World Scenarios**: Practical use cases

### Test Metrics

- **136 tests** covering all functions
- **100% code coverage** for critical paths
- **Edge case coverage** for all input combinations
- **Performance benchmarks** for scalability

## Troubleshooting

### Common Issues

1. **Revenue Sharing Doesn't Add Up**
   - Check that firstPartyShare + vendorShare = pricePerPerson
   - Verify all values are whole numbers (no decimals)

2. **Precision Errors**
   - Use formatMonetaryPrecision() for all monetary values
   - Avoid direct floating-point arithmetic

3. **Validation Failures**
   - Check input ranges (1-999,999 for prices, 0-999,999 for shares)
   - Ensure offer names meet requirements (3-100 chars, no "test")

4. **Performance Issues**
   - Use batch calculations for multiple items
   - Implement caching for repeated calculations
   - Validate inputs early to avoid expensive operations

### Debug Tools

```typescript
// Enable detailed logging
console.log('Revenue split:', calculateRevenueSplit(10, 600, 400));

// Validate configuration
const validation = validateCateringOfferForBooking(offer);
console.log('Validation:', validation);

// Check percentage breakdown
const percentages = calculatePercentageBreakdown(600, 400);
console.log('Percentages:', percentages);
```

## Future Enhancements

### Potential Improvements

1. **Multi-Currency Support**: Extend beyond IQD
2. **Tax Calculations**: Integrate tax handling
3. **Discount System**: Support for promotional pricing
4. **Commission Tiers**: Variable commission rates
5. **Reporting Analytics**: Advanced financial reporting

### Backward Compatibility

Any future changes will maintain backward compatibility with:

- Existing database schema
- Current API contracts
- Frontend component interfaces
- Calculation precision and rounding behavior