# Product Requirements Document: Internal Management Dashboard

## 1. Introduction

### 1.1. Objective

The objective of this project is to develop a comprehensive internal management dashboard for the efficient administration of company resources, customers, bookings, and catering services. This dashboard will serve as the central tool for staff to manage day-to-day operations, streamline workflows, and reduce manual overhead.

### 1.2. Background

Currently, the management of resources and bookings is handled through manual processes, which are time-consuming and prone to error. This document outlines the requirements for a new digital system that will automate and centralize these operations. The initial focus is on an internal-facing dashboard, with the potential for a customer-facing portal in the future.

## 2. Goals

### 2.1. Primary Goals

-   To create a centralized system for managing all company resources.
-   To automate the booking and invoicing process.
-   To improve communication and coordination between admin, logistics, and customers.
-   To provide a foundation for future expansion, such as a customer-facing booking system.

### 2.2. Success Metrics

-   Reduction in time spent by admins on manual booking creation by 80%.
-   100% automation of invoice generation and delivery for new bookings.
-   Elimination of booking conflicts and errors.
-   Positive feedback from admin and logistics staff on usability and efficiency.

## 3. User Personas

### 3.1. Admin

-   **Role:** Manages all aspects of the system, including resources, customers, and bookings.
-   **Needs:** A powerful and intuitive interface to perform daily tasks quickly and efficiently. Needs a clear overview of all activities.

### 3.2. Logistics Staff

-   **Role:** Responsible for preparing and setting up event spaces based on booking requirements.
-   **Needs:** Timely and accurate information about booking details, including attendee numbers and specific setup requirements (e.g., stage, seating).

### 3.3. Customer

-   **Role:** The end-user who books the resources.
-   **Needs:** Clear communication regarding their booking and invoices. Currently, they interact with the system indirectly via notifications.

## 4. Product Requirements

### 4.1. Dashboard Overview

A homepage that provides admins with a snapshot of key metrics and activities, such as:
-   Upcoming bookings.
-   Recent invoices.
-   Resource availability calendar.

### 4.2. Resource Management

A module for admins to perform CRUD (Create, Read, Update, Delete) operations on all bookable resources.

#### 4.2.1. Indoor Event Hall
-   **Seating Style:** Single choice from a dropdown list (`Cinema style`, `Round tables`).
-   **Number of Attendees:** Numeric input.
-   **Stage Style:** Multiple choice checkboxes (`Podium`, `Panel`).
-   **Base Price:** Numeric input.

#### 4.2.2. Outdoor Event Hall
-   **Details:** A free-form text input field for specific requirements.
-   **Base Price:** Numeric input.

#### 4.2.3. Training Rooms
-   **Number of Attendees:** Numeric input.
-   **Seating Style:** Single choice from a dropdown list (`Tables`, `Cinema`).
-   **Base Price:** Numeric input.

#### 4.2.4. Meeting Rooms
-   **Details:** To be determined (TBD). The system should be flexible enough to add specifications later. Suggested fields: AV equipment, whiteboard availability.
-   **Base Price:** Numeric input.

#### 4.2.5. Desks
-   Manage individual or a pool of desks.
-   **Base Price:** Numeric input.

#### 4.2.6. Private Offices
-   **Number of Desks:** Numeric input.
-   **Number of Chairs:** Numeric input.
-   **Base Price:** Numeric input.

### 4.3. Customer Management (CRM)

A simple CRM to manage customer information.
-   CRUD operations for customers.
-   Fields: Name, Email, Phone Number (for WhatsApp), Company Name, Specialization, Industry, Website, LinkedIn, Social Media, Notes.

### 4.4. Booking Management

-   Admins can create, view, update, and cancel bookings.
-   A booking must associate a customer with one or more resources for a specified date and time.
-   The system must prevent double-booking of resources.
-   A calendar view to visualize all bookings.

### 4.5. Invoice Management

-   Invoices are automatically generated upon confirmation of a booking.
-   The invoice should include line items for booked resources and any catering services.
-   The invoice should put a clear distinction between the line items and the catering services.
-   Invoices are automatically sent to the customer's registered email address.
-   Admins can view all generated invoices and their payment status (status to be manually updated for now).
-   Invoices should support partial payments.

### 4.6. Catering Management

-   CRUD interface for catering offers.
-   Each offer must have:
    -   **Offer Name:** Text field.
    -   **Price Per Person:** Numeric field.
    -   **First Party Share:** Percentage or fixed amount.
    -   **Vendor Share:** Percentage or fixed amount.
-   Ability to add one or more catering offers to a booking.

### 4.7. Notifications

The system must send automated notifications for key events.

-   **Booking Confirmation:**
    -   **Customer:** Receives booking details via WhatsApp.
    -   **Logistics:** Receives booking details and setup requirements via WhatsApp.
    -   **Admin:** Receives a confirmation via email.

-   **Invoice Generation:**
    -   **Customer:** Receives the invoice PDF via email.

## 5. Future Considerations

### 5.1. Customer-Facing Portal

-   A web portal for customers to browse resources, check availability, and make bookings themselves.
-   Integration with a payment gateway to create a paywall for online bookings.

### 5.2. Advanced Reporting

-   A reporting module for admins to generate reports on revenue, resource utilization, and customer activity.

### 5.3. Meeting Room Details

-   Finalize and implement the specific configurable attributes for Meeting Rooms. 