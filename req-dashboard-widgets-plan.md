# Dashboard Widgets Plan

Based on the analysis of `prd.md` and `requirements.md`, here is a recommended list of widgets for the main dashboard page:

## 1. Upcoming Bookings
- **Description:** Displays a list or calendar of upcoming bookings with key details (date, customer, resource, status).
- **Rationale:** Allows admins and logistics staff to quickly see what events are scheduled and prepare accordingly.

## 2. Recent Invoices
- **Description:** Shows the most recently generated invoices, their amounts, and payment statuses.
- **Rationale:** Helps admins track financial activity and follow up on outstanding payments.

## 3. Resource Availability Calendar
- **Description:** Visual calendar or summary showing which resources (halls, rooms, desks, etc.) are available or booked.
- **Rationale:** Prevents double-booking and aids in resource planning.

## 4. Quick Actions
- **Description:** Buttons or links for common admin tasks (create booking, add customer, add resource, generate invoice).
- **Rationale:** Streamlines workflow by reducing navigation time for frequent actions.

## 5. Notifications Feed
- **Description:** List of recent system notifications (booking confirmations, invoice sent, etc.).
- **Rationale:** Keeps staff informed of important events and system activity.

## 6. Key Metrics Summary
- **Description:** At-a-glance stats such as total bookings this month, revenue, number of customers, resource utilization rate.
- **Rationale:** Provides a high-level overview of business performance and system usage.

## 7. Pending Actions/Tasks
- **Description:** Highlights items needing admin attention (unpaid invoices, pending bookings, incomplete customer info).
- **Rationale:** Ensures nothing falls through the cracks and improves operational efficiency.

## 8. Recent Customer Activity
- **Description:** Shows recent customer sign-ups, bookings, or updates.
- **Rationale:** Helps admins monitor customer engagement and follow up as needed.

## 9. Catering Orders Overview
- **Description:** Displays recent or upcoming catering orders linked to bookings.
- **Rationale:** Assists in coordinating catering logistics and vendor communication.

---

**Note:** The dashboard should be modular and allow for future widgets such as advanced reporting, customer portal stats, or meeting room details as requirements evolve. 