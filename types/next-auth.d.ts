import NextAuth from 'next-auth'

declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      name?: string | null
      username?: string | null
      email?: string | null
      image?: string | null
    }
  }

  interface User {
    id: string
    username?: string
    name?: string
    email?: string
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string
    username?: string
  }
}